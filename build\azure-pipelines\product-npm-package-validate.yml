trigger: none

pr:
  branches:
    include: ["main"]
  paths:
    include: ["package.json", "package-lock.json"]

variables:
  - name: NPM_REGISTRY
    value: "https://pkgs.dev.azure.com/monacotools/Monaco/_packaging/vscode/npm/registry/"
  - name: VSCODE_CIBUILD
    value: ${{ in(variables['Build.Reason'], 'IndividualCI', 'BatchedCI') }}
  - name: VSCODE_QUALITY
    value: oss

jobs:
  - ${{ if ne(variables['VSCODE_CIBUILD'], true) }}:
    - job: ValidateNpmPackage
      displayName: Valiate NPM package against Terrapin
      pool:
        name: 1es-ubuntu-22.04-x64
        os: linux
      timeoutInMinutes: 40000
      continueOnError: true
      variables:
        VSCODE_ARCH: x64
      steps:
        - task: NodeTool@0
          inputs:
            versionSource: fromFile
            versionFilePath: .nvmrc
            nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

        - script: node build/setup-npm-registry.js $NPM_REGISTRY
          condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
          displayName: Setup NPM Registry

        - script: |
            set -e
            # Set the private NPM registry to the global npmrc file
            # so that authentication works for subfolders like build/, remote/, extensions/ etc
            # which does not have their own .npmrc file
            echo "NPMRC Path: $(npm config get userconfig)"
            echo "NPM Registry: $(npm config get registry)"
            npm config set registry "$NPM_REGISTRY"
            echo "##vso[task.setvariable variable=NPMRC_PATH]$(npm config get userconfig)"
          condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
          displayName: Setup NPM

        - task: npmAuthenticate@0
          inputs:
            workingFile: $(NPMRC_PATH)
          condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
          displayName: Setup NPM Authentication

        - script: sudo apt update -y && sudo apt install -y build-essential pkg-config libx11-dev libx11-xcb-dev libxkbfile-dev libnotify-bin libkrb5-dev
          displayName: Install build tools
          condition: succeeded()

        - script: |
            set -e

            for attempt in {1..6}; do
              if [ $attempt -gt 1 ]; then
                echo "Attempt $attempt: Waiting for 1 hour before retrying..."
                sleep 3600
              fi

              echo "Attempt $attempt: Running npm ci"
              if npm i --ignore-scripts; then
                if node build/npm/postinstall.js; then
                  echo "npm i succeeded on attempt $attempt"
                  exit 0
                else
                  echo "node build/npm/postinstall.js failed on attempt $attempt"
                fi
              else
                echo "npm i failed on attempt $attempt"
              fi
            done

            echo "npm i failed after 6 attempts"
            exit 1
          env:
            npm_command: 'install --ignore-scripts'
            ELECTRON_SKIP_BINARY_DOWNLOAD: 1
            PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
            GITHUB_TOKEN: "$(github-distro-mixin-password)"
          displayName: Install dependencies with retries
          timeoutInMinutes: 400

        - script: .github/workflows/check-clean-git-state.sh
          displayName: Check clean git state
