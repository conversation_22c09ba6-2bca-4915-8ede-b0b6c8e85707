{% extends "base.html" %}

{% block title %}注册 - Python Web应用{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-4">
            <div class="card shadow">
                <div class="card-header text-center bg-success text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-user-plus"></i> 用户注册
                    </h4>
                </div>
                <div class="card-body">
                    <form method="POST">
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="fas fa-user"></i> 用户名
                            </label>
                            <input type="text" class="form-control" id="username" name="username" required>
                            <div class="form-text">用户名必须唯一</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope"></i> 邮箱
                            </label>
                            <input type="email" class="form-control" id="email" name="email" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock"></i> 密码
                            </label>
                            <input type="password" class="form-control" id="password" name="password" required>
                            <div class="form-text">密码至少6位字符</div>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-success">
                                <i class="fas fa-user-plus"></i> 注册
                            </button>
                        </div>
                    </form>
                </div>
                <div class="card-footer text-center">
                    <small class="text-muted">
                        已有账户？ 
                        <a href="{{ url_for('login') }}" class="text-decoration-none">
                            立即登录
                        </a>
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
