[{"identifier": {"id": "ms-python.debugpy", "uuid": "4bd5d2c9-9d65-401a-b0b2-7498d9f17615"}, "version": "2025.6.0", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/ms-python.debugpy-2025.6.0-linux-x64", "scheme": "file"}, "relativeLocation": "ms-python.debugpy-2025.6.0-linux-x64", "metadata": {"installedTimestamp": 1753968194056, "pinned": false, "source": "gallery", "id": "4bd5d2c9-9d65-401a-b0b2-7498d9f17615", "publisherId": "998b010b-e2af-44a5-a6cd-0b5fd3b9b6f8", "publisherDisplayName": "ms-python", "targetPlatform": "linux-x64", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "haloscript.astyle-lsp-vscode", "uuid": "fa56bcc0-cc69-43b9-b2b5-c00ba4a9cbd8"}, "version": "0.0.1", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/haloscript.astyle-lsp-vscode-0.0.1-universal", "scheme": "file"}, "relativeLocation": "haloscript.astyle-lsp-vscode-0.0.1-universal", "metadata": {"installedTimestamp": 1753968212558, "pinned": false, "source": "gallery", "id": "fa56bcc0-cc69-43b9-b2b5-c00ba4a9cbd8", "publisherId": "b0eeafd7-1e9d-45d3-8682-f1832a254ea4", "publisherDisplayName": "haloscript", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "danielpinto8zz6.c-cpp-project-generator", "uuid": "3b159107-**************-3fc6e6f277da"}, "version": "1.2.20", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/danielpinto8zz6.c-cpp-project-generator-1.2.20-universal", "scheme": "file"}, "relativeLocation": "danielpinto8zz6.c-cpp-project-generator-1.2.20-universal", "metadata": {"installedTimestamp": 1753968213364, "pinned": false, "source": "gallery", "id": "3b159107-**************-3fc6e6f277da", "publisherId": "cad497d5-586d-419d-9f40-f9d14950a3dd", "publisherDisplayName": "daniel<PERSON>to8zz6", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "kylinideteam.cmake-intellisence", "uuid": "b02fa3e5-06d4-4aad-8dbb-fe54a1cc58f9"}, "version": "0.3.5", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/kylinideteam.cmake-intellisence-0.3.5-universal", "scheme": "file"}, "relativeLocation": "kylinideteam.cmake-intellisence-0.3.5-universal", "metadata": {"installedTimestamp": 1753968216729, "pinned": false, "source": "gallery", "id": "b02fa3e5-06d4-4aad-8dbb-fe54a1cc58f9", "publisherId": "d7162f76-ba97-421e-ad7b-61d4e260c7d1", "publisherDisplayName": "KylinIdeTeam", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "ms-python.python", "uuid": "f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5"}, "version": "2025.4.0", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal", "scheme": "file"}, "relativeLocation": "ms-python.python-2025.4.0-universal", "metadata": {"installedTimestamp": 1753968194056, "pinned": false, "source": "gallery", "id": "f1f59ae4-9318-4f3c-a9b5-81b2eaa5f8a5", "publisherId": "998b010b-e2af-44a5-a6cd-0b5fd3b9b6f8", "publisherDisplayName": "ms-python", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "ms-dotnettools.vscode-dotnet-runtime", "uuid": "1aab81a1-b3d9-4aef-976b-577d5d90fe3f"}, "version": "2.3.7", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/ms-dotnettools.vscode-dotnet-runtime-2.3.7-universal", "scheme": "file"}, "relativeLocation": "ms-dotnettools.vscode-dotnet-runtime-2.3.7-universal", "metadata": {"installedTimestamp": 1753968222819, "pinned": false, "source": "gallery", "id": "1aab81a1-b3d9-4aef-976b-577d5d90fe3f", "publisherId": "d05e23de-3974-4ff0-8d47-23ee77830092", "publisherDisplayName": "ms-dot<PERSON><PERSON>s", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "kylinideteam.kylin-cmake-tools", "uuid": "b83703ef-ad83-4e08-8c01-463e1ea145ff"}, "version": "0.0.4", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/kylinideteam.kylin-cmake-tools-0.0.4-universal", "scheme": "file"}, "relativeLocation": "kylinideteam.kylin-cmake-tools-0.0.4-universal", "metadata": {"installedTimestamp": 1753968216728, "pinned": false, "source": "gallery", "id": "b83703ef-ad83-4e08-8c01-463e1ea145ff", "publisherId": "d7162f76-ba97-421e-ad7b-61d4e260c7d1", "publisherDisplayName": "KylinIdeTeam", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "kylinideteam.cppdebug", "uuid": "990e1ff7-cc55-4a01-ae62-39bb4ca3136e"}, "version": "0.0.9", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/kylinideteam.cppdebug-0.0.9-linux-x64", "scheme": "file"}, "relativeLocation": "kylinideteam.cppdebug-0.0.9-linux-x64", "metadata": {"installedTimestamp": 1753968216729, "pinned": false, "source": "gallery", "id": "990e1ff7-cc55-4a01-ae62-39bb4ca3136e", "publisherId": "d7162f76-ba97-421e-ad7b-61d4e260c7d1", "publisherDisplayName": "KylinIdeTeam", "targetPlatform": "linux-x64", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "muhammad-sammy.csharp", "uuid": "57b4ceec-c35a-4a96-ab38-23c6338df406"}, "version": "2.84.19", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/muhammad-sammy.csharp-2.84.19-universal", "scheme": "file"}, "relativeLocation": "muh<PERSON><PERSON>-sammy.csharp-2.84.19-universal", "metadata": {"installedTimestamp": 1753968222818, "pinned": false, "source": "gallery", "id": "57b4ceec-c35a-4a96-ab38-23c6338df406", "publisherId": "cc79407d-f6d2-4535-9bd9-920d8557403d", "publisherDisplayName": "muham<PERSON>-sammy", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "dbaeumer.vscode-eslint", "uuid": "583b2b34-2c1e-4634-8c0b-0b82e283ea3a"}, "version": "3.0.10", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/dbaeumer.vscode-eslint-3.0.10-universal", "scheme": "file"}, "relativeLocation": "dbaeumer.vscode-eslint-3.0.10-universal", "metadata": {"installedTimestamp": 1753968418361, "source": "gallery", "id": "583b2b34-2c1e-4634-8c0b-0b82e283ea3a", "publisherId": "29859a75-d81b-4f0e-8578-2c80ecee6f99", "publisherDisplayName": "d<PERSON><PERSON><PERSON>", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "editorconfig.editorconfig", "uuid": "f60a60a6-95ba-42d4-b41c-3d24c1b89588"}, "version": "0.17.4", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/editorconfig.editorconfig-0.17.4-universal", "scheme": "file"}, "relativeLocation": "editorconfig.editorconfig-0.17.4-universal", "metadata": {"installedTimestamp": 1753968418365, "source": "gallery", "id": "f60a60a6-95ba-42d4-b41c-3d24c1b89588", "publisherId": "1ed869e4-8588-4af4-a51e-9c1c86b034b9", "publisherDisplayName": "EditorConfig", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "ms-vscode.vscode-github-issue-notebooks", "uuid": "3be1cece-c179-4cd6-b59b-3bae29e1a166"}, "version": "0.0.133", "location": {"$mid": 1, "path": "/home/<USER>/.openvscode-server/extensions/ms-vscode.vscode-github-issue-notebooks-0.0.133-universal", "scheme": "file"}, "relativeLocation": "ms-vscode.vscode-github-issue-notebooks-0.0.133-universal", "metadata": {"installedTimestamp": 1753968418363, "source": "gallery", "id": "3be1cece-c179-4cd6-b59b-3bae29e1a166", "publisherId": "5f5636e7-69ed-4afe-b5d6-8d231fb3d3ee", "publisherDisplayName": "ms-vscode", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}, {"identifier": {"id": "github.vscode-pull-request-github"}, "version": "0.114.3", "location": {"$mid": 1, "fsPath": "/home/<USER>/.openvscode-server/extensions/github.vscode-pull-request-github-0.114.3-universal", "external": "file:///home/<USER>/.openvscode-server/extensions/github.vscode-pull-request-github-0.114.3-universal", "path": "/home/<USER>/.openvscode-server/extensions/github.vscode-pull-request-github-0.114.3-universal", "scheme": "file"}, "relativeLocation": "github.vscode-pull-request-github-0.114.3-universal", "metadata": {"installedTimestamp": 1753968418366, "source": "gallery", "id": "69ddd764-339a-4ecc-97c1-9c4ece58e36d", "publisherId": "7c1c19cd-78eb-4dfb-8999-99caf7679002", "publisherDisplayName": "GitHub", "targetPlatform": "universal", "updated": false, "private": false, "isPreReleaseVersion": false, "hasPreReleaseVersion": false}}]