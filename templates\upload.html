{% extends "base.html" %}

{% block title %}文件上传 - Python Web应用{% endblock %}

{% block content %}
<div class="container">
    <h2 class="mb-4">
        <i class="fas fa-upload"></i> 文件上传
    </h2>

    <div class="row">
        <div class="col-md-8">
            <!-- 上传区域 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cloud-upload-alt"></i> 选择文件上传
                    </h5>
                </div>
                <div class="card-body">
                    <form method="POST" enctype="multipart/form-data" id="uploadForm">
                        <div class="upload-area" id="uploadArea">
                            <i class="fas fa-cloud-upload-alt fa-3x text-muted mb-3"></i>
                            <h5>拖拽文件到这里或点击选择</h5>
                            <p class="text-muted">支持所有文件类型，最大16MB</p>
                            <input type="file" name="file" id="fileInput" class="d-none" required>
                            <button type="button" class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                                <i class="fas fa-folder-open"></i> 选择文件
                            </button>
                        </div>
                        
                        <div id="fileInfo" class="mt-3" style="display: none;">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-file"></i> 选中的文件:</h6>
                                <p id="fileName" class="mb-1"></p>
                                <small id="fileSize" class="text-muted"></small>
                            </div>
                            <div class="d-grid">
                                <button type="submit" class="btn btn-success btn-lg">
                                    <i class="fas fa-upload"></i> 开始上传
                                </button>
                            </div>
                        </div>
                    </form>
                    
                    <!-- 上传进度 -->
                    <div id="uploadProgress" class="mt-3" style="display: none;">
                        <div class="progress">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%"></div>
                        </div>
                        <small class="text-muted">上传中...</small>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- 上传说明 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle"></i> 上传说明
                    </h5>
                </div>
                <div class="card-body">
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            支持所有文件类型
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            最大文件大小: 16MB
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            文件自动重命名保存
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-check text-success"></i>
                            支持拖拽上传
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-shield-alt text-primary"></i>
                            安全扫描和存储
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 文件类型示例 -->
            <div class="card mt-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-file-alt"></i> 支持的文件类型
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4 mb-3">
                            <i class="fas fa-file-image fa-2x text-primary"></i>
                            <small class="d-block">图片</small>
                        </div>
                        <div class="col-4 mb-3">
                            <i class="fas fa-file-pdf fa-2x text-danger"></i>
                            <small class="d-block">PDF</small>
                        </div>
                        <div class="col-4 mb-3">
                            <i class="fas fa-file-word fa-2x text-info"></i>
                            <small class="d-block">文档</small>
                        </div>
                        <div class="col-4 mb-3">
                            <i class="fas fa-file-excel fa-2x text-success"></i>
                            <small class="d-block">表格</small>
                        </div>
                        <div class="col-4 mb-3">
                            <i class="fas fa-file-video fa-2x text-warning"></i>
                            <small class="d-block">视频</small>
                        </div>
                        <div class="col-4 mb-3">
                            <i class="fas fa-file-audio fa-2x text-secondary"></i>
                            <small class="d-block">音频</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    const uploadArea = document.getElementById('uploadArea');
    const fileInput = document.getElementById('fileInput');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const uploadForm = document.getElementById('uploadForm');
    const uploadProgress = document.getElementById('uploadProgress');

    // 拖拽上传功能
    uploadArea.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#007bff';
        uploadArea.style.backgroundColor = '#f0f8ff';
    });

    uploadArea.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#ddd';
        uploadArea.style.backgroundColor = '#f9f9f9';
    });

    uploadArea.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadArea.style.borderColor = '#ddd';
        uploadArea.style.backgroundColor = '#f9f9f9';
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            showFileInfo(files[0]);
        }
    });

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        if (e.target.files.length > 0) {
            showFileInfo(e.target.files[0]);
        }
    });

    // 显示文件信息
    function showFileInfo(file) {
        fileName.textContent = file.name;
        fileSize.textContent = `大小: ${formatFileSize(file.size)}`;
        fileInfo.style.display = 'block';
    }

    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // 表单提交事件
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(uploadForm);
        const xhr = new XMLHttpRequest();
        
        // 显示进度条
        uploadProgress.style.display = 'block';
        const progressBar = uploadProgress.querySelector('.progress-bar');
        
        // 上传进度
        xhr.upload.addEventListener('progress', function(e) {
            if (e.lengthComputable) {
                const percentComplete = (e.loaded / e.total) * 100;
                progressBar.style.width = percentComplete + '%';
                progressBar.textContent = Math.round(percentComplete) + '%';
            }
        });
        
        // 上传完成
        xhr.addEventListener('load', function() {
            if (xhr.status === 200) {
                // 成功
                progressBar.classList.remove('progress-bar-striped', 'progress-bar-animated');
                progressBar.classList.add('bg-success');
                progressBar.textContent = '上传完成!';
                
                setTimeout(function() {
                    window.location.href = '{{ url_for("dashboard") }}';
                }, 1500);
            } else {
                // 失败
                progressBar.classList.add('bg-danger');
                progressBar.textContent = '上传失败!';
            }
        });
        
        // 发送请求
        xhr.open('POST', uploadForm.action);
        xhr.send(formData);
    });
</script>
{% endblock %}
