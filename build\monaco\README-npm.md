# monaco-editor-core

> This npm module is a building block for the [monaco-editor](https://www.npmjs.com/package/monaco-editor)
npm module and unless you are doing something special (e.g. authoring a monaco editor language that can be shipped
and consumed independently), it is best to consume the [monaco-editor](https://www.npmjs.com/package/monaco-editor) module
that contains this module and adds languages supports.

The Monaco Editor is the code editor that powers [VS Code](https://github.com/microsoft/vscode). Here is a good page describing some [editor features](https://code.visualstudio.com/docs/editor/editingevolved).

This npm module contains the core editor functionality, as it comes from the [vscode repository](https://github.com/microsoft/vscode).

## License

[MIT](https://github.com/microsoft/vscode/blob/main/LICENSE.txt)
