Initializing document watcher...
Document watcher initialized
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
workspace/hello_world.py: {"tabSize":4,"indentSize":"tabSize","insertSpaces":false,"cursorStyle":1,"lineNumbers":1}
