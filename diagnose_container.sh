#!/bin/bash

echo "=== Docker容器Python环境诊断 ==="
echo "诊断时间: $(date)"
echo ""

# 检查当前环境
echo "=== 环境信息 ==="
echo "当前用户: $(whoami)"
echo "当前目录: $(pwd)"
echo "主机名: $(hostname)"
echo "操作系统: $(uname -a)"
echo ""

# 检查是否在Docker容器中
echo "=== 容器检查 ==="
if [ -f /.dockerenv ]; then
    echo "✓ 确认在Docker容器中"
else
    echo "? 可能不在Docker容器中"
fi

if [ -f /proc/1/cgroup ]; then
    echo "容器信息:"
    grep docker /proc/1/cgroup 2>/dev/null || echo "  未检测到Docker标识"
fi
echo ""

# 检查Linux发行版
echo "=== 系统信息 ==="
if [ -f /etc/os-release ]; then
    echo "Linux发行版信息:"
    cat /etc/os-release | grep -E "^(NAME|VERSION|ID)="
else
    echo "无法获取发行版信息"
fi
echo ""

# 检查Python
echo "=== Python检查 ==="
echo "检查python命令:"
if command -v python >/dev/null 2>&1; then
    echo "✓ python: $(python --version 2>&1)"
else
    echo "✗ python命令不存在"
fi

echo "检查python3命令:"
if command -v python3 >/dev/null 2>&1; then
    echo "✓ python3: $(python3 --version 2>&1)"
else
    echo "✗ python3命令不存在"
fi

echo "检查pip命令:"
if command -v pip >/dev/null 2>&1; then
    echo "✓ pip: $(pip --version 2>&1)"
else
    echo "✗ pip命令不存在"
fi

echo "检查pip3命令:"
if command -v pip3 >/dev/null 2>&1; then
    echo "✓ pip3: $(pip3 --version 2>&1)"
else
    echo "✗ pip3命令不存在"
fi
echo ""

# 检查包管理器
echo "=== 包管理器检查 ==="
if command -v apt-get >/dev/null 2>&1; then
    echo "✓ apt-get可用 (Debian/Ubuntu)"
    PKG_MANAGER="apt-get"
elif command -v yum >/dev/null 2>&1; then
    echo "✓ yum可用 (CentOS/RHEL)"
    PKG_MANAGER="yum"
elif command -v dnf >/dev/null 2>&1; then
    echo "✓ dnf可用 (Fedora)"
    PKG_MANAGER="dnf"
elif command -v apk >/dev/null 2>&1; then
    echo "✓ apk可用 (Alpine)"
    PKG_MANAGER="apk"
else
    echo "✗ 未找到常见的包管理器"
    PKG_MANAGER=""
fi
echo ""

# 检查sudo权限
echo "=== 权限检查 ==="
if sudo -n true 2>/dev/null; then
    echo "✓ 有sudo权限"
    HAS_SUDO=true
else
    echo "✗ 无sudo权限或需要密码"
    HAS_SUDO=false
fi
echo ""

# 检查PATH
echo "=== PATH环境变量 ==="
echo "当前PATH:"
echo "$PATH" | tr ':' '\n' | sed 's/^/  /'
echo ""

# 提供解决方案
echo "=== 解决方案建议 ==="

if command -v python >/dev/null 2>&1; then
    echo "✓ Python已可用，可以直接运行:"
    echo "  python hello_world.py"
elif command -v python3 >/dev/null 2>&1; then
    echo "! 只有python3可用，建议创建别名:"
    echo "  alias python=python3"
    echo "  echo 'alias python=python3' >> ~/.bashrc"
    echo "  然后运行: python hello_world.py"
else
    echo "! 需要安装Python，建议的安装命令:"
    
    if [ "$PKG_MANAGER" = "apt-get" ] && [ "$HAS_SUDO" = true ]; then
        echo "  sudo apt-get update"
        echo "  sudo apt-get install -y python3 python3-pip"
        echo "  sudo ln -s /usr/bin/python3 /usr/bin/python"
    elif [ "$PKG_MANAGER" = "yum" ] && [ "$HAS_SUDO" = true ]; then
        echo "  sudo yum install -y python3 python3-pip"
        echo "  sudo ln -s /usr/bin/python3 /usr/bin/python"
    elif [ "$PKG_MANAGER" = "dnf" ] && [ "$HAS_SUDO" = true ]; then
        echo "  sudo dnf install -y python3 python3-pip"
        echo "  sudo ln -s /usr/bin/python3 /usr/bin/python"
    elif [ "$PKG_MANAGER" = "apk" ] && [ "$HAS_SUDO" = true ]; then
        echo "  sudo apk add python3 py3-pip"
        echo "  sudo ln -s /usr/bin/python3 /usr/bin/python"
    else
        echo "  请联系系统管理员安装Python"
        echo "  或者尝试用户级安装方案"
    fi
fi

echo ""
echo "=== 测试文件 ==="
if [ -f "hello_world.py" ]; then
    echo "✓ 找到hello_world.py文件"
    echo "文件内容:"
    cat hello_world.py | sed 's/^/  /'
    
    # 尝试运行
    echo ""
    echo "尝试运行测试:"
    if command -v python >/dev/null 2>&1; then
        echo "使用python命令:"
        python hello_world.py 2>&1 | sed 's/^/  /'
    elif command -v python3 >/dev/null 2>&1; then
        echo "使用python3命令:"
        python3 hello_world.py 2>&1 | sed 's/^/  /'
    else
        echo "  无法运行 - 没有可用的Python命令"
    fi
else
    echo "✗ 未找到hello_world.py文件"
fi

echo ""
echo "=== 诊断完成 ==="
echo "请根据上述建议安装或配置Python环境"
