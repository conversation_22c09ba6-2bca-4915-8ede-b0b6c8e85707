name: Code OSS (node_modules)

on:
  push:
    branches:
      - main

permissions: {}

jobs:
  compile:
    name: Compile
    runs-on: [ self-hosted, 1ES.Pool=1es-vscode-oss-ubuntu-22.04-x64 ]
    steps:
      - name: Checkout microsoft/vscode
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
        env:
          NODEJS_ORG_MIRROR: https://github.com/joaomoreno/node-mirror/releases/download

      - name: Prepare node_modules cache key
        run: mkdir -p .build && node build/azure-pipelines/common/computeNodeModulesCacheKey.js compile $(node -p process.arch) > .build/packagelockhash

      - name: Restore node_modules cache
        id: cache-node-modules
        uses: actions/cache@v4
        with:
          path: .build/node_modules_cache
          key: "node_modules-compile-${{ hashFiles('.build/packagelockhash') }}"

      - name: Extract node_modules cache
        if: steps.cache-node-modules.outputs.cache-hit == 'true'
        run: tar -xzf .build/node_modules_cache/cache.tgz

      - name: Install build tools
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: sudo apt update -y && sudo apt install -y build-essential pkg-config libx11-dev libx11-xcb-dev libxkbfile-dev libnotify-bin libkrb5-dev

      - name: Install dependencies
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: |
          set -e

          for i in {1..5}; do # try 5 times
            npm ci && break
            if [ $i -eq 5 ]; then
              echo "Npm install failed too many times" >&2
              exit 1
            fi
            echo "Npm install failed $i, trying again..."
          done
        env:
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          GITHUB_TOKEN: ${{ secrets.VSCODE_OSS }}

      - name: Create node_modules archive
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: |
          set -e
          node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt
          mkdir -p .build/node_modules_cache
          tar -czf .build/node_modules_cache/cache.tgz --files-from .build/node_modules_list.txt

      - name: Prepare built-in extensions cache key
        run: |
          set -e
          mkdir -p .build
          node build/azure-pipelines/common/computeBuiltInDepsCacheKey.js > .build/builtindepshash

      - name: Restore built-in extensions cache
        id: cache-builtin-extensions
        uses: actions/cache@v4
        with:
          enableCrossOsArchive: true
          path: .build/builtInExtensions
          key: "builtin-extensions-${{ hashFiles('.build/builtindepshash') }}"

      - name: Download built-in extensions
        if: steps.cache-builtin-extensions.outputs.cache-hit != 'true'
        run: node build/lib/builtInExtensions.js
        env:
          GITHUB_TOKEN: ${{ secrets.VSCODE_OSS }}

  linux:
    name: Linux
    runs-on: [ self-hosted, 1ES.Pool=1es-vscode-oss-ubuntu-22.04-x64 ]
    env:
      NPM_ARCH: x64
      VSCODE_ARCH: x64
    steps:
      - name: Checkout microsoft/vscode
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
        env:
          NODEJS_ORG_MIRROR: https://github.com/joaomoreno/node-mirror/releases/download

      - name: Prepare node_modules cache key
        run: mkdir -p .build && node build/azure-pipelines/common/computeNodeModulesCacheKey.js linux $VSCODE_ARCH $(node -p process.arch) > .build/packagelockhash

      - name: Restore node_modules cache
        id: cache-node-modules
        uses: actions/cache@v4
        with:
          path: .build/node_modules_cache
          key: "node_modules-linux-${{ hashFiles('.build/packagelockhash') }}"

      - name: Install build dependencies
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        working-directory: build
        run: |
          set -e

          for i in {1..5}; do # try 5 times
            npm ci && break
            if [ $i -eq 5 ]; then
              echo "Npm install failed too many times" >&2
              exit 1
            fi
            echo "Npm install failed $i, trying again..."
          done
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Install dependencies
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: |
          set -e

          source ./build/azure-pipelines/linux/setup-env.sh

          for i in {1..5}; do # try 5 times
            npm ci && break
            if [ $i -eq 5 ]; then
              echo "Npm install failed too many times" >&2
              exit 1
            fi
            echo "Npm install failed $i, trying again..."
          done
        env:
          npm_config_arch: ${{ env.NPM_ARCH }}
          VSCODE_ARCH: ${{ env.VSCODE_ARCH }}
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          GITHUB_TOKEN: ${{ secrets.VSCODE_OSS }}

      - name: Create node_modules archive
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: |
          set -e
          node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt
          mkdir -p .build/node_modules_cache
          tar -czf .build/node_modules_cache/cache.tgz --files-from .build/node_modules_list.txt

  macOS:
    name: macOS
    runs-on: macos-14-xlarge
    env:
      NPM_ARCH: arm64
      VSCODE_ARCH: arm64
    steps:
      - name: Checkout microsoft/vscode
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
        env:
          NODEJS_ORG_MIRROR: https://github.com/joaomoreno/node-mirror/releases/download

      - name: Prepare node_modules cache key
        run: mkdir -p .build && node build/azure-pipelines/common/computeNodeModulesCacheKey.js darwin $VSCODE_ARCH $(node -p process.arch) > .build/packagelockhash

      - name: Restore node_modules cache
        id: cache-node-modules
        uses: actions/cache@v4
        with:
          path: .build/node_modules_cache
          key: "node_modules-macos-${{ hashFiles('.build/packagelockhash') }}"

      - name: Install dependencies
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: |
          set -e
          c++ --version
          xcode-select -print-path
          python3 -m pip install --break-system-packages setuptools

          for i in {1..5}; do # try 5 times
            npm ci && break
            if [ $i -eq 5 ]; then
              echo "Npm install failed too many times" >&2
              exit 1
            fi
            echo "Npm install failed $i, trying again..."
          done
        env:
          npm_config_arch: ${{ env.NPM_ARCH }}
          VSCODE_ARCH: ${{ env.VSCODE_ARCH }}
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          GITHUB_TOKEN: ${{ secrets.VSCODE_OSS }}
          # Avoid using dlopen to load Kerberos on macOS which can cause missing libraries
          # https://github.com/mongodb-js/kerberos/commit/04044d2814ad1d01e77f1ce87f26b03d86692cf2
          # flipped the default to support legacy linux distros which shouldn't happen
          # on macOS.
          GYP_DEFINES: "kerberos_use_rtld=false"

      - name: Create node_modules archive
        if: steps.cache-node-modules.outputs.cache-hit != 'true'
        run: |
          set -e
          node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt
          mkdir -p .build/node_modules_cache
          tar -czf .build/node_modules_cache/cache.tgz --files-from .build/node_modules_list.txt

  windows:
    name: Windows
    runs-on: [ self-hosted, 1ES.Pool=1es-vscode-oss-windows-2022-x64 ]
    env:
      NPM_ARCH: x64
      VSCODE_ARCH: x64
    steps:
      - name: Checkout microsoft/vscode
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version-file: .nvmrc
        env:
          NODEJS_ORG_MIRROR: https://github.com/joaomoreno/node-mirror/releases/download

      - name: Prepare node_modules cache key
        shell: pwsh
        run: |
          mkdir .build -ea 0
          node build/azure-pipelines/common/computeNodeModulesCacheKey.js win32 ${{ env.VSCODE_ARCH }} $(node -p process.arch) > .build/packagelockhash

      - name: Restore node_modules cache
        uses: actions/cache@v4
        id: node-modules-cache
        with:
          path: .build/node_modules_cache
          key: "node_modules-windows-${{ hashFiles('.build/packagelockhash') }}"

      - name: Install dependencies
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        shell: pwsh
        run: |
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"

          for ($i = 1; $i -le 5; $i++) {
            try {
              exec { npm ci }
              break
            }
            catch {
              if ($i -eq 5) {
                Write-Error "npm ci failed after 5 attempts"
                throw
              }
              Write-Host "npm ci failed attempt $i, retrying..."
              Start-Sleep -Seconds 2
            }
          }
        env:
          npm_config_arch: ${{ env.NPM_ARCH }}
          npm_config_foreground_scripts: "true"
          VSCODE_ARCH: ${{ env.VSCODE_ARCH }}
          ELECTRON_SKIP_BINARY_DOWNLOAD: 1
          PLAYWRIGHT_SKIP_BROWSER_DOWNLOAD: 1
          GITHUB_TOKEN: ${{ secrets.VSCODE_OSS }}

      - name: Create node_modules archive
        if: steps.node-modules-cache.outputs.cache-hit != 'true'
        shell: pwsh
        run: |
          . build/azure-pipelines/win32/exec.ps1
          $ErrorActionPreference = "Stop"
          exec { node build/azure-pipelines/common/listNodeModules.js .build/node_modules_list.txt }
          exec { mkdir -Force .build/node_modules_cache }
          exec { 7z.exe a .build/node_modules_cache/cache.7z -mx3 `@.build/node_modules_list.txt }
