[{"kind": 1, "language": "markdown", "value": "### Bug Verification Queries\n\nBefore shipping we want to verify _all_ bugs. That means when a bug is fixed we check that the fix actually works. It's always best to start with bugs that you have filed and the proceed with bugs that have been filed from users outside the development team. "}, {"kind": 1, "language": "markdown", "value": "#### Config: update list of `repos` and the `milestone`"}, {"kind": 2, "language": "github-issues", "value": "$repos=repo:microsoft/lsprotocol repo:microsoft/monaco-editor repo:microsoft/vscode repo:microsoft/vscode-anycode repo:microsoft/vscode-autopep8 repo:microsoft/vscode-black-formatter repo:microsoft/vscode-copilot repo:microsoft/vscode-copilot-release repo:microsoft/vscode-dev repo:microsoft/vscode-dev-chrome-launcher repo:microsoft/vscode-emmet-helper repo:microsoft/vscode-extension-telemetry repo:microsoft/vscode-flake8 repo:microsoft/vscode-github-issue-notebooks repo:microsoft/vscode-hexeditor repo:microsoft/vscode-internalbacklog repo:microsoft/vscode-isort repo:microsoft/vscode-js-debug repo:microsoft/vscode-jupyter repo:microsoft/vscode-jupyter-internal repo:microsoft/vscode-l10n repo:microsoft/vscode-livepreview repo:microsoft/vscode-markdown-languageservice repo:microsoft/vscode-markdown-tm-grammar repo:microsoft/vscode-mypy repo:microsoft/vscode-pull-request-github repo:microsoft/vscode-pylint repo:microsoft/vscode-python repo:microsoft/vscode-python-debugger repo:microsoft/vscode-python-tools-extension-template repo:microsoft/vscode-references-view repo:microsoft/vscode-remote-release repo:microsoft/vscode-remote-repositories-github repo:microsoft/vscode-remote-tunnels repo:microsoft/vscode-remotehub repo:microsoft/vscode-settings-sync-server repo:microsoft/vscode-unpkg repo:microsoft/vscode-vsce\n$milestone=milestone:\"November 2023\"\n$closedRecently=closed:>2023-09-29"}, {"kind": 1, "language": "markdown", "value": "### Bugs You Filed"}, {"kind": 2, "language": "github-issues", "value": "$repos $milestone is:closed reason:completed -assignee:@me label:bug -label:verified -label:*duplicate author:@me"}, {"kind": 1, "language": "markdown", "value": "### Bugs From Outside"}, {"kind": 2, "language": "github-issues", "value": "$repos $milestone is:closed reason:completed -assignee:@me label:bug -label:verified -label:*duplicate -author:@me -assignee:@me label:bug -label:verified -author:@me -author:aeschli -author:alexdima -author:alexr00 -author:bpasero -author:ch<PERSON><PERSON><PERSON> -author:chrmarti -author:connor4312 -author:d<PERSON><PERSON><PERSON> -author:deepak1556 -author:eamodio -author:egamma -author:gregvanl -author:isidorn -author:<PERSON> -author:joaomoreno -author:jrieken -author:lramos15 -author:lszomoru -author:meganrogge -author:misolori -author:mjbvz -author:rebornix -author:<PERSON><PERSON><PERSON><PERSON><PERSON> -author:rob<PERSON><PERSON> -author:sana-ajani -author:sandy081 -author:sbatten -author:Tyriar -author:weinand -author:rzhao271 -author:k<PERSON><PERSON><PERSON> -author:<PERSON><PERSON><PERSON><PERSON> -author:bamurtaugh -author:hediet -author:joy<PERSON><PERSON><PERSON> -author:r<PERSON><PERSON><PERSON>"}, {"kind": 1, "language": "markdown", "value": "### All"}, {"kind": 2, "language": "github-issues", "value": "$repos $milestone is:closed reason:completed -assignee:@me label:bug -label:verified -label:*duplicate"}, {"kind": 1, "language": "markdown", "value": "### Issues recently closed via PR without a milestone"}, {"kind": 2, "language": "github-issues", "value": "$repos is:closed linked:pr $closedRecently no:milestone -label:verified -label:*duplicate"}]