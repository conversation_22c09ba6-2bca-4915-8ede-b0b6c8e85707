2025-07-31 13:32:56.821 [warning] Dir "/home/<USER>/.pixi/envs" is not watchable (directory does not exist)
2025-07-31 13:32:56.822 [info] Starting Environment refresh
2025-07-31 13:32:56.822 [info] Searching for interpreters in posix paths locator
2025-07-31 13:32:56.822 [info] Searching for pyenv environments
2025-07-31 13:32:56.822 [info] Searching for conda environments
2025-07-31 13:32:56.822 [info] Searching for global virtual environments
2025-07-31 13:32:56.822 [info] Searching for custom virtual environments
2025-07-31 13:32:56.823 [info] pyenv is not installed
2025-07-31 13:32:56.823 [info] Finished searching for pyenv environments: 53 milliseconds
2025-07-31 13:32:56.824 [info] Finished searching for custom virtual envs: 52 milliseconds
2025-07-31 13:32:56.824 [info] > conda info --json
2025-07-31 13:32:56.824 [info] > hatch env show --json
2025-07-31 13:32:56.824 [info] cwd: .
2025-07-31 13:32:56.824 [info] Finished searching for global virtual envs: 58 milliseconds
2025-07-31 13:32:56.863 [info] > C:\Python313\python.exe -c "import sys;print(sys.executable)"
2025-07-31 13:32:56.865 [error] [Error: Command failed: C:\Python313\python.exe -c "import sys;print(sys.executable)"
/bin/sh: 1: C:Python313python.exe: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at Socket.<anonymous> (node:internal/child_process:456:11)
	at Socket.emit (node:events:518:28)
	at Pipe.<anonymous> (node:net:351:12)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: 'C:\\Python313\\python.exe -c "import sys;print(sys.executable)"'
}
2025-07-31 13:32:56.868 [info] Finished searching for interpreters in posix paths locator: 111 milliseconds
2025-07-31 13:32:56.871 [info] > C:\Python313\python.exe -I ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 13:32:56.898 [error] [Error: Command failed: C:\Python313\python.exe -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
/bin/sh: 1: C:Python313python.exe: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at Socket.<anonymous> (node:internal/child_process:456:11)
	at Socket.emit (node:events:518:28)
	at Pipe.<anonymous> (node:net:351:12)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: 'C:\\Python313\\python.exe -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py'
}
2025-07-31 13:32:57.684 [info] Environments refresh paths discovered (event): 926 milliseconds
2025-07-31 13:32:57.685 [info] Environments refresh paths discovered: 927 milliseconds
