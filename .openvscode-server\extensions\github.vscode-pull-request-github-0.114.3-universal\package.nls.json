{"displayName": "GitHub Pull Requests", "description": "Pull Request and Issue Provider for GitHub", "githubPullRequests.pullRequestDescription.description": "The description used when creating pull requests.", "githubPullRequests.pullRequestDescription.template": "Use a pull request template and commit description, or just use the commit description if no templates were found", "githubPullRequests.pullRequestDescription.commit": "Use the latest commit message only", "githubPullRequests.pullRequestDescription.none": "Do not have a default description", "githubPullRequests.pullRequestDescription.copilot": "Generate a pull request title and description from GitHub Copilot. Requires that the GitHub Copilot extension is installed and authenticated. Will fall back to `commit` if Copilot is not set up.", "githubPullRequests.defaultCreateOption.description": "The create option that the \"Create\" button will default to when creating a pull request.", "githubPullRequests.defaultCreateOption.lastUsed": "The most recently used create option.", "githubPullRequests.defaultCreateOption.create": "The pull request will be created.", "githubPullRequests.defaultCreateOption.createDraft": "The pull request will be created as a draft.", "githubPullRequests.defaultCreateOption.createAutoMerge": "The pull request will be created with auto-merge enabled. The merge method selected will be the default for the repo or the value of `githubPullRequests.defaultMergeMethod` if set.", "githubPullRequests.createDraft": "Whether the \"Draft\" checkbox will be checked by default when creating a pull request.", "githubPullRequests.logLevel.description": "Logging for GitHub Pull Request extension. The log is emitted to the output channel named as GitHub Pull Request.", "githubPullRequests.logLevel.markdownDeprecationMessage": {"message": "Log level is now controlled by the [Developer: Set Log Level...](command:workbench.action.setLogLevel) command. You can set the log level for the current session and also the default log level from there.", "comment": ["Do not translate what's inside of (...). It is link syntax.", "{Locked='](command:workbench.action.setLogLevel)'}"]}, "githubPullRequests.codingAgent.description": "Enables integration with the asynchronous Copilot coding agent. The '#copilotCodingAgent' tool will be available in agent mode when this setting is enabled.", "githubPullRequests.codingAgent.uiIntegration.description": "Enables UI integration within VS Code to create new coding agent sessions.", "githubPullRequests.codingAgent.autoCommitAndPush.description": "Allow automatic git operations (commit, push) to be performed when starting a coding agent session", "githubPullRequests.remotes.markdownDescription": "List of remotes, by name, to fetch pull requests from.", "githubPullRequests.queries.markdownDescription": "Specifies what queries should be used in the GitHub Pull Requests tree. All queries are made against **the currently opened repos**. Each query object has a `label` that will be shown in the tree and a search `query` using [GitHub search syntax](https://help.github.com/en/articles/understanding-the-search-syntax). The following variables can be used: \n - `${user}` will resolve to the currently logged in user \n - `${owner}` will resolve to the owner of the current repository, ex. `microsoft` in `microsoft/vscode` \n - `${repository}` will resolve to the repository name, ex. `vscode` in `microsoft/vscode` \n - `${today-Nd}`, where `N` is the number of days ago, will resolve to a date, ex. `2025-01-04`. \n\n By default these queries define the categories \"Waiting For My Review\", \"Assigned To Me\" and \"Created By Me\". If you want to preserve these, make sure they are still in the array when you modify the setting.", "githubPullRequests.queries.label.description": "The label to display for the query in the Pull Requests tree", "githubPullRequests.queries.query.description": "The query used for searching pull requests.", "githubPullRequests.queries.copilotOnMyBehalf": "Copilot on My Behalf", "githubPullRequests.queries.waitingForMyReview": "Waiting For My Review", "githubPullRequests.queries.assignedToMe": "Assigned To Me", "githubPullRequests.queries.createdByMe": "Created By Me", "githubPullRequests.defaultMergeMethod.description": "The method to use when merging pull requests.", "githubPullRequests.notifications.description": "If GitHub notifications should be shown to the user.", "githubPullRequests.fileListLayout.description": "The layout to use when displaying changed files list.", "githubPullRequests.defaultDeletionMethod.selectLocalBranch.description": "When true, the option to delete the local branch will be selected by default when deleting a branch from a pull request.", "githubPullRequests.defaultDeletionMethod.selectRemote.description": "When true, the option to delete the remote will be selected by default when deleting a branch from a pull request.", "githubPullRequests.terminalLinksHandler.description": "Default handler for terminal links.", "githubPullRequests.terminalLinksHandler.github": "Create the pull request on GitHub", "githubPullRequests.terminalLinksHandler.vscode": "Create the pull request in VS Code", "githubPullRequests.terminalLinksHandler.ask": "Ask which method to use", "githubPullRequests.createOnPublishBranch.description": "Create a pull request when a branch is published.", "githubPullRequests.createOnPublishBranch.never": "Never create a pull request when a branch is published.", "githubPullRequests.createOnPublishBranch.ask": "Ask if you want to create a pull request when a branch is published.", "githubPullRequests.commentExpandState.description": "Controls whether comments are expanded when a document with comments is opened. Requires a reload to take effect for comments that have already been added.", "githubPullRequests.commentExpandState.expandUnresolved": "All unresolved comments will be expanded.", "githubPullRequests.commentExpandState.collapseAll": "All comments will be collapsed", "githubPullRequests.useReviewMode.description": "Choose which pull request states will use review mode. \"Open\" pull requests will always use review mode. Setting to \"auto\" will use review mode for open, closed, and merged pull requests in web, but only open pull requests on desktop.", "githubPullRequests.useReviewMode.merged": "Use review mode for merged pull requests.", "githubPullRequests.useReviewMode.closed": "Use review mode for closed pull requests. Merged pull requests are not considered \"closed\".", "githubPullRequests.assignCreated.description": {"message": "All pull requests created with this extension will be assigned to this user. To assign to yourself, use the '${user}' variable.", "comment": ["{Locked='${user}'}", "Do not translate what's inside of the '${..}'. It is an internal syntax for the extension"]}, "githubPullRequests.pushBranch.description": "Push the \"from\" branch when creating a PR and the \"from\" branch is not available on the remote.", "githubPullRequests.pushBranch.prompt": "Prompt to push the branch when creating a PR and the \"from\" branch is not available on the remote.", "githubPullRequests.pushBranch.always": "Always push the branch when creating a PR and the \"from\" branch is not available on the remote.", "githubPullRequests.pullBranch.description": "Pull changes from the remote when a PR branch is checked out locally. Changes are detected when the PR is manually refreshed and during periodic background updates.", "githubPullRequests.pullBranch.prompt": "Prompt to pull a PR branch when changes are detected in the PR.", "githubPullRequests.pullBranch.never": "Never pull a PR branch when changes are detected in the PR.", "githubPullRequests.pullBranch.always": "Always pull a PR branch when changes are detected in the PR. When `\"git.autoStash\": true` this will instead `prompt` to prevent unexpected file changes.", "githubPullRequests.allowFetch.description": "Allows `git fetch` to be run for checked-out pull request branches when checking for updates to the pull request.", "githubPullRequests.ignoredPullRequestBranches.description": "Prevents branches that are associated with a pull request from being automatically detected. This will prevent review mode from being entered on these branches.", "githubPullRequests.ignoredPullRequestBranches.items": "Branch name", "githubPullRequests.neverIgnoreDefaultBranch.description": "Never offer to ignore a pull request associated with the default branch of a repository.", "githubPullRequests.overrideDefaultBranch.description": "The default branch for a repository is set on github.com. With this setting, you can override that default with another branch.", "githubPullRequests.postCreate.description": "The action to take after creating a pull request.", "githubPullRequests.postCreate.none": "No action", "githubPullRequests.postCreate.openOverview": "Open the overview page of the pull request", "githubPullRequests.postCreate.checkoutDefaultBranch": "Checkout the default branch of the repository", "githubPullRequests.postCreate.checkoutDefaultBranchAndShow": "Checkout the default branch of the repository and show the pull request in the Pull Requests view", "githubPullRequests.postCreate.checkoutDefaultBranchAndCopy": "Checkout the default branch of the repository and copy a link to the pull request to the clipboard", "githubPullRequests.defaultCommentType.description": "The default comment type to use when submitting a comment and there is no active review", "githubPullRequests.defaultCommentType.single": "Submits the comment as a single comment that will be immediately visible to other users", "githubPullRequests.defaultCommentType.review": "Submits the comment as a review comment that will be visible to other users once the review is submitted", "githubPullRequests.setAutoMerge.description": "Checks the \"Auto-merge\" checkbox in the \"Create Pull Request\" view.", "githubPullRequests.pullPullRequestBranchBeforeCheckout.description": "Controls whether the pull request branch is pulled before checkout. Can also be set to additionally merge updates from the base branch.", "githubPullRequests.pullPullRequestBranchBeforeCheckout.never": "Never pull the pull request branch before checkout.", "githubPullRequests.pullPullRequestBranchBeforeCheckout.pull": "Pull the pull request branch before checkout.", "githubPullRequests.pullPullRequestBranchBeforeCheckout.pullAndMergeBase": "Pull the pull request branch before checkout, fetch the base branch, and merge the base branch into the pull request branch.", "githubPullRequests.pullPullRequestBranchBeforeCheckout.pullAndUpdateBase": "Pull the pull request branch before checkout, fetch the base branch, merge the base branch into the pull request branch, and finally push the pull request branch to the remote.", "githubPullRequests.upstreamRemote.description": "Controls whether an `upstream` remote is automatically added for forks", "githubPullRequests.upstreamRemote.add": "An `upstream` remote will be automatically added for forks", "githubPullRequests.upstreamRemote.never": "An `upstream` remote will never be automatically added for forks", "githubPullRequests.createDefaultBaseBranch.description": "Controls what the base branch picker defaults to when creating a pull request", "githubPullRequests.createDefaultBaseBranch.repositoryDefault": "The default branch of the repository", "githubPullRequests.createDefaultBaseBranch.createdFromBranch": "The branch that the current branch was created from, if known", "githubPullRequests.createDefaultBaseBranch.auto": "When the current repository is a fork, this will work like \"repositoryDefault\". Otherwise, it will work like \"createdFromBranch\".", "githubPullRequests.experimental.chat.description": "Enables the `@githubpr` Copilot chat participant in the chat view. `@githubpr` can help search for issues and pull requests, suggest fixes for issues, and summarize issues, pull requests, and notifications.", "githubPullRequests.experimental.notificationsView.description": "Enables the notifications view, which shows a list of your GitHub notifications. When combined with `#githubPullRequests.experimental.chat#`, you can have Copilot sort and summarize your notifications. View will not show in a Codespace accessed from the browser.", "githubPullRequests.experimental.notificationsMarkPullRequests.description": "Adds an action in the Notifications view to mark pull requests with no non-empty reviews, comments, or commits since you last viewed the pull request as read.", "githubPullRequests.experimental.useQuickChat.description": "Controls whether the Copilot \"Summarize\" commands in the Pull Requests, Issues, and Notifications views will use quick chat. Only has an effect if `#githubPullRequests.experimental.chat#` is enabled.", "githubPullRequests.webviewRefreshInterval.description": "The interval, in seconds, at which the pull request and issues webviews are refreshed when the webview is the active tab.", "githubIssues.ignoreMilestones.description": "An array of milestones titles to never show issues from.", "githubIssues.createIssueTriggers.description": "Strings that will cause the 'Create issue from comment' code action to show.", "githubIssues.createIssueTriggers.items": "String that enables the 'Create issue from comment' code action. Should not contain whitespace.", "githubIssues.createInsertFormat.description": "Controls whether an issue number (ex. #1234) or a full url (ex. https://github.com/owner/name/issues/1234) is inserted when the Create Issue code action is run.", "githubIssues.issueCompletions.enabled.description": "Controls whether completion suggestions are shown for issues.", "githubIssues.userCompletions.enabled.description": "Controls whether completion suggestions are shown for users.", "githubIssues.ignoreCompletionTrigger.description": "Languages that the '#' character should not be used to trigger issue completion suggestions.", "githubIssues.ignoreCompletionTrigger.items": "Language that issue completions should not trigger on '#'.", "githubIssues.ignoreUserCompletionTrigger.description": "Languages that the '@' character should not be used to trigger user completion suggestions.", "githubIssues.ignoreUserCompletionTrigger.items": "Language that user completions should not trigger on '@'.", "githubIssues.issueBranchTitle.markdownDescription": {"message": "Advanced settings for the name of the branch that is created when you start working on an issue. \n- `${user}` will be replace with the currently logged in username \n- `${issueNumber}` will be replaced with the current issue number \n- `${sanitizedIssueTitle}` will be replaced with the issue title, with all spaces and unsupported characters (https://git-scm.com/docs/git-check-ref-format) removed. For lowercase, use `${sanitizedLowercaseIssueTitle}` ", "comment": ["{Locked='${...}'}", "Do not translate what's inside of the '${..}'. It is an internal syntax for the extension"]}, "githubIssues.useBranchForIssues.markdownDescription": {"message": "Determines whether a branch should be checked out when working on an issue. To configure the name of the branch, set `#githubIssues.issueBranchTitle#`.", "comment": ["{Locked='`#githubIssues.issueBranchTitle#`'}", "Do not translate what's inside of the `...`. It is a setting id."]}, "githubIssues.useBranchForIssues.on": "A branch will always be checked out when you start working on an issue. If the branch doesn't exist, it will be created.", "githubIssues.useBranchForIssues.off": "A branch will not be created when you start working on an issue. If you have worked on an issue before and a branch was created for it, that same branch will be checked out.", "githubIssues.useBranchForIssues.prompt": "A prompt will show for setting the name of the branch that will be created and checked out.", "githubIssues.issueCompletionFormatScm.markdownDescription": {"message": "Sets the format of issue completions in the SCM inputbox. \n- `${user}` will be replace with the currently logged in username \n- `${issueNumber}` will be replaced with the current issue number \n- `${issueNumberLabel}` will be replaced with a label formatted as #number or owner/repository#number, depending on whether the issue is in the current repository", "comment": ["Do not translate what's inside of the ${...}. It is an internal syntax for the extension."]}, "githubIssues.workingIssueFormatScm.markdownDescription": {"message": "Sets the format of the commit message that is set in the SCM inputbox when you **Start Working on an Issue**. Defaults to `${issueTitle} \nFixes ${issueNumberLabel}`", "comment": ["Do not translate what's inside of the ${...}. It is an internal syntax for the extension."]}, "githubIssues.queries.markdownDescription": "Specifies what queries should be used in the GitHub issues tree using [GitHub search syntax](https://help.github.com/en/articles/understanding-the-search-syntax) with variables. The first query listed will be expanded in the Issues view. The \"default\" query includes issues assigned to you by Milestone. If you want to preserve these, make sure they are still in the array when you modify the setting.", "githubIssues.queries.label": "The label to display for the query in the Issues tree.", "githubIssues.queries.query": "The search query using [GitHub search syntax](https://help.github.com/en/articles/understanding-the-search-syntax) with variables. The variable `${user}` can be used to specify the logged in user within a search. `${owner}` and `${repository}` can be used to specify the repository by using `repo:${owner}/${repository}`.", "githubIssues.queries.groupBy": "The categories to group issues by when displaying them, in the order in which they should be grouped", "githubIssues.queries.groupBy.repository": "Group issues by their repository.", "githubIssues.queries.groupBy.milestone": "Group issues by their milestone.", "githubIssues.queries.default.myIssues": "My Issues", "githubIssues.queries.default.createdIssues": "Created Issues", "githubIssues.queries.default.recentIssues": "Recent Issues", "githubIssues.assignWhenWorking.description": "Assigns the issue you're working on to you. Only applies when the issue you're working on is in a repo you currently have open.", "githubPullRequests.focusedMode.description": "The layout to use when a pull request is checked out. Set to false to prevent layout changes.", "githubPullRequests.focusedMode.firstDiff": "Show the first diff in the pull request. If there are no changes, show the overview.", "githubPullRequests.focusedMode.overview": "Show the overview of the pull request.", "githubPullRequests.focusedMode.multiDiff": "Show all diffs in the pull request. If there are no changes, show the overview.", "githubPullRequests.focusedMode.false": "Do not change the layout.", "githubPullRequests.showPullRequestNumberInTree.description": "Shows the pull request number in the tree view.", "githubPullRequests.labelCreated.description": "Group of labels that you want to add to the pull request automatically. Labels that don't exist in the repository won't be added.", "githubPullRequests.labelCreated.label.description": "Each string element is value of label that you want to add", "githubIssues.alwaysPromptForNewIssueRepo.description": "Enabling will always prompt which repository to create an issue in instead of basing off the current open file.", "view.github.pull.requests.name": "GitHub", "view.github.pull.request.name": "GitHub Pull Request", "view.github.login.name": "<PERSON><PERSON>", "view.pr.github.name": "Pull Requests", "view.pr.github.accessibilityHelpContent": {"message": "Helpful commands include:\n-GitHub Pull Requests: Refresh Pull Requests List<keybinding:pr.refreshList>\n-GitHub Pull Requests: Focus on Issues View<keybinding:issues:github.focus> \n-GitHub Pull Requests: Focus on Pull Requests View<keybinding:pr:github.focus>\n-GitHub Issues: Copy GitHub Permalink<keybinding:issue.copyGithubPermalink>\n-GitHub Issues: Create an Issue<keybinding:issue.createIssueFromFile>\n-GitHub Pull Requests: Create Pull Request<keybinding:pr.create>", "comment": ["Do not translate the contents of (...) or <...> in the message. They are commands that will be replaced with the actual command name and keybinding."]}, "view.issues.github.name": "Issues", "view.notifications.github.name": "Notifications", "view.github.conflictResolution.name": "Conflict Resolution", "view.github.create.pull.request.name": "Create", "view.github.compare.changes.name": "Files Changed", "view.github.compare.changesCommits.name": "Commits", "view.pr.status.github.name": "Changes In Pull Request", "view.github.active.pull.request.name": "Review Pull Request", "view.github.active.pull.request.welcome.name": "Active Pull Request", "command.pull.request.category": "GitHub Pull Requests", "command.githubpr.remoteAgent.title": "Remote agent integration", "command.pr.create.title": "Create Pull Request", "command.pr.pick.title": "Checkout Pull Request", "command.pr.openChanges.title": "Open Changes", "command.pr.pickOnVscodeDev.title": "Checkout Pull Request on vscode.dev", "command.pr.exit.title": "Checkout Default Branch", "command.pr.dismissNotification.title": "Dismiss Notification", "command.pr.merge.title": "<PERSON><PERSON>quest", "command.pr.readyForReview.title": "<PERSON> Request Ready For Review", "command.pr.openPullRequestOnGitHub.title": "Open Pull Request on GitHub", "command.pr.openAllDiffs.title": "Open All Diffs", "command.pr.refreshPullRequest.title": "Refresh Pull Request", "command.pr.openFileOnGitHub.title": "Open File on GitHub", "command.pr.copyCommitHash.title": "Copy Commit <PERSON>", "command.pr.openOriginalFile.title": "Open Original File", "command.pr.openModifiedFile.title": "Open Modified File", "command.pr.openDiffView.title": "Open Diff View", "command.pr.openDiffViewFromEditor.title": "Open Pull Request Diff View", "command.pr.openDescription.title": "View Pull Request Description", "command.pr.openDescriptionToTheSide.title": "Open Pull Request Description to the Side", "command.pr.refreshDescription.title": "Refresh Pull Request Description", "command.pr.focusDescriptionInput.title": "Focus Pull Request Description Review Input", "command.pr.showDiffSinceLastReview.title": "Show Changes Since Last Review", "command.pr.showDiffAll.title": "Show All Changes", "command.pr.checkoutByNumber.title": "Checkout Pull Request by Number", "command.review.openFile.title": "Open File", "command.review.openLocalFile.title": "Open File", "command.review.suggestDiff.title": "Suggest Edit", "command.review.approve.title": "Approve", "command.review.comment.title": "Comment", "command.review.requestChanges.title": "Request Changes", "command.review.approveOnDotCom.title": "Approve on github.com", "command.review.requestChangesOnDotCom.title": "Request changes on github.com", "command.review.createSuggestionsFromChanges.title": "Create Pull Request Suggestions", "command.review.createSuggestionFromChange.title": "Convert to Pull Request Suggestion", "command.review.copyPrLink.title": "Copy <PERSON>ull Request Link", "command.pr.refreshList.title": "Refresh <PERSON>ull Requests List", "command.pr.setFileListLayoutAsTree.title": "View as Tree", "command.pr.setFileListLayoutAsFlat.title": "View as List", "command.pr.refreshChanges.title": "Refresh", "command.pr.configurePRViewlet.title": "Configure...", "command.pr.deleteLocalBranch.title": "Delete Local Branch", "command.pr.signin.title": "Sign in to GitHub", "command.pr.signinenterprise.title": "Sign in to GitHub Enterprise", "command.pr.deleteLocalBranchesNRemotes.title": "Delete local branches and remotes", "command.pr.createComment.title": "Add Review Comment", "command.pr.createSingleComment.title": "Add Comment", "command.pr.makeSuggestion.title": "Make Code Suggestion", "command.pr.startReview.title": "Start Review", "command.pr.editComment.title": "Edit Comment", "command.pr.cancelEditComment.title": "Cancel", "command.pr.saveComment.title": "Save", "command.pr.deleteComment.title": "Delete Comment", "command.pr.resolveReviewThread.title": "Resolve Conversation", "command.pr.unresolveReviewThread.title": "Unresolve Conversation", "command.pr.diffOutdatedCommentWithHead.title": "Diff Comment with HEAD", "command.pr.signinAndRefreshList.title": "Sign in and Refresh", "command.pr.configureRemotes.title": "Configure Remotes...", "command.pr.refreshActivePullRequest.title": "Refresh", "command.pr.markFileAsViewed.title": "<PERSON> File As Viewed", "command.pr.unmarkFileAsViewed.title": "<PERSON> File As Not Viewed", "command.pr.openReview.title": "Go to Review", "command.pr.collapseAllComments.title": "Collapse All Comments", "command.comments.category": "Comments", "command.pr.editQuery.title": "Edit Query", "command.pr.openPullsWebsite.title": "Open on GitHub", "command.pr.resetViewedFiles.title": "Reset Viewed Files", "command.pr.goToNextDiffInPr.title": "Go to Next Diff in Pull Request", "command.pr.goToPreviousDiffInPr.title": "Go to Previous Diff in Pull Request", "command.pr.copyCommentLink.title": "Copy Comment Link", "command.pr.applySuggestion.title": "Apply Suggestion", "command.pr.applySuggestionWithCopilot.title": "Apply Suggestion With Copilot", "command.pr.addAssigneesToNewPr.title": "Add Assignees", "command.pr.addReviewersToNewPr.title": "Add Reviewers", "command.pr.addLabelsToNewPr.title": "Apply Labels", "command.pr.addMilestoneToNewPr.title": "Set Milestone", "command.pr.addProjectsToNewPr.title": "Set Projects", "command.pr.preReview.title": "Pre-review Changes", "command.pr.addFileComment.title": "Add File Comment", "command.review.diffWithPrHead.title": "Compare Base With Pull Request Head (readonly)", "command.review.diffLocalWithPrHead.title": "Compare Pull Request Head with Local", "command.issues.category": "GitHub Issues", "command.issue.createIssueFromSelection.title": "Create Issue From Selection", "command.issue.createIssueFromClipboard.title": "Create Issue From Clipboard", "command.pr.copyVscodeDevPrLink.title": "Copy vscode.dev Pull Request Link", "command.pr.createPrMenuCreate.title": "Create", "command.pr.createPrMenuDraft.title": "Create Draft", "command.pr.createPrMenuSquash.title": "Create + Auto-Squash", "command.pr.createPrMenuMergeWhenReady.title": "Create + Merge When Ready", "command.pr.createPrMenuMerge.title": "Create + Auto-Merge", "command.pr.createPrMenuRebase.title": "Create + Auto-Rebase", "command.pr.refreshComments.title": "Refresh Pull Request Comments", "command.pr.resolveConflict.title": "Resolve Conflict", "command.pr.acceptMerge.title": "Accept <PERSON><PERSON>", "command.pr.closeRelatedEditors.title": "Close All Pull Request Editors", "command.pr.toggleEditorCommentingOn.title": "Toggle Editor Commenting On", "command.pr.toggleEditorCommentingOff.title": "Toggle Editor Commenting Off", "command.issue.openDescription.title": "View Issue Description", "command.issue.copyGithubDevLink.title": "Copy github.dev Link", "command.issue.copyGithubPermalink.title": "Copy GitHub Permalink", "command.issue.copyGithubHeadLink.title": "<PERSON><PERSON>itHub Head Link", "command.issue.copyMarkdownGithubPermalink.title": "<PERSON><PERSON> as <PERSON><PERSON>", "command.issue.openGithubPermalink.title": "Open Permalink on GitHub", "command.issue.openIssue.title": "Open Issue on GitHub", "command.issue.copyIssueNumber.title": "Copy Issue Number", "command.issue.copyIssueUrl.title": "Copy Issue Link", "command.issue.refresh.title": "Refresh", "command.issue.suggestRefresh.title": "Refresh Suggestions", "command.issue.startWorking.title": "Start Working on Issue", "command.issue.startWorkingBranchDescriptiveTitle.title": "Start Working on Issue and Checkout Topic Branch", "command.issue.continueWorking.title": "Continue Working on Issue", "command.issue.startWorkingBranchPrompt.title": "Start Working and Set Branch...", "command.issue.stopWorking.title": "Stop Working on Issue", "command.issue.stopWorkingBranchDescriptiveTitle.title": "Stop Working on Issue and Leave Topic Branch", "command.issue.statusBar.title": "Current Issue Options", "command.issue.getCurrent.title": "Get current issue", "command.issue.editQuery.title": "Edit Query", "command.issue.createIssue.title": "Create an Issue", "command.issue.createIssueFromFile.title": "Create Issue", "command.issue.issueCompletion.title": "Issue Completion Chosen", "command.issue.userCompletion.title": "User Completion Chosen", "command.issue.signinAndRefreshList.title": "Sign in and Refresh", "command.issue.goToLinkedCode.title": "Go to Linked Code", "command.issues.openIssuesWebsite.title": "Open on GitHub", "command.issues.configureIssuesViewlet.title": "Configure...", "command.issue.chatSummarizeIssue.title": "Summarize With Copilot", "command.issue.chatSuggestFix.title": "Suggest a Fix with Copilot", "command.notifications.category": "GitHub Notifications", "command.notifications.refresh.title": "Refresh", "command.notifications.pri.title": "Prioritize", "command.notifications.loadMore.title": "Load More Notifications", "command.notifications.sortByTimestamp.title": "Sort by Timestamp", "command.notifications.sortByPriority.title": "Sort by Priority using Copilot", "command.notifications.openOnGitHub.title": "Open on GitHub", "command.notifications.markAsRead.title": "<PERSON> <PERSON>", "command.notifications.markAsDone.title": "<PERSON> as <PERSON>", "command.notifications.markPullRequestsAsRead.title": "<PERSON> Requests as <PERSON>", "command.notifications.markPullRequestsAsDone.title": "<PERSON> Requests as <PERSON>", "command.notifications.configureNotificationsViewlet.title": "Configure...", "command.notification.chatSummarizeNotification.title": "Summarize With Copilot", "command.codingAgent.openSessionLog.title": "Open Coding Agent Session Log", "welcome.github.login.contents": {"message": "You have not yet signed in with GitHub\n[Sign in](command:pr.signin)", "comment": ["Do not translate what's inside of (...). It is link syntax.", "{Locked='](command:pr.signin)'}"]}, "welcome.github.noGit.contents": "Git is not installed or otherwise not available. Install git or fix your git installation and then reload.", "welcome.github.loginNoEnterprise.contents": {"message": "You have not yet signed in with GitHub\n[Sign in](command:pr.signin<PERSON><PERSON>Enter<PERSON>)", "comment": ["Do not translate what's inside of (...). It is link syntax.", "{Locked='](command:pr.signinNoEnterprise)'}"]}, "welcome.github.loginWithEnterprise.contents": {"message": "[Sign in with GitHub Enterprise](command:pr.signinenter<PERSON>)", "comment": ["Do not translate what's inside of (...). It is link syntax.", "{Locked='](command:pr.signinenter<PERSON>)'}"]}, "welcome.pr.github.uninitialized.contents": "Loading...", "welcome.pr.github.noFolder.contents": {"message": "You have not yet opened a folder.\n[Open Folder](command:workbench.action.files.openFolder)", "comment": ["Do not translate what's inside of (...). It is link syntax.", "{Locked='](command:workbench.action.files.openFolder)'}"]}, "welcome.pr.github.noRepo.contents": "No git repositories found", "welcome.pr.github.parentRepo.contents": {"message": "A git repository was found in the parent folders of the workspace or the open file(s).\n[Open Repository](command:git.openRepositoriesInParentFolders)\nUse the [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) setting to control whether git repositories in parent folders of workspaces or open files are opened. To learn more [read our docs](https://aka.ms/vscode-git-repository-in-parent-folders).", "comment": ["{Locked='](command:git.openRepositoriesInParentFolders'}", "{Locked='](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "welcome.pr.github.parentRepos.contents": {"message": "Git repositories were found in the parent folders of the workspace or the open file(s).\n[Open Repository](command:git.openRepositoriesInParentFolders)\nUse the [git.openRepositoryInParentFolders](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D) setting to control whether git repositories in parent folders of workspace or open files are opened. To learn more [read our docs](https://aka.ms/vscode-git-repository-in-parent-folders).", "comment": ["{Locked='](command:git.openRepositoriesInParentFolders'}", "{Locked='](command:workbench.action.openSettings?%5B%22git.openRepositoryInParentFolders%22%5D'}", "Do not translate the 'command:*' part inside of the '(..)'. It is an internal command syntax for VS Code", "Please make sure there is no space between the right bracket and left parenthesis:  ]( this is an internal syntax for links"]}, "welcome.github.notificationsLoading.contents": "Loading...", "welcome.github.notifications.contents": "No notifications, your inbox is empty $(rocket)", "welcome.issues.github.uninitialized.contents": "Loading...", "welcome.issues.github.noFolder.contents": "You have not yet opened a folder.", "welcome.issues.github.noRepo.contents": "No git repositories found", "welcome.github.activePullRequest.contents": "Loading...", "languageModelTools.github-pull-request_issue_fetch.displayName": "Get a GitHub Issue or PR", "languageModelTools.github-pull-request_issue_summarize.displayName": "Summarize a GitHub Issue or PR", "languageModelTools.github-pull-request_notification_fetch.displayName": "Get a GitHub Notification", "languageModelTools.github-pull-request_notification_summarize.displayName": "Summarize a GitHub Notification", "languageModelTools.github-pull-request_suggest-fix.displayName": "Suggest a Fix for a GitHub Issue", "languageModelTools.github-pull-request_formSearchQuery.displayName": "Convert natural language to a GitHub search query", "languageModelTools.github-pull-request_doSearch.displayName": "Execute a GitHub search", "languageModelTools.github-pull-request_renderIssues.displayName": "Render issue items in a markdown table", "languageModelTools.github-pull-request_activePullRequest.displayName": "Active Pull Request", "languageModelTools.github-pull-request_activePullRequest.description": "Get information about the active GitHub pull request. This information includes: comments, files changed, pull request title + description, pull request state, and pull request status checks/CI.", "languageModelTools.github-pull-request_copilot-coding-agent.displayName": "Copilot coding agent", "languageModelTools.github-pull-request_copilot-coding-agent.userDescription": "Completes the provided task using an asynchronous coding agent. Use when the user wants copilot continue completing a task in the background or asynchronously. Launch an autonomous GitHub Copilot agent to work on coding tasks in the background. The agent will create a new branch, implement the requested changes, and open a pull request with the completed work."}