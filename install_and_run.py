#!/usr/bin/env python3
"""
自动安装依赖并运行Web应用的脚本
"""

import subprocess
import sys
import os
import time

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, '-m', 'pip', 'install', package])
        return True
    except subprocess.CalledProcessError:
        return False

def check_and_install_dependencies():
    """检查并安装依赖"""
    print("=" * 60)
    print("🔍 检查Python依赖...")
    print("=" * 60)
    
    # 必需的包
    required_packages = [
        'Flask==2.3.3',
        'Flask-SocketIO==5.3.6',
        'Werkzeug==2.3.7',
        'python-socketio==5.8.0',
        'python-engineio==4.7.1'
    ]
    
    # 可选的包
    optional_packages = [
        'psutil==5.9.5'  # 用于系统信息监控
    ]
    
    # 安装必需包
    for package in required_packages:
        package_name = package.split('==')[0]
        print(f"📦 检查 {package_name}...")
        
        try:
            __import__(package_name.replace('-', '_'))
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"⚠️  {package_name} 未安装，正在安装...")
            if install_package(package):
                print(f"✅ {package_name} 安装成功")
            else:
                print(f"❌ {package_name} 安装失败")
                return False
    
    # 安装可选包
    for package in optional_packages:
        package_name = package.split('==')[0]
        print(f"📦 检查可选包 {package_name}...")
        
        try:
            __import__(package_name.replace('-', '_'))
            print(f"✅ {package_name} 已安装")
        except ImportError:
            print(f"⚠️  {package_name} 未安装，正在安装...")
            if install_package(package):
                print(f"✅ {package_name} 安装成功")
            else:
                print(f"⚠️  {package_name} 安装失败（可选包，不影响运行）")
    
    return True

def create_simple_web_app():
    """创建简化版Web应用（如果主应用无法运行）"""
    simple_app_code = '''
from flask import Flask, render_template_string
import datetime

app = Flask(__name__)

@app.route('/')
def index():
    return render_template_string("""
<!DOCTYPE html>
<html>
<head>
    <title>Python Web应用 - 简化版</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="text-center">
            <h1 class="display-4 text-primary">🐍 Python Web应用</h1>
            <p class="lead">简化版 - 运行在端口1111</p>
            <div class="card mt-4">
                <div class="card-body">
                    <h5>系统信息</h5>
                    <p><strong>当前时间:</strong> {{ current_time }}</p>
                    <p><strong>Python版本:</strong> {{ python_version }}</p>
                    <p><strong>服务器状态:</strong> <span class="badge bg-success">运行中</span></p>
                </div>
            </div>
            <div class="mt-4">
                <a href="/test" class="btn btn-primary">测试页面</a>
                <a href="/api" class="btn btn-outline-info">API测试</a>
            </div>
        </div>
    </div>
</body>
</html>
    """, current_time=datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
         python_version=f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")

@app.route('/test')
def test():
    return render_template_string("""
<!DOCTYPE html>
<html>
<head>
    <title>测试页面</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <h2>🧪 测试页面</h2>
        <div class="alert alert-success">
            <h4>✅ Python Web应用运行正常！</h4>
            <p>这是一个测试页面，证明Flask应用正在正常工作。</p>
        </div>
        <a href="/" class="btn btn-primary">返回首页</a>
    </div>
</body>
</html>
    """)

@app.route('/api')
def api():
    import json
    data = {
        'status': 'success',
        'message': 'API正常工作',
        'timestamp': datetime.datetime.now().isoformat(),
        'python_version': f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"
    }
    return json.dumps(data, ensure_ascii=False, indent=2)

if __name__ == '__main__':
    print("🚀 启动简化版Python Web应用...")
    print("📍 访问地址: http://localhost:1111")
    app.run(host='0.0.0.0', port=1111, debug=True)
'''
    
    with open('simple_web_app.py', 'w', encoding='utf-8') as f:
        f.write(simple_app_code)
    
    print("✅ 创建了简化版Web应用: simple_web_app.py")

def main():
    """主函数"""
    print("🐍 Python Web应用自动安装和启动脚本")
    print(f"📅 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"🐍 Python版本: {sys.version}")
    print(f"📁 工作目录: {os.getcwd()}")
    
    # 检查并安装依赖
    if not check_and_install_dependencies():
        print("\n❌ 依赖安装失败，创建简化版应用...")
        create_simple_web_app()
        print("\n🚀 启动简化版应用...")
        try:
            subprocess.run([sys.executable, 'simple_web_app.py'])
        except KeyboardInterrupt:
            print("\n👋 应用已停止")
        return
    
    print("\n✅ 所有依赖安装完成！")
    
    # 检查主应用文件是否存在
    if os.path.exists('web_app.py'):
        print("🚀 启动完整版Web应用...")
        try:
            subprocess.run([sys.executable, 'web_app.py'])
        except KeyboardInterrupt:
            print("\n👋 应用已停止")
        except Exception as e:
            print(f"\n❌ 启动失败: {e}")
            print("🔄 尝试启动简化版...")
            create_simple_web_app()
            subprocess.run([sys.executable, 'simple_web_app.py'])
    else:
        print("⚠️  未找到主应用文件，创建并启动简化版...")
        create_simple_web_app()
        subprocess.run([sys.executable, 'simple_web_app.py'])

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n👋 安装过程被中断")
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        print("请检查Python环境和网络连接")
