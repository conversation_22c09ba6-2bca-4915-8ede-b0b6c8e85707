on:
  workflow_call:
    inputs:
      job_name:
        type: string
        required: true
      rustup_toolchain:
        type: string
        required: true

jobs:
  linux-cli-test:
    name: ${{ inputs.job_name }}
    runs-on: [ self-hosted, 1ES.Pool=1es-vscode-oss-ubuntu-22.04-x64 ]
    env:
      RUSTUP_TOOLCHAIN: ${{ inputs.rustup_toolchain }}
    steps:
      - name: Checkout microsoft/vscode
        uses: actions/checkout@v4

      - name: Install Rust
        run: |
          set -e
          curl https://sh.rustup.rs -sSf | sh -s -- -y --profile minimal --default-toolchain $RUSTUP_TOOLCHAIN
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH

      - name: Set Rust version
        run: |
          set -e
          rustup default $RUSTUP_TOOLCHAIN
          rustup update $RUSTUP_TOOLCHAIN
          rustup component add clippy

      - name: Check Rust versions
        run: |
          set -e
          rustc --version
          cargo --version

      - name: Clippy lint
        run: cargo clippy -- -D warnings
        working-directory: cli

      - name: 🧪 Run unit tests
        run: cargo test
        working-directory: cli
