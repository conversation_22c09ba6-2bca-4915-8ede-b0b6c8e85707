parameters:
  - name: VSCODE_BUILD_ALPINE
    type: boolean
    default: false
  - name: VSCODE_BUILD_ALPINE_ARM64
    type: boolean
    default: false
  - name: VSCODE_QUALITY
    type: string
  - name: VSCODE_CHECK_ONLY
    type: boolean
    default: false

steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - template: ../cli/cli-apply-patches.yml@self

    - script: |
        set -e
        npm ci
      workingDirectory: build
      env:
        GITHUB_TOKEN: "$(github-distro-mixin-password)"
      displayName: Install build dependencies

  - task: Npm@1
    displayName: Download openssl prebuilt
    inputs:
      command: custom
      customCommand: pack @vscode-internal/openssl-prebuilt@0.0.11
      customRegistry: useFeed
      customFeed: "Monaco/openssl-prebuilt"
      workingDir: $(Build.ArtifactStagingDirectory)

  - script: |
      set -e
      mkdir $(Build.ArtifactStagingDirectory)/openssl
      tar -xvzf $(Build.ArtifactStagingDirectory)/vscode-internal-openssl-prebuilt-0.0.11.tgz --strip-components=1 --directory=$(Build.ArtifactStagingDirectory)/openssl
    displayName: Extract openssl prebuilt

  # inspired by: https://github.com/emk/rust-musl-builder/blob/main/Dockerfile
  - bash: |
      set -e
      sudo apt-get update
      sudo apt-get install -yq build-essential musl-dev musl-tools linux-libc-dev pkgconf xutils-dev lld
      sudo ln -s "/usr/bin/g++" "/usr/bin/musl-g++" || echo "link exists"
    displayName: Install musl build dependencies

  - template: ../cli/install-rust-posix.yml@self
    parameters:
      targets:
        - ${{ if eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true) }}:
          - aarch64-unknown-linux-musl
        - ${{ if eq(parameters.VSCODE_BUILD_ALPINE, true) }}:
          - x86_64-unknown-linux-musl

  - ${{ if eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true) }}:
    - template: ../cli/cli-compile.yml@self
      parameters:
        VSCODE_CLI_TARGET: aarch64-unknown-linux-musl
        VSCODE_CLI_ARTIFACT: vscode_cli_alpine_arm64_cli
        VSCODE_QUALITY: ${{ parameters.VSCODE_QUALITY }}
        VSCODE_CLI_ENV:
          OPENSSL_LIB_DIR: $(Build.ArtifactStagingDirectory)/openssl/arm64-linux-musl/lib
          OPENSSL_INCLUDE_DIR: $(Build.ArtifactStagingDirectory)/openssl/arm64-linux-musl/include
          OPENSSL_STATIC: "1"
          SYSROOT_ARCH: arm64
          IS_MUSL: "1"

  - ${{ if eq(parameters.VSCODE_BUILD_ALPINE, true) }}:
    - template: ../cli/cli-compile.yml@self
      parameters:
        VSCODE_CLI_TARGET: x86_64-unknown-linux-musl
        VSCODE_CLI_ARTIFACT: vscode_cli_alpine_x64_cli
        VSCODE_QUALITY: ${{ parameters.VSCODE_QUALITY }}
        VSCODE_CLI_ENV:
          CXX_aarch64-unknown-linux-musl: musl-g++
          CC_aarch64-unknown-linux-musl: musl-gcc
          OPENSSL_LIB_DIR: $(Build.ArtifactStagingDirectory)/openssl/x64-linux-musl/lib
          OPENSSL_INCLUDE_DIR: $(Build.ArtifactStagingDirectory)/openssl/x64-linux-musl/include
          OPENSSL_STATIC: "1"

  - ${{ if not(parameters.VSCODE_CHECK_ONLY) }}:
    - ${{ if eq(parameters.VSCODE_BUILD_ALPINE_ARM64, true) }}:
      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(Build.ArtifactStagingDirectory)/vscode_cli_alpine_arm64_cli.tar.gz
          artifactName: vscode_cli_alpine_arm64_cli
          sbomBuildDropPath: $(Build.ArtifactStagingDirectory)/cli
          sbomPackageName: "VS Code Alpine arm64 CLI"
          sbomPackageVersion: $(Build.SourceVersion)
        displayName: Publish vscode_cli_alpine_arm64_cli artifact

    - ${{ if eq(parameters.VSCODE_BUILD_ALPINE, true) }}:
      - task: 1ES.PublishPipelineArtifact@1
        inputs:
          targetPath: $(Build.ArtifactStagingDirectory)/vscode_cli_alpine_x64_cli.tar.gz
          artifactName: vscode_cli_alpine_x64_cli
          sbomBuildDropPath: $(Build.ArtifactStagingDirectory)/cli
          sbomPackageName: "VS Code Alpine x64 CLI"
          sbomPackageVersion: $(Build.SourceVersion)
        displayName: Publish vscode_cli_alpine_x64_cli artifact
