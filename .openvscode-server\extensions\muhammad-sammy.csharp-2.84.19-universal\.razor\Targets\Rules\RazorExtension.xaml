<?xml version="1.0" encoding="utf-8"?>
<Rule
  Description="Extension Properties"
  DisplayName="Extension Properties"
  Name="RazorExtension"
  PageTemplate="generic"
  xmlns="http://schemas.microsoft.com/build/2009/properties">
  <Rule.DataSource>
    <DataSource
      Persistence="ProjectFile"
      HasConfigurationCondition="True"
      ItemType="RazorExtension" />
  </Rule.DataSource>

  <Rule.Categories>
    <Category
      Name="General"
      DisplayName="General" />
  </Rule.Categories>

  <StringProperty
    Category="General"
    Description="Razor Extension Assembly Name"
    DisplayName="Razor Extension Assembly Name"
    Name="AssemblyName"
    ReadOnly="True"
    Visible="False" />

  <StringProperty
    Category="General"
    Description="Razor Extension Assembly File Path"
    DisplayName="Razor Extension Assembly File Path"
    Name="AssemblyFilePath"
    ReadOnly="True"
    Visible="False" />

</Rule>
