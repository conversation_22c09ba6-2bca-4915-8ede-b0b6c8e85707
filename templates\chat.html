{% extends "base.html" %}

{% block title %}聊天室 - Python Web应用{% endblock %}

{% block content %}
<div class="container-fluid">
    <h2 class="mb-4">
        <i class="fas fa-comments"></i> 实时聊天室
        <small class="text-muted">与其他用户实时交流</small>
    </h2>

    <div class="row">
        <div class="col-md-8">
            <!-- 聊天区域 -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">
                        <i class="fas fa-comment-dots"></i> 聊天消息
                    </h5>
                    <div>
                        <span class="badge bg-success" id="connectionStatus">
                            <i class="fas fa-circle"></i> 已连接
                        </span>
                        <span class="badge bg-info" id="userCount">0 用户在线</span>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="chat-container" id="chatContainer">
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-comments fa-3x mb-3"></i>
                            <p>欢迎来到聊天室！开始聊天吧...</p>
                        </div>
                    </div>
                </div>
                <div class="card-footer">
                    <form id="messageForm" class="d-flex">
                        <input type="text" id="messageInput" class="form-control me-2" 
                               placeholder="输入消息..." maxlength="500" required>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> 发送
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <!-- 在线用户 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-users"></i> 在线用户
                    </h5>
                </div>
                <div class="card-body">
                    <div id="onlineUsers">
                        <div class="d-flex align-items-center mb-2">
                            <div class="bg-success rounded-circle me-2" style="width: 8px; height: 8px;"></div>
                            <span>{{ session.username }} (你)</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 聊天功能 -->
            <div class="card mb-3">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-cog"></i> 聊天功能
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <label class="form-label">消息通知</label>
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="soundNotification" checked>
                            <label class="form-check-label" for="soundNotification">
                                声音提醒
                            </label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">主题颜色</label>
                        <select class="form-select" id="themeSelect">
                            <option value="default">默认</option>
                            <option value="dark">深色</option>
                            <option value="blue">蓝色</option>
                            <option value="green">绿色</option>
                        </select>
                    </div>
                    
                    <button class="btn btn-outline-secondary btn-sm w-100" onclick="clearChat()">
                        <i class="fas fa-trash"></i> 清空聊天记录
                    </button>
                </div>
            </div>

            <!-- 快捷表情 -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-smile"></i> 快捷表情
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-3 mb-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('😀')">😀</button>
                        </div>
                        <div class="col-3 mb-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('😂')">😂</button>
                        </div>
                        <div class="col-3 mb-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('😍')">😍</button>
                        </div>
                        <div class="col-3 mb-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('🤔')">🤔</button>
                        </div>
                        <div class="col-3 mb-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('👍')">👍</button>
                        </div>
                        <div class="col-3 mb-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('👎')">👎</button>
                        </div>
                        <div class="col-3 mb-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('❤️')">❤️</button>
                        </div>
                        <div class="col-3 mb-2">
                            <button class="btn btn-outline-secondary btn-sm" onclick="insertEmoji('🎉')">🎉</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Socket.IO连接
    const socket = io();
    const chatContainer = document.getElementById('chatContainer');
    const messageForm = document.getElementById('messageForm');
    const messageInput = document.getElementById('messageInput');
    const connectionStatus = document.getElementById('connectionStatus');
    
    let messageCount = 0;

    // 连接事件
    socket.on('connect', function() {
        connectionStatus.innerHTML = '<i class="fas fa-circle"></i> 已连接';
        connectionStatus.className = 'badge bg-success';
        addSystemMessage('已连接到聊天室');
    });

    socket.on('disconnect', function() {
        connectionStatus.innerHTML = '<i class="fas fa-circle"></i> 已断开';
        connectionStatus.className = 'badge bg-danger';
        addSystemMessage('与服务器断开连接');
    });

    // 接收消息
    socket.on('message', function(data) {
        addMessage(data.username, data.message, data.timestamp);
        
        // 声音提醒
        if (document.getElementById('soundNotification').checked && data.username !== '{{ session.username }}') {
            playNotificationSound();
        }
    });

    // 状态消息
    socket.on('status', function(data) {
        addSystemMessage(data.msg);
    });

    // 发送消息
    messageForm.addEventListener('submit', function(e) {
        e.preventDefault();
        const message = messageInput.value.trim();
        
        if (message) {
            socket.emit('message', {
                message: message,
                room: 'general'
            });
            messageInput.value = '';
        }
    });

    // 添加消息到聊天区域
    function addMessage(username, message, timestamp) {
        // 如果是第一条消息，清空欢迎信息
        if (messageCount === 0) {
            chatContainer.innerHTML = '';
        }
        messageCount++;

        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        
        const isOwnMessage = username === '{{ session.username }}';
        if (isOwnMessage) {
            messageDiv.style.backgroundColor = '#e3f2fd';
            messageDiv.style.marginLeft = '20px';
        }
        
        messageDiv.innerHTML = `
            <div class="d-flex justify-content-between align-items-start">
                <div>
                    <span class="message-username">${username}</span>
                    ${isOwnMessage ? '<small class="text-muted">(你)</small>' : ''}
                </div>
                <small class="message-time">${timestamp}</small>
            </div>
            <div class="mt-1">${escapeHtml(message)}</div>
        `;
        
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    // 添加系统消息
    function addSystemMessage(message) {
        const messageDiv = document.createElement('div');
        messageDiv.className = 'message';
        messageDiv.style.backgroundColor = '#f8f9fa';
        messageDiv.style.fontStyle = 'italic';
        messageDiv.style.textAlign = 'center';
        messageDiv.innerHTML = `<small class="text-muted">${escapeHtml(message)}</small>`;
        
        chatContainer.appendChild(messageDiv);
        chatContainer.scrollTop = chatContainer.scrollHeight;
    }

    // HTML转义
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 插入表情
    function insertEmoji(emoji) {
        messageInput.value += emoji;
        messageInput.focus();
    }

    // 清空聊天记录
    function clearChat() {
        if (confirm('确定要清空聊天记录吗？')) {
            chatContainer.innerHTML = '<div class="text-center text-muted py-4"><i class="fas fa-comments fa-3x mb-3"></i><p>聊天记录已清空</p></div>';
            messageCount = 0;
        }
    }

    // 播放通知声音
    function playNotificationSound() {
        // 创建音频上下文播放提示音
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.value = 800;
        oscillator.type = 'sine';
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    }

    // 主题切换
    document.getElementById('themeSelect').addEventListener('change', function(e) {
        const theme = e.target.value;
        const chatContainer = document.getElementById('chatContainer');
        
        switch(theme) {
            case 'dark':
                chatContainer.style.backgroundColor = '#2c3e50';
                chatContainer.style.color = 'white';
                break;
            case 'blue':
                chatContainer.style.backgroundColor = '#e3f2fd';
                break;
            case 'green':
                chatContainer.style.backgroundColor = '#e8f5e8';
                break;
            default:
                chatContainer.style.backgroundColor = '#f9f9f9';
                chatContainer.style.color = 'black';
        }
    });

    // 自动聚焦输入框
    messageInput.focus();
    
    // 回车发送消息
    messageInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            messageForm.dispatchEvent(new Event('submit'));
        }
    });
</script>
{% endblock %}
