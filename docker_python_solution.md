# Docker容器中Python运行问题解决方案

## 问题分析

您遇到的问题是：
- **Windows主机**: Python 3.13.5 已安装且可用
- **Docker容器**: 没有安装Python，所以 `python: command not found`
- **VS Code Server**: 在Docker容器中运行，集成终端连接到容器内部

## 解决方案

### 方案1: 在Docker容器中安装Python (推荐)

#### 1.1 检查容器的Linux发行版
在VS Code Server终端中运行：
```bash
cat /etc/os-release
```

#### 1.2 根据发行版安装Python

**Ubuntu/Debian系统:**
```bash
sudo apt update
sudo apt install -y python3 python3-pip
sudo ln -s /usr/bin/python3 /usr/bin/python
```

**CentOS/RHEL/Fedora系统:**
```bash
sudo yum install -y python3 python3-pip
# 或者对于较新版本
sudo dnf install -y python3 python3-pip
sudo ln -s /usr/bin/python3 /usr/bin/python
```

**Alpine Linux:**
```bash
sudo apk add python3 py3-pip
sudo ln -s /usr/bin/python3 /usr/bin/python
```

#### 1.3 验证安装
```bash
python --version
python3 --version
pip --version
```

### 方案2: 修改Docker镜像

如果您有Docker镜像的控制权，可以在Dockerfile中添加：

```dockerfile
# 对于Ubuntu/Debian基础镜像
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    && ln -s /usr/bin/python3 /usr/bin/python \
    && rm -rf /var/lib/apt/lists/*

# 对于Alpine基础镜像
RUN apk add --no-cache python3 py3-pip \
    && ln -s /usr/bin/python3 /usr/bin/python
```

### 方案3: 使用现有的Python3命令

如果容器中已有python3但没有python命令：

```bash
# 创建别名（临时）
alias python=python3
alias pip=pip3

# 或创建符号链接（永久）
sudo ln -s /usr/bin/python3 /usr/bin/python
sudo ln -s /usr/bin/pip3 /usr/bin/pip
```

### 方案4: 修改VS Code终端配置

在 `.vscode/settings.json` 中添加：

```json
{
  "terminal.integrated.defaultProfile.linux": "bash",
  "terminal.integrated.profiles.linux": {
    "bash": {
      "path": "/bin/bash",
      "args": ["-c", "alias python=python3; alias pip=pip3; exec bash"]
    }
  }
}
```

## 快速诊断脚本

我为您创建了一个诊断脚本，请在VS Code Server终端中运行：

```bash
# 检查当前环境
echo "=== 环境检查 ==="
echo "当前用户: $(whoami)"
echo "当前目录: $(pwd)"
echo "操作系统: $(uname -a)"

# 检查Python
echo -e "\n=== Python检查 ==="
which python && python --version || echo "python命令不存在"
which python3 && python3 --version || echo "python3命令不存在"
which pip && pip --version || echo "pip命令不存在"
which pip3 && pip3 --version || echo "pip3命令不存在"

# 检查包管理器
echo -e "\n=== 包管理器检查 ==="
which apt-get && echo "apt-get可用" || echo "apt-get不可用"
which yum && echo "yum可用" || echo "yum不可用"
which dnf && echo "dnf可用" || echo "dnf不可用"
which apk && echo "apk可用" || echo "apk不可用"

# 检查sudo权限
echo -e "\n=== 权限检查 ==="
sudo -n true 2>/dev/null && echo "有sudo权限" || echo "无sudo权限"
```

## 推荐的完整解决流程

### 步骤1: 诊断环境
在VS Code Server终端中运行上面的诊断脚本。

### 步骤2: 安装Python
根据诊断结果选择合适的安装命令。

### 步骤3: 创建别名
```bash
echo 'alias python=python3' >> ~/.bashrc
echo 'alias pip=pip3' >> ~/.bashrc
source ~/.bashrc
```

### 步骤4: 测试运行
```bash
python hello_world.py
```

## 临时解决方案

如果无法安装Python，可以使用以下临时方案：

### 使用python3命令
```bash
python3 hello_world.py
```

### 在每次会话中创建别名
```bash
alias python=python3
python hello_world.py
```

## 验证解决方案

安装完成后，运行以下命令验证：

```bash
# 检查Python
python --version
python -c "print('Hello from Python!')"

# 检查pip
pip --version
pip list

# 测试您的文件
python hello_world.py
```

## 常见问题

### Q: 没有sudo权限怎么办？
A: 联系系统管理员或使用用户级别的Python安装（如pyenv）。

### Q: 安装后仍然找不到python命令？
A: 检查PATH环境变量，或使用完整路径运行。

### Q: 容器重启后配置丢失？
A: 需要修改Docker镜像或在容器启动脚本中添加安装命令。

## 下一步

解决Python安装问题后，您就可以：
1. 在VS Code Server终端中正常运行Python文件
2. 使用调试功能
3. 安装和使用Python包
4. 进行完整的Python开发

请先运行诊断脚本，然后根据结果选择合适的解决方案！
