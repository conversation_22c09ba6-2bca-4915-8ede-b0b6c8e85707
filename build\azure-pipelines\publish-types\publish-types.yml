# Publish @types/vscode for each release

trigger:
  branches:
    include: ["refs/tags/*"]

pr: none

pool:
  vmImage: ubuntu-latest

steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - bash: |
      TAG_VERSION=$(git describe --tags `git rev-list --tags --max-count=1`)
      CHANNEL="C1C14HJ2F"

      if [ "$TAG_VERSION" == "1.999.0" ]; then
        MESSAGE="<!here>. Someone pushed 1.999.0 tag. Please delete it ASAP from remote and local."

        curl -X POST -H "Authorization: Bearer $(SLACK_TOKEN)" \
        -H 'Content-type: application/json; charset=utf-8' \
        --data '{"channel":"'"$CHANNEL"'", "link_names": true, "text":"'"$MESSAGE"'"}' \
        https://slack.com/api/chat.postMessage

        exit 1
      fi
    displayName: Check 1.999.0 tag

  - bash: |
      # Install build dependencies
      (cd build && npm ci)
      node build/azure-pipelines/publish-types/check-version.js
    displayName: Check version

  - bash: |
      git config --global user.email "<EMAIL>"
      git config --global user.name "VSCode"

      git clone https://$(GITHUB_TOKEN)@github.com/DefinitelyTyped/DefinitelyTyped.git --depth=1
      node build/azure-pipelines/publish-types/update-types.js

      TAG_VERSION=$(git describe --tags `git rev-list --tags --max-count=1`)

      cd DefinitelyTyped

      git diff --color | cat
      git add -A
      git status
      git checkout -b "vscode-types-$TAG_VERSION"
      git commit -m "VS Code $TAG_VERSION Extension API"
      git push origin "vscode-types-$TAG_VERSION"

    displayName: Push update to DefinitelyTyped

  - bash: |
      TAG_VERSION=$(git describe --tags `git rev-list --tags --max-count=1`)
      CHANNEL="C1C14HJ2F"

      MESSAGE="DefinitelyTyped/DefinitelyTyped#vscode-types-$TAG_VERSION created. Endgame champion, please open this link, examine changes and create a PR:"
      LINK="https://github.com/DefinitelyTyped/DefinitelyTyped/compare/vscode-types-$TAG_VERSION?quick_pull=1&body=Updating%20VS%20Code%20Extension%20API.%20See%20https%3A%2F%2Fgithub.com%2Fmicrosoft%2Fvscode%2Fissues%2F70175%20for%20details."
      MESSAGE2="[@jrieken, @kmaetzel, @egamma]. Please review and merge PR to publish @types/vscode."

      curl -X POST -H "Authorization: Bearer $(SLACK_TOKEN)" \
      -H 'Content-type: application/json; charset=utf-8' \
      --data '{"channel":"'"$CHANNEL"'", "link_names": true, "text":"'"$MESSAGE"'"}' \
      https://slack.com/api/chat.postMessage

      curl -X POST -H "Authorization: Bearer $(SLACK_TOKEN)" \
      -H 'Content-type: application/json; charset=utf-8' \
      --data '{"channel":"'"$CHANNEL"'", "link_names": true, "text":"'"$LINK"'"}' \
      https://slack.com/api/chat.postMessage

      curl -X POST -H "Authorization: Bearer $(SLACK_TOKEN)" \
      -H 'Content-type: application/json; charset=utf-8' \
      --data '{"channel":"'"$CHANNEL"'", "link_names": true, "text":"'"$MESSAGE2"'"}' \
      https://slack.com/api/chat.postMessage

    displayName: Send message linking to changes on Slack
