from collections.abc import Iterable
from collections.abc import Iterator
from typing import overload

from .structures import ImmutableList

class Accept(ImmutableList[tuple[str, int]]):
    provided: bool
    def __init__(
        self, values: Accept | Iterable[tuple[str, float]] | None = None
    ) -> None: ...
    def _specificity(self, value: str) -> tuple[bool, ...]: ...
    def _value_matches(self, value: str, item: str) -> bool: ...
    @overload  # type: ignore
    def __getitem__(self, key: str) -> int: ...
    @overload
    def __getitem__(self, key: int) -> tuple[str, int]: ...
    @overload
    def __getitem__(self, key: slice) -> Iterable[tuple[str, int]]: ...
    def quality(self, key: str) -> int: ...
    def __contains__(self, value: str) -> bool: ...  # type: ignore
    def index(self, key: str) -> int: ...  # type: ignore
    def find(self, key: str) -> int: ...
    def values(self) -> Iterator[str]: ...
    def to_header(self) -> str: ...
    def _best_single_match(self, match: str) -> tuple[str, int] | None: ...
    @overload
    def best_match(self, matches: Iterable[str], default: str) -> str: ...
    @overload
    def best_match(
        self, matches: Iterable[str], default: str | None = None
    ) -> str | None: ...
    @property
    def best(self) -> str: ...

def _normalize_mime(value: str) -> list[str]: ...

class MIMEAccept(Accept):
    def _specificity(self, value: str) -> tuple[bool, ...]: ...
    def _value_matches(self, value: str, item: str) -> bool: ...
    @property
    def accept_html(self) -> bool: ...
    @property
    def accept_xhtml(self) -> bool: ...
    @property
    def accept_json(self) -> bool: ...

def _normalize_lang(value: str) -> list[str]: ...

class LanguageAccept(Accept):
    def _value_matches(self, value: str, item: str) -> bool: ...

class CharsetAccept(Accept):
    def _value_matches(self, value: str, item: str) -> bool: ...
