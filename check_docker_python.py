#!/usr/bin/env python3
"""
检查Docker容器内Python环境的脚本
"""

import sys
import os
import subprocess
import shutil

def check_python_commands():
    """检查可用的Python命令"""
    print("=" * 60)
    print("检查Python命令")
    print("=" * 60)
    
    # 可能的Python命令
    python_commands = [
        'python', 'python3', 'python3.8', 'python3.9', 
        'python3.10', 'python3.11', 'python3.12', 'python3.13',
        'py', '/usr/bin/python3', '/usr/local/bin/python3'
    ]
    
    available_commands = []
    
    for cmd in python_commands:
        # 检查命令是否存在
        if shutil.which(cmd):
            try:
                result = subprocess.run([cmd, '--version'], 
                                      capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    version = result.stdout.strip() or result.stderr.strip()
                    available_commands.append((cmd, version))
                    print(f"[OK] {cmd}: {version}")
                else:
                    print(f"[FAIL] {cmd}: 命令存在但无法获取版本")
            except Exception as e:
                print(f"[FAIL] {cmd}: {e}")
        else:
            print(f"[NOT FOUND] {cmd}")
    
    return available_commands

def check_environment():
    """检查环境信息"""
    print("\n" + "=" * 60)
    print("环境信息")
    print("=" * 60)
    
    print(f"当前Python解释器: {sys.executable}")
    print(f"Python版本: {sys.version}")
    print(f"平台: {sys.platform}")
    print(f"工作目录: {os.getcwd()}")
    
    # 检查PATH环境变量
    path_env = os.environ.get('PATH', '')
    print(f"\nPATH环境变量:")
    for path in path_env.split(os.pathsep):
        if path.strip():
            print(f"  {path}")

def check_container_type():
    """检查容器类型"""
    print("\n" + "=" * 60)
    print("容器环境检查")
    print("=" * 60)
    
    # 检查是否在Docker容器中
    docker_indicators = [
        '/.dockerenv',
        '/proc/1/cgroup'
    ]
    
    in_docker = False
    for indicator in docker_indicators:
        if os.path.exists(indicator):
            print(f"[OK] 检测到Docker环境: {indicator}")
            in_docker = True
            break
    
    if not in_docker:
        print("[INFO] 未检测到明确的Docker环境标识")
    
    # 检查操作系统信息
    try:
        with open('/etc/os-release', 'r') as f:
            os_info = f.read()
            print(f"\n操作系统信息:")
            for line in os_info.split('\n'):
                if line.strip() and '=' in line:
                    print(f"  {line}")
    except FileNotFoundError:
        print("[INFO] 无法读取 /etc/os-release")
    except Exception as e:
        print(f"[WARN] 读取系统信息时出错: {e}")

def install_python_if_needed():
    """尝试安装Python（如果需要且可能）"""
    print("\n" + "=" * 60)
    print("Python安装检查")
    print("=" * 60)
    
    # 检查包管理器
    package_managers = ['apt-get', 'yum', 'apk', 'dnf', 'zypper']
    available_pm = None
    
    for pm in package_managers:
        if shutil.which(pm):
            available_pm = pm
            print(f"[OK] 找到包管理器: {pm}")
            break
    
    if not available_pm:
        print("[INFO] 未找到常见的包管理器")
        return False
    
    # 检查是否有sudo权限
    try:
        result = subprocess.run(['sudo', '-n', 'true'], 
                              capture_output=True, timeout=5)
        has_sudo = result.returncode == 0
    except:
        has_sudo = False
    
    print(f"[INFO] sudo权限: {'可用' if has_sudo else '不可用'}")
    
    if not has_sudo:
        print("[WARN] 没有sudo权限，无法安装软件包")
        return False
    
    # 提供安装建议
    if available_pm == 'apt-get':
        install_cmd = "sudo apt-get update && sudo apt-get install -y python3 python3-pip"
    elif available_pm == 'yum':
        install_cmd = "sudo yum install -y python3 python3-pip"
    elif available_pm == 'apk':
        install_cmd = "sudo apk add python3 py3-pip"
    else:
        install_cmd = f"sudo {available_pm} install python3"
    
    print(f"\n建议的安装命令:")
    print(f"  {install_cmd}")
    
    return True

def create_python_alias():
    """创建Python别名"""
    print("\n" + "=" * 60)
    print("创建Python别名")
    print("=" * 60)
    
    # 查找可用的python3命令
    python3_path = shutil.which('python3')
    if python3_path:
        print(f"[OK] 找到python3: {python3_path}")
        
        # 创建别名脚本
        alias_script = '''#!/bin/bash
# Python别名脚本
# 将python命令指向python3

# 添加到当前shell
alias python=python3
alias pip=pip3

# 显示信息
echo "已创建Python别名:"
echo "  python -> python3"
echo "  pip -> pip3"
echo ""
echo "要永久生效，请将以下内容添加到 ~/.bashrc 或 ~/.profile:"
echo "alias python=python3"
echo "alias pip=pip3"
'''
        
        with open('setup_python_alias.sh', 'w') as f:
            f.write(alias_script)
        
        os.chmod('setup_python_alias.sh', 0o755)
        print("[OK] 创建了别名脚本: setup_python_alias.sh")
        
        return True
    else:
        print("[FAIL] 未找到python3命令")
        return False

def main():
    """主函数"""
    print("Docker容器Python环境检查")
    print(f"检查时间: {sys.version_info}")
    
    # 执行各项检查
    available_commands = check_python_commands()
    check_environment()
    check_container_type()
    
    # 如果没有找到python命令，尝试解决
    python_available = any(cmd[0] == 'python' for cmd in available_commands)
    python3_available = any('python3' in cmd[0] for cmd in available_commands)
    
    print("\n" + "=" * 60)
    print("解决方案")
    print("=" * 60)
    
    if python_available:
        print("[OK] python命令可用")
    elif python3_available:
        print("[INFO] 只有python3可用，建议创建别名")
        create_python_alias()
        print("\n临时解决方案:")
        print("  在终端中运行: alias python=python3")
        print("  然后就可以使用: python hello_world.py")
    else:
        print("[WARN] 未找到Python命令")
        install_python_if_needed()
    
    # 测试运行hello_world.py
    if os.path.exists('hello_world.py'):
        print(f"\n测试运行 hello_world.py:")
        for cmd, version in available_commands:
            if 'python' in cmd:
                try:
                    result = subprocess.run([cmd, 'hello_world.py'], 
                                          capture_output=True, text=True, timeout=10)
                    if result.returncode == 0:
                        print(f"[OK] {cmd} hello_world.py")
                        print(f"     输出: {result.stdout.strip()}")
                        break
                    else:
                        print(f"[FAIL] {cmd} hello_world.py: {result.stderr.strip()}")
                except Exception as e:
                    print(f"[FAIL] {cmd} hello_world.py: {e}")

if __name__ == "__main__":
    main()
