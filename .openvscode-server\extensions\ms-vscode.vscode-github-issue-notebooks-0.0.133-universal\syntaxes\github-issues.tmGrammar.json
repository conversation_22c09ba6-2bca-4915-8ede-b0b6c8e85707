{"scopeName": "source.github-issues", "patterns": [{"name": "comment.line", "match": "//[^\r\n]*"}, {"name": "punctuation.quoted", "match": "\\\".*\\\""}, {"name": "variable.other", "match": "\\$[_a-zA-Z][_a-zA-Z0-9]+"}, {"name": "constant.language", "match": "\\b(?<![\\.\\$])(@me|pr|issue|title|body|comments|completed|open|closed|true|false|locked|unlocked|merged|unmerged|public|private|pending|success|failure|none|required|approved|changes_requested)(?!\\s*:)\\b"}, {"name": "constant.numeric.date", "match": "\\d\\d\\d\\d-\\d\\d-\\d\\d"}, {"name": "constant.numeric.datetime", "match": "\\d\\d\\d\\d-\\d\\d-\\d\\dT\\d\\d:\\d\\d:\\d\\dZ"}, {"name": "constant.numeric.sha", "match": "[a-fA-F0-9]{7,40}"}, {"name": "constant.numeric", "match": "\\d+"}, {"name": "keyword.other", "match": "\\b(type|updated|in|org|repo|user|state|assignee|author|mentions|team|stars|topics|pushed|size|commenter|involves|label|linked|milestone|project|language|comments|interactions|reactions|created|closed|archived|is|no|status|base|head|draft|reason|review-requested|review|reviewed-by|team-review-requested|merged)\\b"}]}