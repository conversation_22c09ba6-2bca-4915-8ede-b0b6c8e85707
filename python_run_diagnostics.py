#!/usr/bin/env python3
"""
VS Code Server Python运行诊断脚本
帮助诊断和解决在网页VS Code中运行Python文件的问题
"""

import sys
import os
import subprocess
import json
import shutil
from pathlib import Path
from datetime import datetime

def check_python_installation():
    """检查Python安装和配置"""
    print("=" * 60)
    print("Python安装检查")
    print("=" * 60)

    # 检查Python可执行文件
    python_paths = []

    # 检查常见的Python命令
    commands = ['python', 'python3', 'py']
    for cmd in commands:
        try:
            result = subprocess.run([cmd, '--version'],
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0:
                python_paths.append((cmd, result.stdout.strip()))
                print(f"[OK] {cmd}: {result.stdout.strip()}")
            else:
                print(f"[FAIL] {cmd}: 不可用")
        except Exception as e:
            print(f"[FAIL] {cmd}: {e}")

    # 检查当前Python解释器
    print(f"\n当前Python解释器:")
    print(f"  路径: {sys.executable}")
    print(f"  版本: {sys.version}")
    print(f"  平台: {sys.platform}")

    return len(python_paths) > 0

def check_vscode_python_extension():
    """检查VS Code Python扩展"""
    print("\n" + "=" * 60)
    print("VS Code Python扩展检查")
    print("=" * 60)

    # 检查扩展目录
    extensions_dir = Path("extensions")
    if extensions_dir.exists():
        python_ext_dir = extensions_dir / "python"
        if python_ext_dir.exists():
            print(f"[OK] Python扩展目录存在: {python_ext_dir}")

            # 检查扩展文件
            package_json = python_ext_dir / "package.json"
            if package_json.exists():
                try:
                    with open(package_json, 'r', encoding='utf-8') as f:
                        data = json.load(f)
                        print(f"[OK] Python扩展版本: {data.get('version', 'unknown')}")
                        print(f"[OK] 扩展名称: {data.get('displayName', 'unknown')}")
                except Exception as e:
                    print(f"[WARN] 无法读取扩展信息: {e}")
            else:
                print(f"[WARN] 扩展package.json不存在")
        else:
            print(f"[FAIL] Python扩展目录不存在")
            return False
    else:
        print(f"[FAIL] 扩展目录不存在")
        return False

    return True

def check_workspace_settings():
    """检查工作区设置"""
    print("\n" + "=" * 60)
    print("工作区设置检查")
    print("=" * 60)

    # 检查.vscode目录
    vscode_dir = Path(".vscode")
    if not vscode_dir.exists():
        print("[INFO] .vscode目录不存在，创建基本配置...")
        vscode_dir.mkdir(exist_ok=True)

        # 创建settings.json
        settings = {
            "python.defaultInterpreterPath": sys.executable,
            "python.terminal.activateEnvironment": True,
            "python.linting.enabled": True,
            "python.linting.pylintEnabled": False,
            "python.linting.flake8Enabled": False,
            "files.autoSave": "afterDelay",
            "files.autoSaveDelay": 1000
        }

        settings_file = vscode_dir / "settings.json"
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(settings, f, indent=2)
        print(f"[OK] 创建settings.json: {settings_file}")

        # 创建launch.json
        launch_config = {
            "version": "0.2.0",
            "configurations": [
                {
                    "name": "Python: Current File",
                    "type": "python",
                    "request": "launch",
                    "program": "${file}",
                    "console": "integratedTerminal",
                    "cwd": "${workspaceFolder}"
                }
            ]
        }

        launch_file = vscode_dir / "launch.json"
        with open(launch_file, 'w', encoding='utf-8') as f:
            json.dump(launch_config, f, indent=2)
        print(f"[OK] 创建launch.json: {launch_file}")

    else:
        print(f"[OK] .vscode目录存在")

        # 检查现有配置
        settings_file = vscode_dir / "settings.json"
        if settings_file.exists():
            try:
                with open(settings_file, 'r', encoding='utf-8') as f:
                    settings = json.load(f)
                    python_path = settings.get('python.defaultInterpreterPath')
                    print(f"[OK] Python解释器路径: {python_path}")
            except Exception as e:
                print(f"[WARN] 无法读取settings.json: {e}")
        else:
            print(f"[INFO] settings.json不存在")

    return True

def test_python_execution():
    """测试Python文件执行"""
    print("\n" + "=" * 60)
    print("Python文件执行测试")
    print("=" * 60)

    # 测试hello_world.py
    hello_file = Path("hello_world.py")
    if hello_file.exists():
        print(f"[OK] 找到hello_world.py文件")

        # 尝试不同方式执行
        execution_methods = [
            ("python hello_world.py", ["python", "hello_world.py"]),
            ("python3 hello_world.py", ["python3", "hello_world.py"]),
            (f"{sys.executable} hello_world.py", [sys.executable, "hello_world.py"])
        ]

        for method_name, command in execution_methods:
            try:
                print(f"\n测试执行方法: {method_name}")
                result = subprocess.run(command,
                                      capture_output=True, text=True,
                                      timeout=10, cwd=os.getcwd())

                if result.returncode == 0:
                    print(f"[OK] 执行成功")
                    print(f"     输出: {result.stdout.strip()}")
                else:
                    print(f"[FAIL] 执行失败 (退出码: {result.returncode})")
                    if result.stderr:
                        print(f"     错误: {result.stderr.strip()}")

            except subprocess.TimeoutExpired:
                print(f"[FAIL] 执行超时")
            except FileNotFoundError:
                print(f"[FAIL] 命令不存在")
            except Exception as e:
                print(f"[FAIL] 执行异常: {e}")
    else:
        print(f"[FAIL] hello_world.py文件不存在")
        return False

    return True

def create_test_files():
    """创建测试文件"""
    print("\n" + "=" * 60)
    print("创建测试文件")
    print("=" * 60)

    # 创建一个更复杂的测试文件
    test_content = '''#!/usr/bin/env python3
"""
VS Code Server Python测试文件
"""

import sys
import os
from datetime import datetime

def main():
    print("=" * 50)
    print("Python测试程序")
    print("=" * 50)
    print(f"执行时间: {datetime.now()}")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print(f"脚本路径: {__file__}")

    # 测试基本功能
    numbers = [1, 2, 3, 4, 5]
    result = sum(numbers)
    print(f"数字求和: {numbers} = {result}")

    # 测试文件操作
    test_file = "test_output.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(f"测试输出 - {datetime.now()}\\n")
        f.write("Python在VS Code Server中运行正常！\\n")

    print(f"创建测试文件: {test_file}")

    # 读取并显示文件内容
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(f"文件内容:\\n{content}")

    print("=" * 50)
    print("测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
'''

    test_file = Path("python_test.py")
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(test_content)

    print(f"[OK] 创建测试文件: {test_file}")

    # 测试执行
    try:
        result = subprocess.run([sys.executable, str(test_file)],
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"[OK] 测试文件执行成功")
            print("输出:")
            for line in result.stdout.split('\n'):
                if line.strip():
                    print(f"  {line}")
        else:
            print(f"[FAIL] 测试文件执行失败")
            if result.stderr:
                print(f"错误: {result.stderr}")
    except Exception as e:
        print(f"[FAIL] 测试文件执行异常: {e}")

    return True

def provide_solutions():
    """提供解决方案"""
    print("\n" + "=" * 60)
    print("解决方案和建议")
    print("=" * 60)

    solutions = [
        "1. 确保Python扩展已安装并启用",
        "2. 检查Python解释器路径设置",
        "3. 使用集成终端运行Python文件",
        "4. 配置正确的launch.json文件",
        "5. 检查工作区设置中的Python配置"
    ]

    print("常见解决方案:")
    for solution in solutions:
        print(f"  {solution}")

    print(f"\n在VS Code Server中运行Python的方法:")
    print(f"  方法1: 打开集成终端 (Ctrl+`) 然后运行:")
    print(f"         python hello_world.py")
    print(f"  方法2: 右键点击Python文件，选择 'Run Python File in Terminal'")
    print(f"  方法3: 使用F5键启动调试模式")
    print(f"  方法4: 使用Ctrl+F5运行而不调试")

    print(f"\n如果仍有问题，请检查:")
    print(f"  - VS Code Server是否正确加载了Python扩展")
    print(f"  - 浏览器控制台是否有错误信息")
    print(f"  - 网络连接是否稳定")

def main():
    """主诊断函数"""
    print("VS Code Server Python运行问题诊断")
    print(f"诊断时间: {datetime.now()}")
    print(f"工作目录: {os.getcwd()}")

    # 执行各项检查
    checks = [
        ("Python安装", check_python_installation),
        ("VS Code Python扩展", check_vscode_python_extension),
        ("工作区设置", check_workspace_settings),
        ("Python执行测试", test_python_execution),
        ("创建测试文件", create_test_files),
    ]

    results = []
    for check_name, check_func in checks:
        try:
            result = check_func()
            results.append((check_name, result))
        except Exception as e:
            print(f"[ERROR] {check_name}检查异常: {e}")
            results.append((check_name, False))

    # 显示检查结果
    print("\n" + "=" * 60)
    print("诊断结果总结")
    print("=" * 60)

    passed = 0
    for check_name, result in results:
        status = "通过" if result else "失败"
        print(f"{check_name}: {status}")
        if result:
            passed += 1

    print(f"\n检查通过: {passed}/{len(results)}")

    # 提供解决方案
    provide_solutions()

    return passed == len(results)

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n诊断被中断")
        sys.exit(1)
    except Exception as e:
        print(f"\n诊断发生错误: {e}")
        sys.exit(1)
