// node_modules/preact/dist/preact.module.js
var n;
var l;
var u;
var i;
var t;
var r;
var o;
var f = {};
var e = [];
var c = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;
function s(n2, l2) {
  for (var u3 in l2) n2[u3] = l2[u3];
  return n2;
}
function a(n2) {
  var l2 = n2.parentNode;
  l2 && l2.removeChild(n2);
}
function v(n2, l2, u3) {
  var i3, t3 = arguments, r3 = {};
  for (i3 in l2) "key" !== i3 && "ref" !== i3 && (r3[i3] = l2[i3]);
  if (arguments.length > 3) for (u3 = [u3], i3 = 3; i3 < arguments.length; i3++) u3.push(t3[i3]);
  if (null != u3 && (r3.children = u3), "function" == typeof n2 && null != n2.defaultProps) for (i3 in n2.defaultProps) void 0 === r3[i3] && (r3[i3] = n2.defaultProps[i3]);
  return h(n2, r3, l2 && l2.key, l2 && l2.ref, null);
}
function h(l2, u3, i3, t3, r3) {
  var o3 = { type: l2, props: u3, key: i3, ref: t3, __k: null, __: null, __b: 0, __e: null, __d: void 0, __c: null, constructor: void 0, __v: r3 };
  return null == r3 && (o3.__v = o3), n.vnode && n.vnode(o3), o3;
}
function p(n2) {
  return n2.children;
}
function d(n2, l2) {
  this.props = n2, this.context = l2;
}
function _(n2, l2) {
  if (null == l2) return n2.__ ? _(n2.__, n2.__.__k.indexOf(n2) + 1) : null;
  for (var u3; l2 < n2.__k.length; l2++) if (null != (u3 = n2.__k[l2]) && null != u3.__e) return u3.__e;
  return "function" == typeof n2.type ? _(n2) : null;
}
function k(n2) {
  var l2, u3;
  if (null != (n2 = n2.__) && null != n2.__c) {
    for (n2.__e = n2.__c.base = null, l2 = 0; l2 < n2.__k.length; l2++) if (null != (u3 = n2.__k[l2]) && null != u3.__e) {
      n2.__e = n2.__c.base = u3.__e;
      break;
    }
    return k(n2);
  }
}
function w(l2) {
  (!l2.__d && (l2.__d = true) && u.push(l2) && !m.__r++ || t !== n.debounceRendering) && ((t = n.debounceRendering) || i)(m);
}
function m() {
  for (var n2; m.__r = u.length; ) n2 = u.sort(function(n3, l2) {
    return n3.__v.__b - l2.__v.__b;
  }), u = [], n2.some(function(n3) {
    var l2, u3, i3, t3, r3, o3, f3;
    n3.__d && (o3 = (r3 = (l2 = n3).__v).__e, (f3 = l2.__P) && (u3 = [], (i3 = s({}, r3)).__v = i3, t3 = T(f3, r3, i3, l2.__n, void 0 !== f3.ownerSVGElement, null, u3, null == o3 ? _(r3) : o3), $(u3, r3), t3 != o3 && k(r3)));
  });
}
function g(n2, l2, u3, i3, t3, r3, o3, c3, s2, v3) {
  var y2, d2, k3, w2, m3, g3, b2, A2 = i3 && i3.__k || e, P2 = A2.length;
  for (s2 == f && (s2 = null != o3 ? o3[0] : P2 ? _(i3, 0) : null), u3.__k = [], y2 = 0; y2 < l2.length; y2++) if (null != (w2 = u3.__k[y2] = null == (w2 = l2[y2]) || "boolean" == typeof w2 ? null : "string" == typeof w2 || "number" == typeof w2 ? h(null, w2, null, null, w2) : Array.isArray(w2) ? h(p, { children: w2 }, null, null, null) : null != w2.__e || null != w2.__c ? h(w2.type, w2.props, w2.key, null, w2.__v) : w2)) {
    if (w2.__ = u3, w2.__b = u3.__b + 1, null === (k3 = A2[y2]) || k3 && w2.key == k3.key && w2.type === k3.type) A2[y2] = void 0;
    else for (d2 = 0; d2 < P2; d2++) {
      if ((k3 = A2[d2]) && w2.key == k3.key && w2.type === k3.type) {
        A2[d2] = void 0;
        break;
      }
      k3 = null;
    }
    m3 = T(n2, w2, k3 = k3 || f, t3, r3, o3, c3, s2, v3), (d2 = w2.ref) && k3.ref != d2 && (b2 || (b2 = []), k3.ref && b2.push(k3.ref, null, w2), b2.push(d2, w2.__c || m3, w2)), null != m3 ? (null == g3 && (g3 = m3), s2 = x(n2, w2, k3, A2, o3, m3, s2), "option" == u3.type ? n2.value = "" : "function" == typeof u3.type && (u3.__d = s2)) : s2 && k3.__e == s2 && s2.parentNode != n2 && (s2 = _(k3));
  }
  if (u3.__e = g3, null != o3 && "function" != typeof u3.type) for (y2 = o3.length; y2--; ) null != o3[y2] && a(o3[y2]);
  for (y2 = P2; y2--; ) null != A2[y2] && I(A2[y2], A2[y2]);
  if (b2) for (y2 = 0; y2 < b2.length; y2++) H(b2[y2], b2[++y2], b2[++y2]);
}
function x(n2, l2, u3, i3, t3, r3, o3) {
  var f3, e3, c3;
  if (void 0 !== l2.__d) f3 = l2.__d, l2.__d = void 0;
  else if (t3 == u3 || r3 != o3 || null == r3.parentNode) n: if (null == o3 || o3.parentNode !== n2) n2.appendChild(r3), f3 = null;
  else {
    for (e3 = o3, c3 = 0; (e3 = e3.nextSibling) && c3 < i3.length; c3 += 2) if (e3 == r3) break n;
    n2.insertBefore(r3, o3), f3 = o3;
  }
  return void 0 !== f3 ? f3 : r3.nextSibling;
}
function A(n2, l2, u3, i3, t3) {
  var r3;
  for (r3 in u3) "children" === r3 || "key" === r3 || r3 in l2 || C(n2, r3, null, u3[r3], i3);
  for (r3 in l2) t3 && "function" != typeof l2[r3] || "children" === r3 || "key" === r3 || "value" === r3 || "checked" === r3 || u3[r3] === l2[r3] || C(n2, r3, l2[r3], u3[r3], i3);
}
function P(n2, l2, u3) {
  "-" === l2[0] ? n2.setProperty(l2, u3) : n2[l2] = "number" == typeof u3 && false === c.test(l2) ? u3 + "px" : null == u3 ? "" : u3;
}
function C(n2, l2, u3, i3, t3) {
  var r3, o3, f3, e3, c3;
  if (t3 ? "className" === l2 && (l2 = "class") : "class" === l2 && (l2 = "className"), "style" === l2) if (r3 = n2.style, "string" == typeof u3) r3.cssText = u3;
  else {
    if ("string" == typeof i3 && (r3.cssText = "", i3 = null), i3) for (e3 in i3) u3 && e3 in u3 || P(r3, e3, "");
    if (u3) for (c3 in u3) i3 && u3[c3] === i3[c3] || P(r3, c3, u3[c3]);
  }
  else "o" === l2[0] && "n" === l2[1] ? (o3 = l2 !== (l2 = l2.replace(/Capture$/, "")), f3 = l2.toLowerCase(), l2 = (f3 in n2 ? f3 : l2).slice(2), u3 ? (i3 || n2.addEventListener(l2, N, o3), (n2.l || (n2.l = {}))[l2] = u3) : n2.removeEventListener(l2, N, o3)) : "list" !== l2 && "tagName" !== l2 && "form" !== l2 && "type" !== l2 && "size" !== l2 && !t3 && l2 in n2 ? n2[l2] = null == u3 ? "" : u3 : "function" != typeof u3 && "dangerouslySetInnerHTML" !== l2 && (l2 !== (l2 = l2.replace(/^xlink:?/, "")) ? null == u3 || false === u3 ? n2.removeAttributeNS("http://www.w3.org/1999/xlink", l2.toLowerCase()) : n2.setAttributeNS("http://www.w3.org/1999/xlink", l2.toLowerCase(), u3) : null == u3 || false === u3 && !/^ar/.test(l2) ? n2.removeAttribute(l2) : n2.setAttribute(l2, u3));
}
function N(l2) {
  this.l[l2.type](n.event ? n.event(l2) : l2);
}
function z(n2, l2, u3) {
  var i3, t3;
  for (i3 = 0; i3 < n2.__k.length; i3++) (t3 = n2.__k[i3]) && (t3.__ = n2, t3.__e && ("function" == typeof t3.type && t3.__k.length > 1 && z(t3, l2, u3), l2 = x(u3, t3, t3, n2.__k, null, t3.__e, l2), "function" == typeof n2.type && (n2.__d = l2)));
}
function T(l2, u3, i3, t3, r3, o3, f3, e3, c3) {
  var a3, v3, h2, y2, _3, k3, w2, m3, b2, x3, A2, P2 = u3.type;
  if (void 0 !== u3.constructor) return null;
  (a3 = n.__b) && a3(u3);
  try {
    n: if ("function" == typeof P2) {
      if (m3 = u3.props, b2 = (a3 = P2.contextType) && t3[a3.__c], x3 = a3 ? b2 ? b2.props.value : a3.__ : t3, i3.__c ? w2 = (v3 = u3.__c = i3.__c).__ = v3.__E : ("prototype" in P2 && P2.prototype.render ? u3.__c = v3 = new P2(m3, x3) : (u3.__c = v3 = new d(m3, x3), v3.constructor = P2, v3.render = L), b2 && b2.sub(v3), v3.props = m3, v3.state || (v3.state = {}), v3.context = x3, v3.__n = t3, h2 = v3.__d = true, v3.__h = []), null == v3.__s && (v3.__s = v3.state), null != P2.getDerivedStateFromProps && (v3.__s == v3.state && (v3.__s = s({}, v3.__s)), s(v3.__s, P2.getDerivedStateFromProps(m3, v3.__s))), y2 = v3.props, _3 = v3.state, h2) null == P2.getDerivedStateFromProps && null != v3.componentWillMount && v3.componentWillMount(), null != v3.componentDidMount && v3.__h.push(v3.componentDidMount);
      else {
        if (null == P2.getDerivedStateFromProps && m3 !== y2 && null != v3.componentWillReceiveProps && v3.componentWillReceiveProps(m3, x3), !v3.__e && null != v3.shouldComponentUpdate && false === v3.shouldComponentUpdate(m3, v3.__s, x3) || u3.__v === i3.__v) {
          v3.props = m3, v3.state = v3.__s, u3.__v !== i3.__v && (v3.__d = false), v3.__v = u3, u3.__e = i3.__e, u3.__k = i3.__k, v3.__h.length && f3.push(v3), z(u3, e3, l2);
          break n;
        }
        null != v3.componentWillUpdate && v3.componentWillUpdate(m3, v3.__s, x3), null != v3.componentDidUpdate && v3.__h.push(function() {
          v3.componentDidUpdate(y2, _3, k3);
        });
      }
      v3.context = x3, v3.props = m3, v3.state = v3.__s, (a3 = n.__r) && a3(u3), v3.__d = false, v3.__v = u3, v3.__P = l2, a3 = v3.render(v3.props, v3.state, v3.context), v3.state = v3.__s, null != v3.getChildContext && (t3 = s(s({}, t3), v3.getChildContext())), h2 || null == v3.getSnapshotBeforeUpdate || (k3 = v3.getSnapshotBeforeUpdate(y2, _3)), A2 = null != a3 && a3.type == p && null == a3.key ? a3.props.children : a3, g(l2, Array.isArray(A2) ? A2 : [A2], u3, i3, t3, r3, o3, f3, e3, c3), v3.base = u3.__e, v3.__h.length && f3.push(v3), w2 && (v3.__E = v3.__ = null), v3.__e = false;
    } else null == o3 && u3.__v === i3.__v ? (u3.__k = i3.__k, u3.__e = i3.__e) : u3.__e = j(i3.__e, u3, i3, t3, r3, o3, f3, c3);
    (a3 = n.diffed) && a3(u3);
  } catch (l3) {
    u3.__v = null, n.__e(l3, u3, i3);
  }
  return u3.__e;
}
function $(l2, u3) {
  n.__c && n.__c(u3, l2), l2.some(function(u4) {
    try {
      l2 = u4.__h, u4.__h = [], l2.some(function(n2) {
        n2.call(u4);
      });
    } catch (l3) {
      n.__e(l3, u4.__v);
    }
  });
}
function j(n2, l2, u3, i3, t3, r3, o3, c3) {
  var s2, a3, v3, h2, y2, p3 = u3.props, d2 = l2.props;
  if (t3 = "svg" === l2.type || t3, null != r3) {
    for (s2 = 0; s2 < r3.length; s2++) if (null != (a3 = r3[s2]) && ((null === l2.type ? 3 === a3.nodeType : a3.localName === l2.type) || n2 == a3)) {
      n2 = a3, r3[s2] = null;
      break;
    }
  }
  if (null == n2) {
    if (null === l2.type) return document.createTextNode(d2);
    n2 = t3 ? document.createElementNS("http://www.w3.org/2000/svg", l2.type) : document.createElement(l2.type, d2.is && { is: d2.is }), r3 = null, c3 = false;
  }
  if (null === l2.type) p3 !== d2 && n2.data != d2 && (n2.data = d2);
  else {
    if (null != r3 && (r3 = e.slice.call(n2.childNodes)), v3 = (p3 = u3.props || f).dangerouslySetInnerHTML, h2 = d2.dangerouslySetInnerHTML, !c3) {
      if (null != r3) for (p3 = {}, y2 = 0; y2 < n2.attributes.length; y2++) p3[n2.attributes[y2].name] = n2.attributes[y2].value;
      (h2 || v3) && (h2 && v3 && h2.__html == v3.__html || (n2.innerHTML = h2 && h2.__html || ""));
    }
    A(n2, d2, p3, t3, c3), h2 ? l2.__k = [] : (s2 = l2.props.children, g(n2, Array.isArray(s2) ? s2 : [s2], l2, u3, i3, "foreignObject" !== l2.type && t3, r3, o3, f, c3)), c3 || ("value" in d2 && void 0 !== (s2 = d2.value) && s2 !== n2.value && C(n2, "value", s2, p3.value, false), "checked" in d2 && void 0 !== (s2 = d2.checked) && s2 !== n2.checked && C(n2, "checked", s2, p3.checked, false));
  }
  return n2;
}
function H(l2, u3, i3) {
  try {
    "function" == typeof l2 ? l2(u3) : l2.current = u3;
  } catch (l3) {
    n.__e(l3, i3);
  }
}
function I(l2, u3, i3) {
  var t3, r3, o3;
  if (n.unmount && n.unmount(l2), (t3 = l2.ref) && (t3.current && t3.current !== l2.__e || H(t3, null, u3)), i3 || "function" == typeof l2.type || (i3 = null != (r3 = l2.__e)), l2.__e = l2.__d = void 0, null != (t3 = l2.__c)) {
    if (t3.componentWillUnmount) try {
      t3.componentWillUnmount();
    } catch (l3) {
      n.__e(l3, u3);
    }
    t3.base = t3.__P = null;
  }
  if (t3 = l2.__k) for (o3 = 0; o3 < t3.length; o3++) t3[o3] && I(t3[o3], u3, i3);
  null != r3 && a(r3);
}
function L(n2, l2, u3) {
  return this.constructor(n2, u3);
}
function M(l2, u3, i3) {
  var t3, o3, c3;
  n.__ && n.__(l2, u3), o3 = (t3 = i3 === r) ? null : i3 && i3.__k || u3.__k, l2 = v(p, null, [l2]), c3 = [], T(u3, (t3 ? u3 : i3 || u3).__k = l2, o3 || f, f, void 0 !== u3.ownerSVGElement, i3 && !t3 ? [i3] : o3 ? null : u3.childNodes.length ? e.slice.call(u3.childNodes) : null, c3, i3 || f, t3), $(c3, l2);
}
n = { __e: function(n2, l2) {
  for (var u3, i3; l2 = l2.__; ) if ((u3 = l2.__c) && !u3.__) try {
    if (u3.constructor && null != u3.constructor.getDerivedStateFromError && (i3 = true, u3.setState(u3.constructor.getDerivedStateFromError(n2))), null != u3.componentDidCatch && (i3 = true, u3.componentDidCatch(n2)), i3) return w(u3.__E = u3);
  } catch (l3) {
    n2 = l3;
  }
  throw n2;
} }, l = function(n2) {
  return null != n2 && void 0 === n2.constructor;
}, d.prototype.setState = function(n2, l2) {
  var u3;
  u3 = this.__s !== this.state ? this.__s : this.__s = s({}, this.state), "function" == typeof n2 && (n2 = n2(u3, this.props)), n2 && s(u3, n2), null != n2 && this.__v && (l2 && this.__h.push(l2), w(this));
}, d.prototype.forceUpdate = function(n2) {
  this.__v && (this.__e = true, n2 && this.__h.push(n2), w(this));
}, d.prototype.render = p, u = [], i = "function" == typeof Promise ? Promise.prototype.then.bind(Promise.resolve()) : setTimeout, m.__r = 0, r = f, o = 0;

// node_modules/preact/hooks/dist/hooks.module.js
var t2;
var u2;
var r2;
var o2 = 0;
var i2 = [];
var c2 = n.__r;
var f2 = n.diffed;
var e2 = n.__c;
var a2 = n.unmount;
function v2(t3, r3) {
  n.__h && n.__h(u2, t3, o2 || r3), o2 = 0;
  var i3 = u2.__H || (u2.__H = { __: [], __h: [] });
  return t3 >= i3.__.length && i3.__.push({}), i3.__[t3];
}
function m2(n2) {
  return o2 = 1, p2(k2, n2);
}
function p2(n2, r3, o3) {
  var i3 = v2(t2++, 2);
  return i3.t = n2, i3.__c || (i3.__c = u2, i3.__ = [o3 ? o3(r3) : k2(void 0, r3), function(n3) {
    var t3 = i3.t(i3.__[0], n3);
    i3.__[0] !== t3 && (i3.__ = [t3, i3.__[1]], i3.__c.setState({}));
  }]), i3.__;
}
function y(r3, o3) {
  var i3 = v2(t2++, 3);
  !n.__s && j2(i3.__H, o3) && (i3.__ = r3, i3.__H = o3, u2.__H.__h.push(i3));
}
function _2(n2, u3) {
  var r3 = v2(t2++, 7);
  return j2(r3.__H, u3) ? (r3.__H = u3, r3.__h = n2, r3.__ = n2()) : r3.__;
}
function q() {
  i2.some(function(t3) {
    if (t3.__P) try {
      t3.__H.__h.forEach(b), t3.__H.__h.forEach(g2), t3.__H.__h = [];
    } catch (u3) {
      return t3.__H.__h = [], n.__e(u3, t3.__v), true;
    }
  }), i2 = [];
}
n.__r = function(n2) {
  c2 && c2(n2), t2 = 0;
  var r3 = (u2 = n2.__c).__H;
  r3 && (r3.__h.forEach(b), r3.__h.forEach(g2), r3.__h = []);
}, n.diffed = function(t3) {
  f2 && f2(t3);
  var u3 = t3.__c;
  u3 && u3.__H && u3.__H.__h.length && (1 !== i2.push(u3) && r2 === n.requestAnimationFrame || ((r2 = n.requestAnimationFrame) || function(n2) {
    var t4, u4 = function() {
      clearTimeout(r3), x2 && cancelAnimationFrame(t4), setTimeout(n2);
    }, r3 = setTimeout(u4, 100);
    x2 && (t4 = requestAnimationFrame(u4));
  })(q));
}, n.__c = function(t3, u3) {
  u3.some(function(t4) {
    try {
      t4.__h.forEach(b), t4.__h = t4.__h.filter(function(n2) {
        return !n2.__ || g2(n2);
      });
    } catch (r3) {
      u3.some(function(n2) {
        n2.__h && (n2.__h = []);
      }), u3 = [], n.__e(r3, t4.__v);
    }
  }), e2 && e2(t3, u3);
}, n.unmount = function(t3) {
  a2 && a2(t3);
  var u3 = t3.__c;
  if (u3 && u3.__H) try {
    u3.__H.__.forEach(b);
  } catch (t4) {
    n.__e(t4, u3.__v);
  }
};
var x2 = "function" == typeof requestAnimationFrame;
function b(n2) {
  "function" == typeof n2.u && n2.u();
}
function g2(n2) {
  n2.u = n2.__();
}
function j2(n2, t3) {
  return !n2 || t3.some(function(t4, u3) {
    return t4 !== n2[u3];
  });
}
function k2(n2, t3) {
  return "function" == typeof t3 ? t3(n2) : t3;
}

// src/common/emoji.ts
var _emojiMap = JSON.parse(`{
  "100": "\u{1F4AF}",
  "1234": "\u{1F522}",
  "grinning": "\u{1F600}",
  "smiley": "\u{1F603}",
  "smile": "\u{1F604}",
  "grin": "\u{1F601}",
  "laughing": "\u{1F606}",
  "satisfied": "\u{1F606}",
  "sweat_smile": "\u{1F605}",
  "rofl": "\u{1F923}",
  "joy": "\u{1F602}",
  "slightly_smiling_face": "\u{1F642}",
  "upside_down_face": "\u{1F643}",
  "wink": "\u{1F609}",
  "blush": "\u{1F60A}",
  "innocent": "\u{1F607}",
  "smiling_face_with_three_hearts": "\u{1F970}",
  "heart_eyes": "\u{1F60D}",
  "star_struck": "\u{1F929}",
  "kissing_heart": "\u{1F618}",
  "kissing": "\u{1F617}",
  "relaxed": "\u263A\uFE0F",
  "kissing_closed_eyes": "\u{1F61A}",
  "kissing_smiling_eyes": "\u{1F619}",
  "smiling_face_with_tear": "\u{1F972}",
  "yum": "\u{1F60B}",
  "stuck_out_tongue": "\u{1F61B}",
  "stuck_out_tongue_winking_eye": "\u{1F61C}",
  "zany_face": "\u{1F92A}",
  "stuck_out_tongue_closed_eyes": "\u{1F61D}",
  "money_mouth_face": "\u{1F911}",
  "hugs": "\u{1F917}",
  "hand_over_mouth": "\u{1F92D}",
  "shushing_face": "\u{1F92B}",
  "thinking": "\u{1F914}",
  "zipper_mouth_face": "\u{1F910}",
  "raised_eyebrow": "\u{1F928}",
  "neutral_face": "\u{1F610}",
  "expressionless": "\u{1F611}",
  "no_mouth": "\u{1F636}",
  "smirk": "\u{1F60F}",
  "unamused": "\u{1F612}",
  "roll_eyes": "\u{1F644}",
  "grimacing": "\u{1F62C}",
  "lying_face": "\u{1F925}",
  "relieved": "\u{1F60C}",
  "pensive": "\u{1F614}",
  "sleepy": "\u{1F62A}",
  "drooling_face": "\u{1F924}",
  "sleeping": "\u{1F634}",
  "mask": "\u{1F637}",
  "face_with_thermometer": "\u{1F912}",
  "face_with_head_bandage": "\u{1F915}",
  "nauseated_face": "\u{1F922}",
  "vomiting_face": "\u{1F92E}",
  "sneezing_face": "\u{1F927}",
  "hot_face": "\u{1F975}",
  "cold_face": "\u{1F976}",
  "woozy_face": "\u{1F974}",
  "dizzy_face": "\u{1F635}",
  "exploding_head": "\u{1F92F}",
  "cowboy_hat_face": "\u{1F920}",
  "partying_face": "\u{1F973}",
  "disguised_face": "\u{1F978}",
  "sunglasses": "\u{1F60E}",
  "nerd_face": "\u{1F913}",
  "monocle_face": "\u{1F9D0}",
  "confused": "\u{1F615}",
  "worried": "\u{1F61F}",
  "slightly_frowning_face": "\u{1F641}",
  "frowning_face": "\u2639\uFE0F",
  "open_mouth": "\u{1F62E}",
  "hushed": "\u{1F62F}",
  "astonished": "\u{1F632}",
  "flushed": "\u{1F633}",
  "pleading_face": "\u{1F97A}",
  "frowning": "\u{1F626}",
  "anguished": "\u{1F627}",
  "fearful": "\u{1F628}",
  "cold_sweat": "\u{1F630}",
  "disappointed_relieved": "\u{1F625}",
  "cry": "\u{1F622}",
  "sob": "\u{1F62D}",
  "scream": "\u{1F631}",
  "confounded": "\u{1F616}",
  "persevere": "\u{1F623}",
  "disappointed": "\u{1F61E}",
  "sweat": "\u{1F613}",
  "weary": "\u{1F629}",
  "tired_face": "\u{1F62B}",
  "yawning_face": "\u{1F971}",
  "triumph": "\u{1F624}",
  "rage": "\u{1F621}",
  "pout": "\u{1F621}",
  "angry": "\u{1F620}",
  "cursing_face": "\u{1F92C}",
  "smiling_imp": "\u{1F608}",
  "imp": "\u{1F47F}",
  "skull": "\u{1F480}",
  "skull_and_crossbones": "\u2620\uFE0F",
  "hankey": "\u{1F4A9}",
  "poop": "\u{1F4A9}",
  "shit": "\u{1F4A9}",
  "clown_face": "\u{1F921}",
  "japanese_ogre": "\u{1F479}",
  "japanese_goblin": "\u{1F47A}",
  "ghost": "\u{1F47B}",
  "alien": "\u{1F47D}",
  "space_invader": "\u{1F47E}",
  "robot": "\u{1F916}",
  "smiley_cat": "\u{1F63A}",
  "smile_cat": "\u{1F638}",
  "joy_cat": "\u{1F639}",
  "heart_eyes_cat": "\u{1F63B}",
  "smirk_cat": "\u{1F63C}",
  "kissing_cat": "\u{1F63D}",
  "scream_cat": "\u{1F640}",
  "crying_cat_face": "\u{1F63F}",
  "pouting_cat": "\u{1F63E}",
  "see_no_evil": "\u{1F648}",
  "hear_no_evil": "\u{1F649}",
  "speak_no_evil": "\u{1F64A}",
  "kiss": "\u{1F48B}",
  "love_letter": "\u{1F48C}",
  "cupid": "\u{1F498}",
  "gift_heart": "\u{1F49D}",
  "sparkling_heart": "\u{1F496}",
  "heartpulse": "\u{1F497}",
  "heartbeat": "\u{1F493}",
  "revolving_hearts": "\u{1F49E}",
  "two_hearts": "\u{1F495}",
  "heart_decoration": "\u{1F49F}",
  "heavy_heart_exclamation": "\u2763\uFE0F",
  "broken_heart": "\u{1F494}",
  "heart": "\u2764\uFE0F",
  "orange_heart": "\u{1F9E1}",
  "yellow_heart": "\u{1F49B}",
  "green_heart": "\u{1F49A}",
  "blue_heart": "\u{1F499}",
  "purple_heart": "\u{1F49C}",
  "brown_heart": "\u{1F90E}",
  "black_heart": "\u{1F5A4}",
  "white_heart": "\u{1F90D}",
  "anger": "\u{1F4A2}",
  "boom": "\u{1F4A5}",
  "collision": "\u{1F4A5}",
  "dizzy": "\u{1F4AB}",
  "sweat_drops": "\u{1F4A6}",
  "dash": "\u{1F4A8}",
  "hole": "\u{1F573}\uFE0F",
  "bomb": "\u{1F4A3}",
  "speech_balloon": "\u{1F4AC}",
  "eye_speech_bubble": "\u{1F441}\uFE0F\u200D\u{1F5E8}\uFE0F",
  "left_speech_bubble": "\u{1F5E8}\uFE0F",
  "right_anger_bubble": "\u{1F5EF}\uFE0F",
  "thought_balloon": "\u{1F4AD}",
  "zzz": "\u{1F4A4}",
  "wave": "\u{1F44B}",
  "raised_back_of_hand": "\u{1F91A}",
  "raised_hand_with_fingers_splayed": "\u{1F590}\uFE0F",
  "hand": "\u270B",
  "raised_hand": "\u270B",
  "vulcan_salute": "\u{1F596}",
  "ok_hand": "\u{1F44C}",
  "pinched_fingers": "\u{1F90C}",
  "pinching_hand": "\u{1F90F}",
  "v": "\u270C\uFE0F",
  "crossed_fingers": "\u{1F91E}",
  "love_you_gesture": "\u{1F91F}",
  "metal": "\u{1F918}",
  "call_me_hand": "\u{1F919}",
  "point_left": "\u{1F448}",
  "point_right": "\u{1F449}",
  "point_up_2": "\u{1F446}",
  "middle_finger": "\u{1F595}",
  "fu": "\u{1F595}",
  "point_down": "\u{1F447}",
  "point_up": "\u261D\uFE0F",
  "+1": "\u{1F44D}",
  "thumbsup": "\u{1F44D}",
  "-1": "\u{1F44E}",
  "thumbsdown": "\u{1F44E}",
  "fist_raised": "\u270A",
  "fist": "\u270A",
  "fist_oncoming": "\u{1F44A}",
  "facepunch": "\u{1F44A}",
  "punch": "\u{1F44A}",
  "fist_left": "\u{1F91B}",
  "fist_right": "\u{1F91C}",
  "clap": "\u{1F44F}",
  "raised_hands": "\u{1F64C}",
  "open_hands": "\u{1F450}",
  "palms_up_together": "\u{1F932}",
  "handshake": "\u{1F91D}",
  "pray": "\u{1F64F}",
  "writing_hand": "\u270D\uFE0F",
  "nail_care": "\u{1F485}",
  "selfie": "\u{1F933}",
  "muscle": "\u{1F4AA}",
  "mechanical_arm": "\u{1F9BE}",
  "mechanical_leg": "\u{1F9BF}",
  "leg": "\u{1F9B5}",
  "foot": "\u{1F9B6}",
  "ear": "\u{1F442}",
  "ear_with_hearing_aid": "\u{1F9BB}",
  "nose": "\u{1F443}",
  "brain": "\u{1F9E0}",
  "anatomical_heart": "\u{1FAC0}",
  "lungs": "\u{1FAC1}",
  "tooth": "\u{1F9B7}",
  "bone": "\u{1F9B4}",
  "eyes": "\u{1F440}",
  "eye": "\u{1F441}\uFE0F",
  "tongue": "\u{1F445}",
  "lips": "\u{1F444}",
  "baby": "\u{1F476}",
  "child": "\u{1F9D2}",
  "boy": "\u{1F466}",
  "girl": "\u{1F467}",
  "adult": "\u{1F9D1}",
  "blond_haired_person": "\u{1F471}",
  "man": "\u{1F468}",
  "bearded_person": "\u{1F9D4}",
  "red_haired_man": "\u{1F468}\u200D\u{1F9B0}",
  "curly_haired_man": "\u{1F468}\u200D\u{1F9B1}",
  "white_haired_man": "\u{1F468}\u200D\u{1F9B3}",
  "bald_man": "\u{1F468}\u200D\u{1F9B2}",
  "woman": "\u{1F469}",
  "red_haired_woman": "\u{1F469}\u200D\u{1F9B0}",
  "person_red_hair": "\u{1F9D1}\u200D\u{1F9B0}",
  "curly_haired_woman": "\u{1F469}\u200D\u{1F9B1}",
  "person_curly_hair": "\u{1F9D1}\u200D\u{1F9B1}",
  "white_haired_woman": "\u{1F469}\u200D\u{1F9B3}",
  "person_white_hair": "\u{1F9D1}\u200D\u{1F9B3}",
  "bald_woman": "\u{1F469}\u200D\u{1F9B2}",
  "person_bald": "\u{1F9D1}\u200D\u{1F9B2}",
  "blond_haired_woman": "\u{1F471}\u200D\u2640\uFE0F",
  "blonde_woman": "\u{1F471}\u200D\u2640\uFE0F",
  "blond_haired_man": "\u{1F471}\u200D\u2642\uFE0F",
  "older_adult": "\u{1F9D3}",
  "older_man": "\u{1F474}",
  "older_woman": "\u{1F475}",
  "frowning_person": "\u{1F64D}",
  "frowning_man": "\u{1F64D}\u200D\u2642\uFE0F",
  "frowning_woman": "\u{1F64D}\u200D\u2640\uFE0F",
  "pouting_face": "\u{1F64E}",
  "pouting_man": "\u{1F64E}\u200D\u2642\uFE0F",
  "pouting_woman": "\u{1F64E}\u200D\u2640\uFE0F",
  "no_good": "\u{1F645}",
  "no_good_man": "\u{1F645}\u200D\u2642\uFE0F",
  "ng_man": "\u{1F645}\u200D\u2642\uFE0F",
  "no_good_woman": "\u{1F645}\u200D\u2640\uFE0F",
  "ng_woman": "\u{1F645}\u200D\u2640\uFE0F",
  "ok_person": "\u{1F646}",
  "ok_man": "\u{1F646}\u200D\u2642\uFE0F",
  "ok_woman": "\u{1F646}\u200D\u2640\uFE0F",
  "tipping_hand_person": "\u{1F481}",
  "information_desk_person": "\u{1F481}",
  "tipping_hand_man": "\u{1F481}\u200D\u2642\uFE0F",
  "sassy_man": "\u{1F481}\u200D\u2642\uFE0F",
  "tipping_hand_woman": "\u{1F481}\u200D\u2640\uFE0F",
  "sassy_woman": "\u{1F481}\u200D\u2640\uFE0F",
  "raising_hand": "\u{1F64B}",
  "raising_hand_man": "\u{1F64B}\u200D\u2642\uFE0F",
  "raising_hand_woman": "\u{1F64B}\u200D\u2640\uFE0F",
  "deaf_person": "\u{1F9CF}",
  "deaf_man": "\u{1F9CF}\u200D\u2642\uFE0F",
  "deaf_woman": "\u{1F9CF}\u200D\u2640\uFE0F",
  "bow": "\u{1F647}",
  "bowing_man": "\u{1F647}\u200D\u2642\uFE0F",
  "bowing_woman": "\u{1F647}\u200D\u2640\uFE0F",
  "facepalm": "\u{1F926}",
  "man_facepalming": "\u{1F926}\u200D\u2642\uFE0F",
  "woman_facepalming": "\u{1F926}\u200D\u2640\uFE0F",
  "shrug": "\u{1F937}",
  "man_shrugging": "\u{1F937}\u200D\u2642\uFE0F",
  "woman_shrugging": "\u{1F937}\u200D\u2640\uFE0F",
  "health_worker": "\u{1F9D1}\u200D\u2695\uFE0F",
  "man_health_worker": "\u{1F468}\u200D\u2695\uFE0F",
  "woman_health_worker": "\u{1F469}\u200D\u2695\uFE0F",
  "student": "\u{1F9D1}\u200D\u{1F393}",
  "man_student": "\u{1F468}\u200D\u{1F393}",
  "woman_student": "\u{1F469}\u200D\u{1F393}",
  "teacher": "\u{1F9D1}\u200D\u{1F3EB}",
  "man_teacher": "\u{1F468}\u200D\u{1F3EB}",
  "woman_teacher": "\u{1F469}\u200D\u{1F3EB}",
  "judge": "\u{1F9D1}\u200D\u2696\uFE0F",
  "man_judge": "\u{1F468}\u200D\u2696\uFE0F",
  "woman_judge": "\u{1F469}\u200D\u2696\uFE0F",
  "farmer": "\u{1F9D1}\u200D\u{1F33E}",
  "man_farmer": "\u{1F468}\u200D\u{1F33E}",
  "woman_farmer": "\u{1F469}\u200D\u{1F33E}",
  "cook": "\u{1F9D1}\u200D\u{1F373}",
  "man_cook": "\u{1F468}\u200D\u{1F373}",
  "woman_cook": "\u{1F469}\u200D\u{1F373}",
  "mechanic": "\u{1F9D1}\u200D\u{1F527}",
  "man_mechanic": "\u{1F468}\u200D\u{1F527}",
  "woman_mechanic": "\u{1F469}\u200D\u{1F527}",
  "factory_worker": "\u{1F9D1}\u200D\u{1F3ED}",
  "man_factory_worker": "\u{1F468}\u200D\u{1F3ED}",
  "woman_factory_worker": "\u{1F469}\u200D\u{1F3ED}",
  "office_worker": "\u{1F9D1}\u200D\u{1F4BC}",
  "man_office_worker": "\u{1F468}\u200D\u{1F4BC}",
  "woman_office_worker": "\u{1F469}\u200D\u{1F4BC}",
  "scientist": "\u{1F9D1}\u200D\u{1F52C}",
  "man_scientist": "\u{1F468}\u200D\u{1F52C}",
  "woman_scientist": "\u{1F469}\u200D\u{1F52C}",
  "technologist": "\u{1F9D1}\u200D\u{1F4BB}",
  "man_technologist": "\u{1F468}\u200D\u{1F4BB}",
  "woman_technologist": "\u{1F469}\u200D\u{1F4BB}",
  "singer": "\u{1F9D1}\u200D\u{1F3A4}",
  "man_singer": "\u{1F468}\u200D\u{1F3A4}",
  "woman_singer": "\u{1F469}\u200D\u{1F3A4}",
  "artist": "\u{1F9D1}\u200D\u{1F3A8}",
  "man_artist": "\u{1F468}\u200D\u{1F3A8}",
  "woman_artist": "\u{1F469}\u200D\u{1F3A8}",
  "pilot": "\u{1F9D1}\u200D\u2708\uFE0F",
  "man_pilot": "\u{1F468}\u200D\u2708\uFE0F",
  "woman_pilot": "\u{1F469}\u200D\u2708\uFE0F",
  "astronaut": "\u{1F9D1}\u200D\u{1F680}",
  "man_astronaut": "\u{1F468}\u200D\u{1F680}",
  "woman_astronaut": "\u{1F469}\u200D\u{1F680}",
  "firefighter": "\u{1F9D1}\u200D\u{1F692}",
  "man_firefighter": "\u{1F468}\u200D\u{1F692}",
  "woman_firefighter": "\u{1F469}\u200D\u{1F692}",
  "police_officer": "\u{1F46E}",
  "cop": "\u{1F46E}",
  "policeman": "\u{1F46E}\u200D\u2642\uFE0F",
  "policewoman": "\u{1F46E}\u200D\u2640\uFE0F",
  "detective": "\u{1F575}\uFE0F",
  "male_detective": "\u{1F575}\uFE0F\u200D\u2642\uFE0F",
  "female_detective": "\u{1F575}\uFE0F\u200D\u2640\uFE0F",
  "guard": "\u{1F482}",
  "guardsman": "\u{1F482}\u200D\u2642\uFE0F",
  "guardswoman": "\u{1F482}\u200D\u2640\uFE0F",
  "ninja": "\u{1F977}",
  "construction_worker": "\u{1F477}",
  "construction_worker_man": "\u{1F477}\u200D\u2642\uFE0F",
  "construction_worker_woman": "\u{1F477}\u200D\u2640\uFE0F",
  "prince": "\u{1F934}",
  "princess": "\u{1F478}",
  "person_with_turban": "\u{1F473}",
  "man_with_turban": "\u{1F473}\u200D\u2642\uFE0F",
  "woman_with_turban": "\u{1F473}\u200D\u2640\uFE0F",
  "man_with_gua_pi_mao": "\u{1F472}",
  "woman_with_headscarf": "\u{1F9D5}",
  "person_in_tuxedo": "\u{1F935}",
  "man_in_tuxedo": "\u{1F935}\u200D\u2642\uFE0F",
  "woman_in_tuxedo": "\u{1F935}\u200D\u2640\uFE0F",
  "person_with_veil": "\u{1F470}",
  "man_with_veil": "\u{1F470}\u200D\u2642\uFE0F",
  "woman_with_veil": "\u{1F470}\u200D\u2640\uFE0F",
  "bride_with_veil": "\u{1F470}\u200D\u2640\uFE0F",
  "pregnant_woman": "\u{1F930}",
  "breast_feeding": "\u{1F931}",
  "woman_feeding_baby": "\u{1F469}\u200D\u{1F37C}",
  "man_feeding_baby": "\u{1F468}\u200D\u{1F37C}",
  "person_feeding_baby": "\u{1F9D1}\u200D\u{1F37C}",
  "angel": "\u{1F47C}",
  "santa": "\u{1F385}",
  "mrs_claus": "\u{1F936}",
  "mx_claus": "\u{1F9D1}\u200D\u{1F384}",
  "superhero": "\u{1F9B8}",
  "superhero_man": "\u{1F9B8}\u200D\u2642\uFE0F",
  "superhero_woman": "\u{1F9B8}\u200D\u2640\uFE0F",
  "supervillain": "\u{1F9B9}",
  "supervillain_man": "\u{1F9B9}\u200D\u2642\uFE0F",
  "supervillain_woman": "\u{1F9B9}\u200D\u2640\uFE0F",
  "mage": "\u{1F9D9}",
  "mage_man": "\u{1F9D9}\u200D\u2642\uFE0F",
  "mage_woman": "\u{1F9D9}\u200D\u2640\uFE0F",
  "fairy": "\u{1F9DA}",
  "fairy_man": "\u{1F9DA}\u200D\u2642\uFE0F",
  "fairy_woman": "\u{1F9DA}\u200D\u2640\uFE0F",
  "vampire": "\u{1F9DB}",
  "vampire_man": "\u{1F9DB}\u200D\u2642\uFE0F",
  "vampire_woman": "\u{1F9DB}\u200D\u2640\uFE0F",
  "merperson": "\u{1F9DC}",
  "merman": "\u{1F9DC}\u200D\u2642\uFE0F",
  "mermaid": "\u{1F9DC}\u200D\u2640\uFE0F",
  "elf": "\u{1F9DD}",
  "elf_man": "\u{1F9DD}\u200D\u2642\uFE0F",
  "elf_woman": "\u{1F9DD}\u200D\u2640\uFE0F",
  "genie": "\u{1F9DE}",
  "genie_man": "\u{1F9DE}\u200D\u2642\uFE0F",
  "genie_woman": "\u{1F9DE}\u200D\u2640\uFE0F",
  "zombie": "\u{1F9DF}",
  "zombie_man": "\u{1F9DF}\u200D\u2642\uFE0F",
  "zombie_woman": "\u{1F9DF}\u200D\u2640\uFE0F",
  "massage": "\u{1F486}",
  "massage_man": "\u{1F486}\u200D\u2642\uFE0F",
  "massage_woman": "\u{1F486}\u200D\u2640\uFE0F",
  "haircut": "\u{1F487}",
  "haircut_man": "\u{1F487}\u200D\u2642\uFE0F",
  "haircut_woman": "\u{1F487}\u200D\u2640\uFE0F",
  "walking": "\u{1F6B6}",
  "walking_man": "\u{1F6B6}\u200D\u2642\uFE0F",
  "walking_woman": "\u{1F6B6}\u200D\u2640\uFE0F",
  "standing_person": "\u{1F9CD}",
  "standing_man": "\u{1F9CD}\u200D\u2642\uFE0F",
  "standing_woman": "\u{1F9CD}\u200D\u2640\uFE0F",
  "kneeling_person": "\u{1F9CE}",
  "kneeling_man": "\u{1F9CE}\u200D\u2642\uFE0F",
  "kneeling_woman": "\u{1F9CE}\u200D\u2640\uFE0F",
  "person_with_probing_cane": "\u{1F9D1}\u200D\u{1F9AF}",
  "man_with_probing_cane": "\u{1F468}\u200D\u{1F9AF}",
  "woman_with_probing_cane": "\u{1F469}\u200D\u{1F9AF}",
  "person_in_motorized_wheelchair": "\u{1F9D1}\u200D\u{1F9BC}",
  "man_in_motorized_wheelchair": "\u{1F468}\u200D\u{1F9BC}",
  "woman_in_motorized_wheelchair": "\u{1F469}\u200D\u{1F9BC}",
  "person_in_manual_wheelchair": "\u{1F9D1}\u200D\u{1F9BD}",
  "man_in_manual_wheelchair": "\u{1F468}\u200D\u{1F9BD}",
  "woman_in_manual_wheelchair": "\u{1F469}\u200D\u{1F9BD}",
  "runner": "\u{1F3C3}",
  "running": "\u{1F3C3}",
  "running_man": "\u{1F3C3}\u200D\u2642\uFE0F",
  "running_woman": "\u{1F3C3}\u200D\u2640\uFE0F",
  "woman_dancing": "\u{1F483}",
  "dancer": "\u{1F483}",
  "man_dancing": "\u{1F57A}",
  "business_suit_levitating": "\u{1F574}\uFE0F",
  "dancers": "\u{1F46F}",
  "dancing_men": "\u{1F46F}\u200D\u2642\uFE0F",
  "dancing_women": "\u{1F46F}\u200D\u2640\uFE0F",
  "sauna_person": "\u{1F9D6}",
  "sauna_man": "\u{1F9D6}\u200D\u2642\uFE0F",
  "sauna_woman": "\u{1F9D6}\u200D\u2640\uFE0F",
  "climbing": "\u{1F9D7}",
  "climbing_man": "\u{1F9D7}\u200D\u2642\uFE0F",
  "climbing_woman": "\u{1F9D7}\u200D\u2640\uFE0F",
  "person_fencing": "\u{1F93A}",
  "horse_racing": "\u{1F3C7}",
  "skier": "\u26F7\uFE0F",
  "snowboarder": "\u{1F3C2}",
  "golfing": "\u{1F3CC}\uFE0F",
  "golfing_man": "\u{1F3CC}\uFE0F\u200D\u2642\uFE0F",
  "golfing_woman": "\u{1F3CC}\uFE0F\u200D\u2640\uFE0F",
  "surfer": "\u{1F3C4}",
  "surfing_man": "\u{1F3C4}\u200D\u2642\uFE0F",
  "surfing_woman": "\u{1F3C4}\u200D\u2640\uFE0F",
  "rowboat": "\u{1F6A3}",
  "rowing_man": "\u{1F6A3}\u200D\u2642\uFE0F",
  "rowing_woman": "\u{1F6A3}\u200D\u2640\uFE0F",
  "swimmer": "\u{1F3CA}",
  "swimming_man": "\u{1F3CA}\u200D\u2642\uFE0F",
  "swimming_woman": "\u{1F3CA}\u200D\u2640\uFE0F",
  "bouncing_ball_person": "\u26F9\uFE0F",
  "bouncing_ball_man": "\u26F9\uFE0F\u200D\u2642\uFE0F",
  "basketball_man": "\u26F9\uFE0F\u200D\u2642\uFE0F",
  "bouncing_ball_woman": "\u26F9\uFE0F\u200D\u2640\uFE0F",
  "basketball_woman": "\u26F9\uFE0F\u200D\u2640\uFE0F",
  "weight_lifting": "\u{1F3CB}\uFE0F",
  "weight_lifting_man": "\u{1F3CB}\uFE0F\u200D\u2642\uFE0F",
  "weight_lifting_woman": "\u{1F3CB}\uFE0F\u200D\u2640\uFE0F",
  "bicyclist": "\u{1F6B4}",
  "biking_man": "\u{1F6B4}\u200D\u2642\uFE0F",
  "biking_woman": "\u{1F6B4}\u200D\u2640\uFE0F",
  "mountain_bicyclist": "\u{1F6B5}",
  "mountain_biking_man": "\u{1F6B5}\u200D\u2642\uFE0F",
  "mountain_biking_woman": "\u{1F6B5}\u200D\u2640\uFE0F",
  "cartwheeling": "\u{1F938}",
  "man_cartwheeling": "\u{1F938}\u200D\u2642\uFE0F",
  "woman_cartwheeling": "\u{1F938}\u200D\u2640\uFE0F",
  "wrestling": "\u{1F93C}",
  "men_wrestling": "\u{1F93C}\u200D\u2642\uFE0F",
  "women_wrestling": "\u{1F93C}\u200D\u2640\uFE0F",
  "water_polo": "\u{1F93D}",
  "man_playing_water_polo": "\u{1F93D}\u200D\u2642\uFE0F",
  "woman_playing_water_polo": "\u{1F93D}\u200D\u2640\uFE0F",
  "handball_person": "\u{1F93E}",
  "man_playing_handball": "\u{1F93E}\u200D\u2642\uFE0F",
  "woman_playing_handball": "\u{1F93E}\u200D\u2640\uFE0F",
  "juggling_person": "\u{1F939}",
  "man_juggling": "\u{1F939}\u200D\u2642\uFE0F",
  "woman_juggling": "\u{1F939}\u200D\u2640\uFE0F",
  "lotus_position": "\u{1F9D8}",
  "lotus_position_man": "\u{1F9D8}\u200D\u2642\uFE0F",
  "lotus_position_woman": "\u{1F9D8}\u200D\u2640\uFE0F",
  "bath": "\u{1F6C0}",
  "sleeping_bed": "\u{1F6CC}",
  "people_holding_hands": "\u{1F9D1}\u200D\u{1F91D}\u200D\u{1F9D1}",
  "two_women_holding_hands": "\u{1F46D}",
  "couple": "\u{1F46B}",
  "two_men_holding_hands": "\u{1F46C}",
  "couplekiss": "\u{1F48F}",
  "couplekiss_man_woman": "\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F48B}\u200D\u{1F468}",
  "couplekiss_man_man": "\u{1F468}\u200D\u2764\uFE0F\u200D\u{1F48B}\u200D\u{1F468}",
  "couplekiss_woman_woman": "\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F48B}\u200D\u{1F469}",
  "couple_with_heart": "\u{1F491}",
  "couple_with_heart_woman_man": "\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F468}",
  "couple_with_heart_man_man": "\u{1F468}\u200D\u2764\uFE0F\u200D\u{1F468}",
  "couple_with_heart_woman_woman": "\u{1F469}\u200D\u2764\uFE0F\u200D\u{1F469}",
  "family": "\u{1F46A}",
  "family_man_woman_boy": "\u{1F468}\u200D\u{1F469}\u200D\u{1F466}",
  "family_man_woman_girl": "\u{1F468}\u200D\u{1F469}\u200D\u{1F467}",
  "family_man_woman_girl_boy": "\u{1F468}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",
  "family_man_woman_boy_boy": "\u{1F468}\u200D\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",
  "family_man_woman_girl_girl": "\u{1F468}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",
  "family_man_man_boy": "\u{1F468}\u200D\u{1F468}\u200D\u{1F466}",
  "family_man_man_girl": "\u{1F468}\u200D\u{1F468}\u200D\u{1F467}",
  "family_man_man_girl_boy": "\u{1F468}\u200D\u{1F468}\u200D\u{1F467}\u200D\u{1F466}",
  "family_man_man_boy_boy": "\u{1F468}\u200D\u{1F468}\u200D\u{1F466}\u200D\u{1F466}",
  "family_man_man_girl_girl": "\u{1F468}\u200D\u{1F468}\u200D\u{1F467}\u200D\u{1F467}",
  "family_woman_woman_boy": "\u{1F469}\u200D\u{1F469}\u200D\u{1F466}",
  "family_woman_woman_girl": "\u{1F469}\u200D\u{1F469}\u200D\u{1F467}",
  "family_woman_woman_girl_boy": "\u{1F469}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",
  "family_woman_woman_boy_boy": "\u{1F469}\u200D\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",
  "family_woman_woman_girl_girl": "\u{1F469}\u200D\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",
  "family_man_boy": "\u{1F468}\u200D\u{1F466}",
  "family_man_boy_boy": "\u{1F468}\u200D\u{1F466}\u200D\u{1F466}",
  "family_man_girl": "\u{1F468}\u200D\u{1F467}",
  "family_man_girl_boy": "\u{1F468}\u200D\u{1F467}\u200D\u{1F466}",
  "family_man_girl_girl": "\u{1F468}\u200D\u{1F467}\u200D\u{1F467}",
  "family_woman_boy": "\u{1F469}\u200D\u{1F466}",
  "family_woman_boy_boy": "\u{1F469}\u200D\u{1F466}\u200D\u{1F466}",
  "family_woman_girl": "\u{1F469}\u200D\u{1F467}",
  "family_woman_girl_boy": "\u{1F469}\u200D\u{1F467}\u200D\u{1F466}",
  "family_woman_girl_girl": "\u{1F469}\u200D\u{1F467}\u200D\u{1F467}",
  "speaking_head": "\u{1F5E3}\uFE0F",
  "bust_in_silhouette": "\u{1F464}",
  "busts_in_silhouette": "\u{1F465}",
  "people_hugging": "\u{1FAC2}",
  "footprints": "\u{1F463}",
  "monkey_face": "\u{1F435}",
  "monkey": "\u{1F412}",
  "gorilla": "\u{1F98D}",
  "orangutan": "\u{1F9A7}",
  "dog": "\u{1F436}",
  "dog2": "\u{1F415}",
  "guide_dog": "\u{1F9AE}",
  "service_dog": "\u{1F415}\u200D\u{1F9BA}",
  "poodle": "\u{1F429}",
  "wolf": "\u{1F43A}",
  "fox_face": "\u{1F98A}",
  "raccoon": "\u{1F99D}",
  "cat": "\u{1F431}",
  "cat2": "\u{1F408}",
  "black_cat": "\u{1F408}\u200D\u2B1B",
  "lion": "\u{1F981}",
  "tiger": "\u{1F42F}",
  "tiger2": "\u{1F405}",
  "leopard": "\u{1F406}",
  "horse": "\u{1F434}",
  "racehorse": "\u{1F40E}",
  "unicorn": "\u{1F984}",
  "zebra": "\u{1F993}",
  "deer": "\u{1F98C}",
  "bison": "\u{1F9AC}",
  "cow": "\u{1F42E}",
  "ox": "\u{1F402}",
  "water_buffalo": "\u{1F403}",
  "cow2": "\u{1F404}",
  "pig": "\u{1F437}",
  "pig2": "\u{1F416}",
  "boar": "\u{1F417}",
  "pig_nose": "\u{1F43D}",
  "ram": "\u{1F40F}",
  "sheep": "\u{1F411}",
  "goat": "\u{1F410}",
  "dromedary_camel": "\u{1F42A}",
  "camel": "\u{1F42B}",
  "llama": "\u{1F999}",
  "giraffe": "\u{1F992}",
  "elephant": "\u{1F418}",
  "mammoth": "\u{1F9A3}",
  "rhinoceros": "\u{1F98F}",
  "hippopotamus": "\u{1F99B}",
  "mouse": "\u{1F42D}",
  "mouse2": "\u{1F401}",
  "rat": "\u{1F400}",
  "hamster": "\u{1F439}",
  "rabbit": "\u{1F430}",
  "rabbit2": "\u{1F407}",
  "chipmunk": "\u{1F43F}\uFE0F",
  "beaver": "\u{1F9AB}",
  "hedgehog": "\u{1F994}",
  "bat": "\u{1F987}",
  "bear": "\u{1F43B}",
  "polar_bear": "\u{1F43B}\u200D\u2744\uFE0F",
  "koala": "\u{1F428}",
  "panda_face": "\u{1F43C}",
  "sloth": "\u{1F9A5}",
  "otter": "\u{1F9A6}",
  "skunk": "\u{1F9A8}",
  "kangaroo": "\u{1F998}",
  "badger": "\u{1F9A1}",
  "feet": "\u{1F43E}",
  "paw_prints": "\u{1F43E}",
  "turkey": "\u{1F983}",
  "chicken": "\u{1F414}",
  "rooster": "\u{1F413}",
  "hatching_chick": "\u{1F423}",
  "baby_chick": "\u{1F424}",
  "hatched_chick": "\u{1F425}",
  "bird": "\u{1F426}",
  "penguin": "\u{1F427}",
  "dove": "\u{1F54A}\uFE0F",
  "eagle": "\u{1F985}",
  "duck": "\u{1F986}",
  "swan": "\u{1F9A2}",
  "owl": "\u{1F989}",
  "dodo": "\u{1F9A4}",
  "feather": "\u{1FAB6}",
  "flamingo": "\u{1F9A9}",
  "peacock": "\u{1F99A}",
  "parrot": "\u{1F99C}",
  "frog": "\u{1F438}",
  "crocodile": "\u{1F40A}",
  "turtle": "\u{1F422}",
  "lizard": "\u{1F98E}",
  "snake": "\u{1F40D}",
  "dragon_face": "\u{1F432}",
  "dragon": "\u{1F409}",
  "sauropod": "\u{1F995}",
  "t-rex": "\u{1F996}",
  "whale": "\u{1F433}",
  "whale2": "\u{1F40B}",
  "dolphin": "\u{1F42C}",
  "flipper": "\u{1F42C}",
  "seal": "\u{1F9AD}",
  "fish": "\u{1F41F}",
  "tropical_fish": "\u{1F420}",
  "blowfish": "\u{1F421}",
  "shark": "\u{1F988}",
  "octopus": "\u{1F419}",
  "shell": "\u{1F41A}",
  "snail": "\u{1F40C}",
  "butterfly": "\u{1F98B}",
  "bug": "\u{1F41B}",
  "ant": "\u{1F41C}",
  "bee": "\u{1F41D}",
  "honeybee": "\u{1F41D}",
  "beetle": "\u{1FAB2}",
  "lady_beetle": "\u{1F41E}",
  "cricket": "\u{1F997}",
  "cockroach": "\u{1FAB3}",
  "spider": "\u{1F577}\uFE0F",
  "spider_web": "\u{1F578}\uFE0F",
  "scorpion": "\u{1F982}",
  "mosquito": "\u{1F99F}",
  "fly": "\u{1FAB0}",
  "worm": "\u{1FAB1}",
  "microbe": "\u{1F9A0}",
  "bouquet": "\u{1F490}",
  "cherry_blossom": "\u{1F338}",
  "white_flower": "\u{1F4AE}",
  "rosette": "\u{1F3F5}\uFE0F",
  "rose": "\u{1F339}",
  "wilted_flower": "\u{1F940}",
  "hibiscus": "\u{1F33A}",
  "sunflower": "\u{1F33B}",
  "blossom": "\u{1F33C}",
  "tulip": "\u{1F337}",
  "seedling": "\u{1F331}",
  "potted_plant": "\u{1FAB4}",
  "evergreen_tree": "\u{1F332}",
  "deciduous_tree": "\u{1F333}",
  "palm_tree": "\u{1F334}",
  "cactus": "\u{1F335}",
  "ear_of_rice": "\u{1F33E}",
  "herb": "\u{1F33F}",
  "shamrock": "\u2618\uFE0F",
  "four_leaf_clover": "\u{1F340}",
  "maple_leaf": "\u{1F341}",
  "fallen_leaf": "\u{1F342}",
  "leaves": "\u{1F343}",
  "grapes": "\u{1F347}",
  "melon": "\u{1F348}",
  "watermelon": "\u{1F349}",
  "tangerine": "\u{1F34A}",
  "orange": "\u{1F34A}",
  "mandarin": "\u{1F34A}",
  "lemon": "\u{1F34B}",
  "banana": "\u{1F34C}",
  "pineapple": "\u{1F34D}",
  "mango": "\u{1F96D}",
  "apple": "\u{1F34E}",
  "green_apple": "\u{1F34F}",
  "pear": "\u{1F350}",
  "peach": "\u{1F351}",
  "cherries": "\u{1F352}",
  "strawberry": "\u{1F353}",
  "blueberries": "\u{1FAD0}",
  "kiwi_fruit": "\u{1F95D}",
  "tomato": "\u{1F345}",
  "olive": "\u{1FAD2}",
  "coconut": "\u{1F965}",
  "avocado": "\u{1F951}",
  "eggplant": "\u{1F346}",
  "potato": "\u{1F954}",
  "carrot": "\u{1F955}",
  "corn": "\u{1F33D}",
  "hot_pepper": "\u{1F336}\uFE0F",
  "bell_pepper": "\u{1FAD1}",
  "cucumber": "\u{1F952}",
  "leafy_green": "\u{1F96C}",
  "broccoli": "\u{1F966}",
  "garlic": "\u{1F9C4}",
  "onion": "\u{1F9C5}",
  "mushroom": "\u{1F344}",
  "peanuts": "\u{1F95C}",
  "chestnut": "\u{1F330}",
  "bread": "\u{1F35E}",
  "croissant": "\u{1F950}",
  "baguette_bread": "\u{1F956}",
  "flatbread": "\u{1FAD3}",
  "pretzel": "\u{1F968}",
  "bagel": "\u{1F96F}",
  "pancakes": "\u{1F95E}",
  "waffle": "\u{1F9C7}",
  "cheese": "\u{1F9C0}",
  "meat_on_bone": "\u{1F356}",
  "poultry_leg": "\u{1F357}",
  "cut_of_meat": "\u{1F969}",
  "bacon": "\u{1F953}",
  "hamburger": "\u{1F354}",
  "fries": "\u{1F35F}",
  "pizza": "\u{1F355}",
  "hotdog": "\u{1F32D}",
  "sandwich": "\u{1F96A}",
  "taco": "\u{1F32E}",
  "burrito": "\u{1F32F}",
  "tamale": "\u{1FAD4}",
  "stuffed_flatbread": "\u{1F959}",
  "falafel": "\u{1F9C6}",
  "egg": "\u{1F95A}",
  "fried_egg": "\u{1F373}",
  "shallow_pan_of_food": "\u{1F958}",
  "stew": "\u{1F372}",
  "fondue": "\u{1FAD5}",
  "bowl_with_spoon": "\u{1F963}",
  "green_salad": "\u{1F957}",
  "popcorn": "\u{1F37F}",
  "butter": "\u{1F9C8}",
  "salt": "\u{1F9C2}",
  "canned_food": "\u{1F96B}",
  "bento": "\u{1F371}",
  "rice_cracker": "\u{1F358}",
  "rice_ball": "\u{1F359}",
  "rice": "\u{1F35A}",
  "curry": "\u{1F35B}",
  "ramen": "\u{1F35C}",
  "spaghetti": "\u{1F35D}",
  "sweet_potato": "\u{1F360}",
  "oden": "\u{1F362}",
  "sushi": "\u{1F363}",
  "fried_shrimp": "\u{1F364}",
  "fish_cake": "\u{1F365}",
  "moon_cake": "\u{1F96E}",
  "dango": "\u{1F361}",
  "dumpling": "\u{1F95F}",
  "fortune_cookie": "\u{1F960}",
  "takeout_box": "\u{1F961}",
  "crab": "\u{1F980}",
  "lobster": "\u{1F99E}",
  "shrimp": "\u{1F990}",
  "squid": "\u{1F991}",
  "oyster": "\u{1F9AA}",
  "icecream": "\u{1F366}",
  "shaved_ice": "\u{1F367}",
  "ice_cream": "\u{1F368}",
  "doughnut": "\u{1F369}",
  "cookie": "\u{1F36A}",
  "birthday": "\u{1F382}",
  "cake": "\u{1F370}",
  "cupcake": "\u{1F9C1}",
  "pie": "\u{1F967}",
  "chocolate_bar": "\u{1F36B}",
  "candy": "\u{1F36C}",
  "lollipop": "\u{1F36D}",
  "custard": "\u{1F36E}",
  "honey_pot": "\u{1F36F}",
  "baby_bottle": "\u{1F37C}",
  "milk_glass": "\u{1F95B}",
  "coffee": "\u2615",
  "teapot": "\u{1FAD6}",
  "tea": "\u{1F375}",
  "sake": "\u{1F376}",
  "champagne": "\u{1F37E}",
  "wine_glass": "\u{1F377}",
  "cocktail": "\u{1F378}",
  "tropical_drink": "\u{1F379}",
  "beer": "\u{1F37A}",
  "beers": "\u{1F37B}",
  "clinking_glasses": "\u{1F942}",
  "tumbler_glass": "\u{1F943}",
  "cup_with_straw": "\u{1F964}",
  "bubble_tea": "\u{1F9CB}",
  "beverage_box": "\u{1F9C3}",
  "mate": "\u{1F9C9}",
  "ice_cube": "\u{1F9CA}",
  "chopsticks": "\u{1F962}",
  "plate_with_cutlery": "\u{1F37D}\uFE0F",
  "fork_and_knife": "\u{1F374}",
  "spoon": "\u{1F944}",
  "hocho": "\u{1F52A}",
  "knife": "\u{1F52A}",
  "amphora": "\u{1F3FA}",
  "earth_africa": "\u{1F30D}",
  "earth_americas": "\u{1F30E}",
  "earth_asia": "\u{1F30F}",
  "globe_with_meridians": "\u{1F310}",
  "world_map": "\u{1F5FA}\uFE0F",
  "japan": "\u{1F5FE}",
  "compass": "\u{1F9ED}",
  "mountain_snow": "\u{1F3D4}\uFE0F",
  "mountain": "\u26F0\uFE0F",
  "volcano": "\u{1F30B}",
  "mount_fuji": "\u{1F5FB}",
  "camping": "\u{1F3D5}\uFE0F",
  "beach_umbrella": "\u{1F3D6}\uFE0F",
  "desert": "\u{1F3DC}\uFE0F",
  "desert_island": "\u{1F3DD}\uFE0F",
  "national_park": "\u{1F3DE}\uFE0F",
  "stadium": "\u{1F3DF}\uFE0F",
  "classical_building": "\u{1F3DB}\uFE0F",
  "building_construction": "\u{1F3D7}\uFE0F",
  "bricks": "\u{1F9F1}",
  "rock": "\u{1FAA8}",
  "wood": "\u{1FAB5}",
  "hut": "\u{1F6D6}",
  "houses": "\u{1F3D8}\uFE0F",
  "derelict_house": "\u{1F3DA}\uFE0F",
  "house": "\u{1F3E0}",
  "house_with_garden": "\u{1F3E1}",
  "office": "\u{1F3E2}",
  "post_office": "\u{1F3E3}",
  "european_post_office": "\u{1F3E4}",
  "hospital": "\u{1F3E5}",
  "bank": "\u{1F3E6}",
  "hotel": "\u{1F3E8}",
  "love_hotel": "\u{1F3E9}",
  "convenience_store": "\u{1F3EA}",
  "school": "\u{1F3EB}",
  "department_store": "\u{1F3EC}",
  "factory": "\u{1F3ED}",
  "japanese_castle": "\u{1F3EF}",
  "european_castle": "\u{1F3F0}",
  "wedding": "\u{1F492}",
  "tokyo_tower": "\u{1F5FC}",
  "statue_of_liberty": "\u{1F5FD}",
  "church": "\u26EA",
  "mosque": "\u{1F54C}",
  "hindu_temple": "\u{1F6D5}",
  "synagogue": "\u{1F54D}",
  "shinto_shrine": "\u26E9\uFE0F",
  "kaaba": "\u{1F54B}",
  "fountain": "\u26F2",
  "tent": "\u26FA",
  "foggy": "\u{1F301}",
  "night_with_stars": "\u{1F303}",
  "cityscape": "\u{1F3D9}\uFE0F",
  "sunrise_over_mountains": "\u{1F304}",
  "sunrise": "\u{1F305}",
  "city_sunset": "\u{1F306}",
  "city_sunrise": "\u{1F307}",
  "bridge_at_night": "\u{1F309}",
  "hotsprings": "\u2668\uFE0F",
  "carousel_horse": "\u{1F3A0}",
  "ferris_wheel": "\u{1F3A1}",
  "roller_coaster": "\u{1F3A2}",
  "barber": "\u{1F488}",
  "circus_tent": "\u{1F3AA}",
  "steam_locomotive": "\u{1F682}",
  "railway_car": "\u{1F683}",
  "bullettrain_side": "\u{1F684}",
  "bullettrain_front": "\u{1F685}",
  "train2": "\u{1F686}",
  "metro": "\u{1F687}",
  "light_rail": "\u{1F688}",
  "station": "\u{1F689}",
  "tram": "\u{1F68A}",
  "monorail": "\u{1F69D}",
  "mountain_railway": "\u{1F69E}",
  "train": "\u{1F68B}",
  "bus": "\u{1F68C}",
  "oncoming_bus": "\u{1F68D}",
  "trolleybus": "\u{1F68E}",
  "minibus": "\u{1F690}",
  "ambulance": "\u{1F691}",
  "fire_engine": "\u{1F692}",
  "police_car": "\u{1F693}",
  "oncoming_police_car": "\u{1F694}",
  "taxi": "\u{1F695}",
  "oncoming_taxi": "\u{1F696}",
  "car": "\u{1F697}",
  "red_car": "\u{1F697}",
  "oncoming_automobile": "\u{1F698}",
  "blue_car": "\u{1F699}",
  "pickup_truck": "\u{1F6FB}",
  "truck": "\u{1F69A}",
  "articulated_lorry": "\u{1F69B}",
  "tractor": "\u{1F69C}",
  "racing_car": "\u{1F3CE}\uFE0F",
  "motorcycle": "\u{1F3CD}\uFE0F",
  "motor_scooter": "\u{1F6F5}",
  "manual_wheelchair": "\u{1F9BD}",
  "motorized_wheelchair": "\u{1F9BC}",
  "auto_rickshaw": "\u{1F6FA}",
  "bike": "\u{1F6B2}",
  "kick_scooter": "\u{1F6F4}",
  "skateboard": "\u{1F6F9}",
  "roller_skate": "\u{1F6FC}",
  "busstop": "\u{1F68F}",
  "motorway": "\u{1F6E3}\uFE0F",
  "railway_track": "\u{1F6E4}\uFE0F",
  "oil_drum": "\u{1F6E2}\uFE0F",
  "fuelpump": "\u26FD",
  "rotating_light": "\u{1F6A8}",
  "traffic_light": "\u{1F6A5}",
  "vertical_traffic_light": "\u{1F6A6}",
  "stop_sign": "\u{1F6D1}",
  "construction": "\u{1F6A7}",
  "anchor": "\u2693",
  "boat": "\u26F5",
  "sailboat": "\u26F5",
  "canoe": "\u{1F6F6}",
  "speedboat": "\u{1F6A4}",
  "passenger_ship": "\u{1F6F3}\uFE0F",
  "ferry": "\u26F4\uFE0F",
  "motor_boat": "\u{1F6E5}\uFE0F",
  "ship": "\u{1F6A2}",
  "airplane": "\u2708\uFE0F",
  "small_airplane": "\u{1F6E9}\uFE0F",
  "flight_departure": "\u{1F6EB}",
  "flight_arrival": "\u{1F6EC}",
  "parachute": "\u{1FA82}",
  "seat": "\u{1F4BA}",
  "helicopter": "\u{1F681}",
  "suspension_railway": "\u{1F69F}",
  "mountain_cableway": "\u{1F6A0}",
  "aerial_tramway": "\u{1F6A1}",
  "artificial_satellite": "\u{1F6F0}\uFE0F",
  "rocket": "\u{1F680}",
  "flying_saucer": "\u{1F6F8}",
  "bellhop_bell": "\u{1F6CE}\uFE0F",
  "luggage": "\u{1F9F3}",
  "hourglass": "\u231B",
  "hourglass_flowing_sand": "\u23F3",
  "watch": "\u231A",
  "alarm_clock": "\u23F0",
  "stopwatch": "\u23F1\uFE0F",
  "timer_clock": "\u23F2\uFE0F",
  "mantelpiece_clock": "\u{1F570}\uFE0F",
  "clock12": "\u{1F55B}",
  "clock1230": "\u{1F567}",
  "clock1": "\u{1F550}",
  "clock130": "\u{1F55C}",
  "clock2": "\u{1F551}",
  "clock230": "\u{1F55D}",
  "clock3": "\u{1F552}",
  "clock330": "\u{1F55E}",
  "clock4": "\u{1F553}",
  "clock430": "\u{1F55F}",
  "clock5": "\u{1F554}",
  "clock530": "\u{1F560}",
  "clock6": "\u{1F555}",
  "clock630": "\u{1F561}",
  "clock7": "\u{1F556}",
  "clock730": "\u{1F562}",
  "clock8": "\u{1F557}",
  "clock830": "\u{1F563}",
  "clock9": "\u{1F558}",
  "clock930": "\u{1F564}",
  "clock10": "\u{1F559}",
  "clock1030": "\u{1F565}",
  "clock11": "\u{1F55A}",
  "clock1130": "\u{1F566}",
  "new_moon": "\u{1F311}",
  "waxing_crescent_moon": "\u{1F312}",
  "first_quarter_moon": "\u{1F313}",
  "moon": "\u{1F314}",
  "waxing_gibbous_moon": "\u{1F314}",
  "full_moon": "\u{1F315}",
  "waning_gibbous_moon": "\u{1F316}",
  "last_quarter_moon": "\u{1F317}",
  "waning_crescent_moon": "\u{1F318}",
  "crescent_moon": "\u{1F319}",
  "new_moon_with_face": "\u{1F31A}",
  "first_quarter_moon_with_face": "\u{1F31B}",
  "last_quarter_moon_with_face": "\u{1F31C}",
  "thermometer": "\u{1F321}\uFE0F",
  "sunny": "\u2600\uFE0F",
  "full_moon_with_face": "\u{1F31D}",
  "sun_with_face": "\u{1F31E}",
  "ringed_planet": "\u{1FA90}",
  "star": "\u2B50",
  "star2": "\u{1F31F}",
  "stars": "\u{1F320}",
  "milky_way": "\u{1F30C}",
  "cloud": "\u2601\uFE0F",
  "partly_sunny": "\u26C5",
  "cloud_with_lightning_and_rain": "\u26C8\uFE0F",
  "sun_behind_small_cloud": "\u{1F324}\uFE0F",
  "sun_behind_large_cloud": "\u{1F325}\uFE0F",
  "sun_behind_rain_cloud": "\u{1F326}\uFE0F",
  "cloud_with_rain": "\u{1F327}\uFE0F",
  "cloud_with_snow": "\u{1F328}\uFE0F",
  "cloud_with_lightning": "\u{1F329}\uFE0F",
  "tornado": "\u{1F32A}\uFE0F",
  "fog": "\u{1F32B}\uFE0F",
  "wind_face": "\u{1F32C}\uFE0F",
  "cyclone": "\u{1F300}",
  "rainbow": "\u{1F308}",
  "closed_umbrella": "\u{1F302}",
  "open_umbrella": "\u2602\uFE0F",
  "umbrella": "\u2614",
  "parasol_on_ground": "\u26F1\uFE0F",
  "zap": "\u26A1",
  "snowflake": "\u2744\uFE0F",
  "snowman_with_snow": "\u2603\uFE0F",
  "snowman": "\u26C4",
  "comet": "\u2604\uFE0F",
  "fire": "\u{1F525}",
  "droplet": "\u{1F4A7}",
  "ocean": "\u{1F30A}",
  "jack_o_lantern": "\u{1F383}",
  "christmas_tree": "\u{1F384}",
  "fireworks": "\u{1F386}",
  "sparkler": "\u{1F387}",
  "firecracker": "\u{1F9E8}",
  "sparkles": "\u2728",
  "balloon": "\u{1F388}",
  "tada": "\u{1F389}",
  "confetti_ball": "\u{1F38A}",
  "tanabata_tree": "\u{1F38B}",
  "bamboo": "\u{1F38D}",
  "dolls": "\u{1F38E}",
  "flags": "\u{1F38F}",
  "wind_chime": "\u{1F390}",
  "rice_scene": "\u{1F391}",
  "red_envelope": "\u{1F9E7}",
  "ribbon": "\u{1F380}",
  "gift": "\u{1F381}",
  "reminder_ribbon": "\u{1F397}\uFE0F",
  "tickets": "\u{1F39F}\uFE0F",
  "ticket": "\u{1F3AB}",
  "medal_military": "\u{1F396}\uFE0F",
  "trophy": "\u{1F3C6}",
  "medal_sports": "\u{1F3C5}",
  "1st_place_medal": "\u{1F947}",
  "2nd_place_medal": "\u{1F948}",
  "3rd_place_medal": "\u{1F949}",
  "soccer": "\u26BD",
  "baseball": "\u26BE",
  "softball": "\u{1F94E}",
  "basketball": "\u{1F3C0}",
  "volleyball": "\u{1F3D0}",
  "football": "\u{1F3C8}",
  "rugby_football": "\u{1F3C9}",
  "tennis": "\u{1F3BE}",
  "flying_disc": "\u{1F94F}",
  "bowling": "\u{1F3B3}",
  "cricket_game": "\u{1F3CF}",
  "field_hockey": "\u{1F3D1}",
  "ice_hockey": "\u{1F3D2}",
  "lacrosse": "\u{1F94D}",
  "ping_pong": "\u{1F3D3}",
  "badminton": "\u{1F3F8}",
  "boxing_glove": "\u{1F94A}",
  "martial_arts_uniform": "\u{1F94B}",
  "goal_net": "\u{1F945}",
  "golf": "\u26F3",
  "ice_skate": "\u26F8\uFE0F",
  "fishing_pole_and_fish": "\u{1F3A3}",
  "diving_mask": "\u{1F93F}",
  "running_shirt_with_sash": "\u{1F3BD}",
  "ski": "\u{1F3BF}",
  "sled": "\u{1F6F7}",
  "curling_stone": "\u{1F94C}",
  "dart": "\u{1F3AF}",
  "yo_yo": "\u{1FA80}",
  "kite": "\u{1FA81}",
  "8ball": "\u{1F3B1}",
  "crystal_ball": "\u{1F52E}",
  "magic_wand": "\u{1FA84}",
  "nazar_amulet": "\u{1F9FF}",
  "video_game": "\u{1F3AE}",
  "joystick": "\u{1F579}\uFE0F",
  "slot_machine": "\u{1F3B0}",
  "game_die": "\u{1F3B2}",
  "jigsaw": "\u{1F9E9}",
  "teddy_bear": "\u{1F9F8}",
  "pinata": "\u{1FA85}",
  "nesting_dolls": "\u{1FA86}",
  "spades": "\u2660\uFE0F",
  "hearts": "\u2665\uFE0F",
  "diamonds": "\u2666\uFE0F",
  "clubs": "\u2663\uFE0F",
  "chess_pawn": "\u265F\uFE0F",
  "black_joker": "\u{1F0CF}",
  "mahjong": "\u{1F004}",
  "flower_playing_cards": "\u{1F3B4}",
  "performing_arts": "\u{1F3AD}",
  "framed_picture": "\u{1F5BC}\uFE0F",
  "art": "\u{1F3A8}",
  "thread": "\u{1F9F5}",
  "sewing_needle": "\u{1FAA1}",
  "yarn": "\u{1F9F6}",
  "knot": "\u{1FAA2}",
  "eyeglasses": "\u{1F453}",
  "dark_sunglasses": "\u{1F576}\uFE0F",
  "goggles": "\u{1F97D}",
  "lab_coat": "\u{1F97C}",
  "safety_vest": "\u{1F9BA}",
  "necktie": "\u{1F454}",
  "shirt": "\u{1F455}",
  "tshirt": "\u{1F455}",
  "jeans": "\u{1F456}",
  "scarf": "\u{1F9E3}",
  "gloves": "\u{1F9E4}",
  "coat": "\u{1F9E5}",
  "socks": "\u{1F9E6}",
  "dress": "\u{1F457}",
  "kimono": "\u{1F458}",
  "sari": "\u{1F97B}",
  "one_piece_swimsuit": "\u{1FA71}",
  "swim_brief": "\u{1FA72}",
  "shorts": "\u{1FA73}",
  "bikini": "\u{1F459}",
  "womans_clothes": "\u{1F45A}",
  "purse": "\u{1F45B}",
  "handbag": "\u{1F45C}",
  "pouch": "\u{1F45D}",
  "shopping": "\u{1F6CD}\uFE0F",
  "school_satchel": "\u{1F392}",
  "thong_sandal": "\u{1FA74}",
  "mans_shoe": "\u{1F45E}",
  "shoe": "\u{1F45E}",
  "athletic_shoe": "\u{1F45F}",
  "hiking_boot": "\u{1F97E}",
  "flat_shoe": "\u{1F97F}",
  "high_heel": "\u{1F460}",
  "sandal": "\u{1F461}",
  "ballet_shoes": "\u{1FA70}",
  "boot": "\u{1F462}",
  "crown": "\u{1F451}",
  "womans_hat": "\u{1F452}",
  "tophat": "\u{1F3A9}",
  "mortar_board": "\u{1F393}",
  "billed_cap": "\u{1F9E2}",
  "military_helmet": "\u{1FA96}",
  "rescue_worker_helmet": "\u26D1\uFE0F",
  "prayer_beads": "\u{1F4FF}",
  "lipstick": "\u{1F484}",
  "ring": "\u{1F48D}",
  "gem": "\u{1F48E}",
  "mute": "\u{1F507}",
  "speaker": "\u{1F508}",
  "sound": "\u{1F509}",
  "loud_sound": "\u{1F50A}",
  "loudspeaker": "\u{1F4E2}",
  "mega": "\u{1F4E3}",
  "postal_horn": "\u{1F4EF}",
  "bell": "\u{1F514}",
  "no_bell": "\u{1F515}",
  "musical_score": "\u{1F3BC}",
  "musical_note": "\u{1F3B5}",
  "notes": "\u{1F3B6}",
  "studio_microphone": "\u{1F399}\uFE0F",
  "level_slider": "\u{1F39A}\uFE0F",
  "control_knobs": "\u{1F39B}\uFE0F",
  "microphone": "\u{1F3A4}",
  "headphones": "\u{1F3A7}",
  "radio": "\u{1F4FB}",
  "saxophone": "\u{1F3B7}",
  "accordion": "\u{1FA97}",
  "guitar": "\u{1F3B8}",
  "musical_keyboard": "\u{1F3B9}",
  "trumpet": "\u{1F3BA}",
  "violin": "\u{1F3BB}",
  "banjo": "\u{1FA95}",
  "drum": "\u{1F941}",
  "long_drum": "\u{1FA98}",
  "iphone": "\u{1F4F1}",
  "calling": "\u{1F4F2}",
  "phone": "\u260E\uFE0F",
  "telephone": "\u260E\uFE0F",
  "telephone_receiver": "\u{1F4DE}",
  "pager": "\u{1F4DF}",
  "fax": "\u{1F4E0}",
  "battery": "\u{1F50B}",
  "electric_plug": "\u{1F50C}",
  "computer": "\u{1F4BB}",
  "desktop_computer": "\u{1F5A5}\uFE0F",
  "printer": "\u{1F5A8}\uFE0F",
  "keyboard": "\u2328\uFE0F",
  "computer_mouse": "\u{1F5B1}\uFE0F",
  "trackball": "\u{1F5B2}\uFE0F",
  "minidisc": "\u{1F4BD}",
  "floppy_disk": "\u{1F4BE}",
  "cd": "\u{1F4BF}",
  "dvd": "\u{1F4C0}",
  "abacus": "\u{1F9EE}",
  "movie_camera": "\u{1F3A5}",
  "film_strip": "\u{1F39E}\uFE0F",
  "film_projector": "\u{1F4FD}\uFE0F",
  "clapper": "\u{1F3AC}",
  "tv": "\u{1F4FA}",
  "camera": "\u{1F4F7}",
  "camera_flash": "\u{1F4F8}",
  "video_camera": "\u{1F4F9}",
  "vhs": "\u{1F4FC}",
  "mag": "\u{1F50D}",
  "mag_right": "\u{1F50E}",
  "candle": "\u{1F56F}\uFE0F",
  "bulb": "\u{1F4A1}",
  "flashlight": "\u{1F526}",
  "izakaya_lantern": "\u{1F3EE}",
  "lantern": "\u{1F3EE}",
  "diya_lamp": "\u{1FA94}",
  "notebook_with_decorative_cover": "\u{1F4D4}",
  "closed_book": "\u{1F4D5}",
  "book": "\u{1F4D6}",
  "open_book": "\u{1F4D6}",
  "green_book": "\u{1F4D7}",
  "blue_book": "\u{1F4D8}",
  "orange_book": "\u{1F4D9}",
  "books": "\u{1F4DA}",
  "notebook": "\u{1F4D3}",
  "ledger": "\u{1F4D2}",
  "page_with_curl": "\u{1F4C3}",
  "scroll": "\u{1F4DC}",
  "page_facing_up": "\u{1F4C4}",
  "newspaper": "\u{1F4F0}",
  "newspaper_roll": "\u{1F5DE}\uFE0F",
  "bookmark_tabs": "\u{1F4D1}",
  "bookmark": "\u{1F516}",
  "label": "\u{1F3F7}\uFE0F",
  "moneybag": "\u{1F4B0}",
  "coin": "\u{1FA99}",
  "yen": "\u{1F4B4}",
  "dollar": "\u{1F4B5}",
  "euro": "\u{1F4B6}",
  "pound": "\u{1F4B7}",
  "money_with_wings": "\u{1F4B8}",
  "credit_card": "\u{1F4B3}",
  "receipt": "\u{1F9FE}",
  "chart": "\u{1F4B9}",
  "envelope": "\u2709\uFE0F",
  "email": "\u{1F4E7}",
  "e-mail": "\u{1F4E7}",
  "incoming_envelope": "\u{1F4E8}",
  "envelope_with_arrow": "\u{1F4E9}",
  "outbox_tray": "\u{1F4E4}",
  "inbox_tray": "\u{1F4E5}",
  "package": "\u{1F4E6}",
  "mailbox": "\u{1F4EB}",
  "mailbox_closed": "\u{1F4EA}",
  "mailbox_with_mail": "\u{1F4EC}",
  "mailbox_with_no_mail": "\u{1F4ED}",
  "postbox": "\u{1F4EE}",
  "ballot_box": "\u{1F5F3}\uFE0F",
  "pencil2": "\u270F\uFE0F",
  "black_nib": "\u2712\uFE0F",
  "fountain_pen": "\u{1F58B}\uFE0F",
  "pen": "\u{1F58A}\uFE0F",
  "paintbrush": "\u{1F58C}\uFE0F",
  "crayon": "\u{1F58D}\uFE0F",
  "memo": "\u{1F4DD}",
  "pencil": "\u{1F4DD}",
  "briefcase": "\u{1F4BC}",
  "file_folder": "\u{1F4C1}",
  "open_file_folder": "\u{1F4C2}",
  "card_index_dividers": "\u{1F5C2}\uFE0F",
  "date": "\u{1F4C5}",
  "calendar": "\u{1F4C6}",
  "spiral_notepad": "\u{1F5D2}\uFE0F",
  "spiral_calendar": "\u{1F5D3}\uFE0F",
  "card_index": "\u{1F4C7}",
  "chart_with_upwards_trend": "\u{1F4C8}",
  "chart_with_downwards_trend": "\u{1F4C9}",
  "bar_chart": "\u{1F4CA}",
  "clipboard": "\u{1F4CB}",
  "pushpin": "\u{1F4CC}",
  "round_pushpin": "\u{1F4CD}",
  "paperclip": "\u{1F4CE}",
  "paperclips": "\u{1F587}\uFE0F",
  "straight_ruler": "\u{1F4CF}",
  "triangular_ruler": "\u{1F4D0}",
  "scissors": "\u2702\uFE0F",
  "card_file_box": "\u{1F5C3}\uFE0F",
  "file_cabinet": "\u{1F5C4}\uFE0F",
  "wastebasket": "\u{1F5D1}\uFE0F",
  "lock": "\u{1F512}",
  "unlock": "\u{1F513}",
  "lock_with_ink_pen": "\u{1F50F}",
  "closed_lock_with_key": "\u{1F510}",
  "key": "\u{1F511}",
  "old_key": "\u{1F5DD}\uFE0F",
  "hammer": "\u{1F528}",
  "axe": "\u{1FA93}",
  "pick": "\u26CF\uFE0F",
  "hammer_and_pick": "\u2692\uFE0F",
  "hammer_and_wrench": "\u{1F6E0}\uFE0F",
  "dagger": "\u{1F5E1}\uFE0F",
  "crossed_swords": "\u2694\uFE0F",
  "gun": "\u{1F52B}",
  "boomerang": "\u{1FA83}",
  "bow_and_arrow": "\u{1F3F9}",
  "shield": "\u{1F6E1}\uFE0F",
  "carpentry_saw": "\u{1FA9A}",
  "wrench": "\u{1F527}",
  "screwdriver": "\u{1FA9B}",
  "nut_and_bolt": "\u{1F529}",
  "gear": "\u2699\uFE0F",
  "clamp": "\u{1F5DC}\uFE0F",
  "balance_scale": "\u2696\uFE0F",
  "probing_cane": "\u{1F9AF}",
  "link": "\u{1F517}",
  "chains": "\u26D3\uFE0F",
  "hook": "\u{1FA9D}",
  "toolbox": "\u{1F9F0}",
  "magnet": "\u{1F9F2}",
  "ladder": "\u{1FA9C}",
  "alembic": "\u2697\uFE0F",
  "test_tube": "\u{1F9EA}",
  "petri_dish": "\u{1F9EB}",
  "dna": "\u{1F9EC}",
  "microscope": "\u{1F52C}",
  "telescope": "\u{1F52D}",
  "satellite": "\u{1F4E1}",
  "syringe": "\u{1F489}",
  "drop_of_blood": "\u{1FA78}",
  "pill": "\u{1F48A}",
  "adhesive_bandage": "\u{1FA79}",
  "stethoscope": "\u{1FA7A}",
  "door": "\u{1F6AA}",
  "elevator": "\u{1F6D7}",
  "mirror": "\u{1FA9E}",
  "window": "\u{1FA9F}",
  "bed": "\u{1F6CF}\uFE0F",
  "couch_and_lamp": "\u{1F6CB}\uFE0F",
  "chair": "\u{1FA91}",
  "toilet": "\u{1F6BD}",
  "plunger": "\u{1FAA0}",
  "shower": "\u{1F6BF}",
  "bathtub": "\u{1F6C1}",
  "mouse_trap": "\u{1FAA4}",
  "razor": "\u{1FA92}",
  "lotion_bottle": "\u{1F9F4}",
  "safety_pin": "\u{1F9F7}",
  "broom": "\u{1F9F9}",
  "basket": "\u{1F9FA}",
  "roll_of_paper": "\u{1F9FB}",
  "bucket": "\u{1FAA3}",
  "soap": "\u{1F9FC}",
  "toothbrush": "\u{1FAA5}",
  "sponge": "\u{1F9FD}",
  "fire_extinguisher": "\u{1F9EF}",
  "shopping_cart": "\u{1F6D2}",
  "smoking": "\u{1F6AC}",
  "coffin": "\u26B0\uFE0F",
  "headstone": "\u{1FAA6}",
  "funeral_urn": "\u26B1\uFE0F",
  "moyai": "\u{1F5FF}",
  "placard": "\u{1FAA7}",
  "atm": "\u{1F3E7}",
  "put_litter_in_its_place": "\u{1F6AE}",
  "potable_water": "\u{1F6B0}",
  "wheelchair": "\u267F",
  "mens": "\u{1F6B9}",
  "womens": "\u{1F6BA}",
  "restroom": "\u{1F6BB}",
  "baby_symbol": "\u{1F6BC}",
  "wc": "\u{1F6BE}",
  "passport_control": "\u{1F6C2}",
  "customs": "\u{1F6C3}",
  "baggage_claim": "\u{1F6C4}",
  "left_luggage": "\u{1F6C5}",
  "warning": "\u26A0\uFE0F",
  "children_crossing": "\u{1F6B8}",
  "no_entry": "\u26D4",
  "no_entry_sign": "\u{1F6AB}",
  "no_bicycles": "\u{1F6B3}",
  "no_smoking": "\u{1F6AD}",
  "do_not_litter": "\u{1F6AF}",
  "non-potable_water": "\u{1F6B1}",
  "no_pedestrians": "\u{1F6B7}",
  "no_mobile_phones": "\u{1F4F5}",
  "underage": "\u{1F51E}",
  "radioactive": "\u2622\uFE0F",
  "biohazard": "\u2623\uFE0F",
  "arrow_up": "\u2B06\uFE0F",
  "arrow_upper_right": "\u2197\uFE0F",
  "arrow_right": "\u27A1\uFE0F",
  "arrow_lower_right": "\u2198\uFE0F",
  "arrow_down": "\u2B07\uFE0F",
  "arrow_lower_left": "\u2199\uFE0F",
  "arrow_left": "\u2B05\uFE0F",
  "arrow_upper_left": "\u2196\uFE0F",
  "arrow_up_down": "\u2195\uFE0F",
  "left_right_arrow": "\u2194\uFE0F",
  "leftwards_arrow_with_hook": "\u21A9\uFE0F",
  "arrow_right_hook": "\u21AA\uFE0F",
  "arrow_heading_up": "\u2934\uFE0F",
  "arrow_heading_down": "\u2935\uFE0F",
  "arrows_clockwise": "\u{1F503}",
  "arrows_counterclockwise": "\u{1F504}",
  "back": "\u{1F519}",
  "end": "\u{1F51A}",
  "on": "\u{1F51B}",
  "soon": "\u{1F51C}",
  "top": "\u{1F51D}",
  "place_of_worship": "\u{1F6D0}",
  "atom_symbol": "\u269B\uFE0F",
  "om": "\u{1F549}\uFE0F",
  "star_of_david": "\u2721\uFE0F",
  "wheel_of_dharma": "\u2638\uFE0F",
  "yin_yang": "\u262F\uFE0F",
  "latin_cross": "\u271D\uFE0F",
  "orthodox_cross": "\u2626\uFE0F",
  "star_and_crescent": "\u262A\uFE0F",
  "peace_symbol": "\u262E\uFE0F",
  "menorah": "\u{1F54E}",
  "six_pointed_star": "\u{1F52F}",
  "aries": "\u2648",
  "taurus": "\u2649",
  "gemini": "\u264A",
  "cancer": "\u264B",
  "leo": "\u264C",
  "virgo": "\u264D",
  "libra": "\u264E",
  "scorpius": "\u264F",
  "sagittarius": "\u2650",
  "capricorn": "\u2651",
  "aquarius": "\u2652",
  "pisces": "\u2653",
  "ophiuchus": "\u26CE",
  "twisted_rightwards_arrows": "\u{1F500}",
  "repeat": "\u{1F501}",
  "repeat_one": "\u{1F502}",
  "arrow_forward": "\u25B6\uFE0F",
  "fast_forward": "\u23E9",
  "next_track_button": "\u23ED\uFE0F",
  "play_or_pause_button": "\u23EF\uFE0F",
  "arrow_backward": "\u25C0\uFE0F",
  "rewind": "\u23EA",
  "previous_track_button": "\u23EE\uFE0F",
  "arrow_up_small": "\u{1F53C}",
  "arrow_double_up": "\u23EB",
  "arrow_down_small": "\u{1F53D}",
  "arrow_double_down": "\u23EC",
  "pause_button": "\u23F8\uFE0F",
  "stop_button": "\u23F9\uFE0F",
  "record_button": "\u23FA\uFE0F",
  "eject_button": "\u23CF\uFE0F",
  "cinema": "\u{1F3A6}",
  "low_brightness": "\u{1F505}",
  "high_brightness": "\u{1F506}",
  "signal_strength": "\u{1F4F6}",
  "vibration_mode": "\u{1F4F3}",
  "mobile_phone_off": "\u{1F4F4}",
  "female_sign": "\u2640\uFE0F",
  "male_sign": "\u2642\uFE0F",
  "transgender_symbol": "\u26A7\uFE0F",
  "heavy_multiplication_x": "\u2716\uFE0F",
  "heavy_plus_sign": "\u2795",
  "heavy_minus_sign": "\u2796",
  "heavy_division_sign": "\u2797",
  "infinity": "\u267E\uFE0F",
  "bangbang": "\u203C\uFE0F",
  "interrobang": "\u2049\uFE0F",
  "question": "\u2753",
  "grey_question": "\u2754",
  "grey_exclamation": "\u2755",
  "exclamation": "\u2757",
  "heavy_exclamation_mark": "\u2757",
  "wavy_dash": "\u3030\uFE0F",
  "currency_exchange": "\u{1F4B1}",
  "heavy_dollar_sign": "\u{1F4B2}",
  "medical_symbol": "\u2695\uFE0F",
  "recycle": "\u267B\uFE0F",
  "fleur_de_lis": "\u269C\uFE0F",
  "trident": "\u{1F531}",
  "name_badge": "\u{1F4DB}",
  "beginner": "\u{1F530}",
  "o": "\u2B55",
  "white_check_mark": "\u2705",
  "ballot_box_with_check": "\u2611\uFE0F",
  "heavy_check_mark": "\u2714\uFE0F",
  "x": "\u274C",
  "negative_squared_cross_mark": "\u274E",
  "curly_loop": "\u27B0",
  "loop": "\u27BF",
  "part_alternation_mark": "\u303D\uFE0F",
  "eight_spoked_asterisk": "\u2733\uFE0F",
  "eight_pointed_black_star": "\u2734\uFE0F",
  "sparkle": "\u2747\uFE0F",
  "copyright": "\xA9\uFE0F",
  "registered": "\xAE\uFE0F",
  "tm": "\u2122\uFE0F",
  "hash": "#\uFE0F\u20E3",
  "asterisk": "*\uFE0F\u20E3",
  "zero": "0\uFE0F\u20E3",
  "one": "1\uFE0F\u20E3",
  "two": "2\uFE0F\u20E3",
  "three": "3\uFE0F\u20E3",
  "four": "4\uFE0F\u20E3",
  "five": "5\uFE0F\u20E3",
  "six": "6\uFE0F\u20E3",
  "seven": "7\uFE0F\u20E3",
  "eight": "8\uFE0F\u20E3",
  "nine": "9\uFE0F\u20E3",
  "keycap_ten": "\u{1F51F}",
  "capital_abcd": "\u{1F520}",
  "abcd": "\u{1F521}",
  "symbols": "\u{1F523}",
  "abc": "\u{1F524}",
  "a": "\u{1F170}\uFE0F",
  "ab": "\u{1F18E}",
  "b": "\u{1F171}\uFE0F",
  "cl": "\u{1F191}",
  "cool": "\u{1F192}",
  "free": "\u{1F193}",
  "information_source": "\u2139\uFE0F",
  "id": "\u{1F194}",
  "m": "\u24C2\uFE0F",
  "new": "\u{1F195}",
  "ng": "\u{1F196}",
  "o2": "\u{1F17E}\uFE0F",
  "ok": "\u{1F197}",
  "parking": "\u{1F17F}\uFE0F",
  "sos": "\u{1F198}",
  "up": "\u{1F199}",
  "vs": "\u{1F19A}",
  "koko": "\u{1F201}",
  "sa": "\u{1F202}\uFE0F",
  "ideograph_advantage": "\u{1F250}",
  "accept": "\u{1F251}",
  "congratulations": "\u3297\uFE0F",
  "secret": "\u3299\uFE0F",
  "u6e80": "\u{1F235}",
  "red_circle": "\u{1F534}",
  "orange_circle": "\u{1F7E0}",
  "yellow_circle": "\u{1F7E1}",
  "green_circle": "\u{1F7E2}",
  "large_blue_circle": "\u{1F535}",
  "purple_circle": "\u{1F7E3}",
  "brown_circle": "\u{1F7E4}",
  "black_circle": "\u26AB",
  "white_circle": "\u26AA",
  "red_square": "\u{1F7E5}",
  "orange_square": "\u{1F7E7}",
  "yellow_square": "\u{1F7E8}",
  "green_square": "\u{1F7E9}",
  "blue_square": "\u{1F7E6}",
  "purple_square": "\u{1F7EA}",
  "brown_square": "\u{1F7EB}",
  "black_large_square": "\u2B1B",
  "white_large_square": "\u2B1C",
  "black_medium_square": "\u25FC\uFE0F",
  "white_medium_square": "\u25FB\uFE0F",
  "black_medium_small_square": "\u25FE",
  "white_medium_small_square": "\u25FD",
  "black_small_square": "\u25AA\uFE0F",
  "white_small_square": "\u25AB\uFE0F",
  "large_orange_diamond": "\u{1F536}",
  "large_blue_diamond": "\u{1F537}",
  "small_orange_diamond": "\u{1F538}",
  "small_blue_diamond": "\u{1F539}",
  "small_red_triangle": "\u{1F53A}",
  "small_red_triangle_down": "\u{1F53B}",
  "diamond_shape_with_a_dot_inside": "\u{1F4A0}",
  "radio_button": "\u{1F518}",
  "white_square_button": "\u{1F533}",
  "black_square_button": "\u{1F532}",
  "checkered_flag": "\u{1F3C1}",
  "triangular_flag_on_post": "\u{1F6A9}",
  "crossed_flags": "\u{1F38C}",
  "black_flag": "\u{1F3F4}",
  "white_flag": "\u{1F3F3}\uFE0F",
  "rainbow_flag": "\u{1F3F3}\uFE0F\u200D\u{1F308}",
  "transgender_flag": "\u{1F3F3}\uFE0F\u200D\u26A7\uFE0F",
  "pirate_flag": "\u{1F3F4}\u200D\u2620\uFE0F",
  "ascension_island": "\u{1F1E6}\u{1F1E8}",
  "andorra": "\u{1F1E6}\u{1F1E9}",
  "united_arab_emirates": "\u{1F1E6}\u{1F1EA}",
  "afghanistan": "\u{1F1E6}\u{1F1EB}",
  "antigua_barbuda": "\u{1F1E6}\u{1F1EC}",
  "anguilla": "\u{1F1E6}\u{1F1EE}",
  "albania": "\u{1F1E6}\u{1F1F1}",
  "armenia": "\u{1F1E6}\u{1F1F2}",
  "angola": "\u{1F1E6}\u{1F1F4}",
  "antarctica": "\u{1F1E6}\u{1F1F6}",
  "argentina": "\u{1F1E6}\u{1F1F7}",
  "american_samoa": "\u{1F1E6}\u{1F1F8}",
  "austria": "\u{1F1E6}\u{1F1F9}",
  "australia": "\u{1F1E6}\u{1F1FA}",
  "aruba": "\u{1F1E6}\u{1F1FC}",
  "aland_islands": "\u{1F1E6}\u{1F1FD}",
  "azerbaijan": "\u{1F1E6}\u{1F1FF}",
  "bosnia_herzegovina": "\u{1F1E7}\u{1F1E6}",
  "barbados": "\u{1F1E7}\u{1F1E7}",
  "bangladesh": "\u{1F1E7}\u{1F1E9}",
  "belgium": "\u{1F1E7}\u{1F1EA}",
  "burkina_faso": "\u{1F1E7}\u{1F1EB}",
  "bulgaria": "\u{1F1E7}\u{1F1EC}",
  "bahrain": "\u{1F1E7}\u{1F1ED}",
  "burundi": "\u{1F1E7}\u{1F1EE}",
  "benin": "\u{1F1E7}\u{1F1EF}",
  "st_barthelemy": "\u{1F1E7}\u{1F1F1}",
  "bermuda": "\u{1F1E7}\u{1F1F2}",
  "brunei": "\u{1F1E7}\u{1F1F3}",
  "bolivia": "\u{1F1E7}\u{1F1F4}",
  "caribbean_netherlands": "\u{1F1E7}\u{1F1F6}",
  "brazil": "\u{1F1E7}\u{1F1F7}",
  "bahamas": "\u{1F1E7}\u{1F1F8}",
  "bhutan": "\u{1F1E7}\u{1F1F9}",
  "bouvet_island": "\u{1F1E7}\u{1F1FB}",
  "botswana": "\u{1F1E7}\u{1F1FC}",
  "belarus": "\u{1F1E7}\u{1F1FE}",
  "belize": "\u{1F1E7}\u{1F1FF}",
  "canada": "\u{1F1E8}\u{1F1E6}",
  "cocos_islands": "\u{1F1E8}\u{1F1E8}",
  "congo_kinshasa": "\u{1F1E8}\u{1F1E9}",
  "central_african_republic": "\u{1F1E8}\u{1F1EB}",
  "congo_brazzaville": "\u{1F1E8}\u{1F1EC}",
  "switzerland": "\u{1F1E8}\u{1F1ED}",
  "cote_divoire": "\u{1F1E8}\u{1F1EE}",
  "cook_islands": "\u{1F1E8}\u{1F1F0}",
  "chile": "\u{1F1E8}\u{1F1F1}",
  "cameroon": "\u{1F1E8}\u{1F1F2}",
  "cn": "\u{1F1E8}\u{1F1F3}",
  "colombia": "\u{1F1E8}\u{1F1F4}",
  "clipperton_island": "\u{1F1E8}\u{1F1F5}",
  "costa_rica": "\u{1F1E8}\u{1F1F7}",
  "cuba": "\u{1F1E8}\u{1F1FA}",
  "cape_verde": "\u{1F1E8}\u{1F1FB}",
  "curacao": "\u{1F1E8}\u{1F1FC}",
  "christmas_island": "\u{1F1E8}\u{1F1FD}",
  "cyprus": "\u{1F1E8}\u{1F1FE}",
  "czech_republic": "\u{1F1E8}\u{1F1FF}",
  "de": "\u{1F1E9}\u{1F1EA}",
  "diego_garcia": "\u{1F1E9}\u{1F1EC}",
  "djibouti": "\u{1F1E9}\u{1F1EF}",
  "denmark": "\u{1F1E9}\u{1F1F0}",
  "dominica": "\u{1F1E9}\u{1F1F2}",
  "dominican_republic": "\u{1F1E9}\u{1F1F4}",
  "algeria": "\u{1F1E9}\u{1F1FF}",
  "ceuta_melilla": "\u{1F1EA}\u{1F1E6}",
  "ecuador": "\u{1F1EA}\u{1F1E8}",
  "estonia": "\u{1F1EA}\u{1F1EA}",
  "egypt": "\u{1F1EA}\u{1F1EC}",
  "western_sahara": "\u{1F1EA}\u{1F1ED}",
  "eritrea": "\u{1F1EA}\u{1F1F7}",
  "es": "\u{1F1EA}\u{1F1F8}",
  "ethiopia": "\u{1F1EA}\u{1F1F9}",
  "eu": "\u{1F1EA}\u{1F1FA}",
  "european_union": "\u{1F1EA}\u{1F1FA}",
  "finland": "\u{1F1EB}\u{1F1EE}",
  "fiji": "\u{1F1EB}\u{1F1EF}",
  "falkland_islands": "\u{1F1EB}\u{1F1F0}",
  "micronesia": "\u{1F1EB}\u{1F1F2}",
  "faroe_islands": "\u{1F1EB}\u{1F1F4}",
  "fr": "\u{1F1EB}\u{1F1F7}",
  "gabon": "\u{1F1EC}\u{1F1E6}",
  "gb": "\u{1F1EC}\u{1F1E7}",
  "uk": "\u{1F1EC}\u{1F1E7}",
  "grenada": "\u{1F1EC}\u{1F1E9}",
  "georgia": "\u{1F1EC}\u{1F1EA}",
  "french_guiana": "\u{1F1EC}\u{1F1EB}",
  "guernsey": "\u{1F1EC}\u{1F1EC}",
  "ghana": "\u{1F1EC}\u{1F1ED}",
  "gibraltar": "\u{1F1EC}\u{1F1EE}",
  "greenland": "\u{1F1EC}\u{1F1F1}",
  "gambia": "\u{1F1EC}\u{1F1F2}",
  "guinea": "\u{1F1EC}\u{1F1F3}",
  "guadeloupe": "\u{1F1EC}\u{1F1F5}",
  "equatorial_guinea": "\u{1F1EC}\u{1F1F6}",
  "greece": "\u{1F1EC}\u{1F1F7}",
  "south_georgia_south_sandwich_islands": "\u{1F1EC}\u{1F1F8}",
  "guatemala": "\u{1F1EC}\u{1F1F9}",
  "guam": "\u{1F1EC}\u{1F1FA}",
  "guinea_bissau": "\u{1F1EC}\u{1F1FC}",
  "guyana": "\u{1F1EC}\u{1F1FE}",
  "hong_kong": "\u{1F1ED}\u{1F1F0}",
  "heard_mcdonald_islands": "\u{1F1ED}\u{1F1F2}",
  "honduras": "\u{1F1ED}\u{1F1F3}",
  "croatia": "\u{1F1ED}\u{1F1F7}",
  "haiti": "\u{1F1ED}\u{1F1F9}",
  "hungary": "\u{1F1ED}\u{1F1FA}",
  "canary_islands": "\u{1F1EE}\u{1F1E8}",
  "indonesia": "\u{1F1EE}\u{1F1E9}",
  "ireland": "\u{1F1EE}\u{1F1EA}",
  "israel": "\u{1F1EE}\u{1F1F1}",
  "isle_of_man": "\u{1F1EE}\u{1F1F2}",
  "india": "\u{1F1EE}\u{1F1F3}",
  "british_indian_ocean_territory": "\u{1F1EE}\u{1F1F4}",
  "iraq": "\u{1F1EE}\u{1F1F6}",
  "iran": "\u{1F1EE}\u{1F1F7}",
  "iceland": "\u{1F1EE}\u{1F1F8}",
  "it": "\u{1F1EE}\u{1F1F9}",
  "jersey": "\u{1F1EF}\u{1F1EA}",
  "jamaica": "\u{1F1EF}\u{1F1F2}",
  "jordan": "\u{1F1EF}\u{1F1F4}",
  "jp": "\u{1F1EF}\u{1F1F5}",
  "kenya": "\u{1F1F0}\u{1F1EA}",
  "kyrgyzstan": "\u{1F1F0}\u{1F1EC}",
  "cambodia": "\u{1F1F0}\u{1F1ED}",
  "kiribati": "\u{1F1F0}\u{1F1EE}",
  "comoros": "\u{1F1F0}\u{1F1F2}",
  "st_kitts_nevis": "\u{1F1F0}\u{1F1F3}",
  "north_korea": "\u{1F1F0}\u{1F1F5}",
  "kr": "\u{1F1F0}\u{1F1F7}",
  "kuwait": "\u{1F1F0}\u{1F1FC}",
  "cayman_islands": "\u{1F1F0}\u{1F1FE}",
  "kazakhstan": "\u{1F1F0}\u{1F1FF}",
  "laos": "\u{1F1F1}\u{1F1E6}",
  "lebanon": "\u{1F1F1}\u{1F1E7}",
  "st_lucia": "\u{1F1F1}\u{1F1E8}",
  "liechtenstein": "\u{1F1F1}\u{1F1EE}",
  "sri_lanka": "\u{1F1F1}\u{1F1F0}",
  "liberia": "\u{1F1F1}\u{1F1F7}",
  "lesotho": "\u{1F1F1}\u{1F1F8}",
  "lithuania": "\u{1F1F1}\u{1F1F9}",
  "luxembourg": "\u{1F1F1}\u{1F1FA}",
  "latvia": "\u{1F1F1}\u{1F1FB}",
  "libya": "\u{1F1F1}\u{1F1FE}",
  "morocco": "\u{1F1F2}\u{1F1E6}",
  "monaco": "\u{1F1F2}\u{1F1E8}",
  "moldova": "\u{1F1F2}\u{1F1E9}",
  "montenegro": "\u{1F1F2}\u{1F1EA}",
  "st_martin": "\u{1F1F2}\u{1F1EB}",
  "madagascar": "\u{1F1F2}\u{1F1EC}",
  "marshall_islands": "\u{1F1F2}\u{1F1ED}",
  "macedonia": "\u{1F1F2}\u{1F1F0}",
  "mali": "\u{1F1F2}\u{1F1F1}",
  "myanmar": "\u{1F1F2}\u{1F1F2}",
  "mongolia": "\u{1F1F2}\u{1F1F3}",
  "macau": "\u{1F1F2}\u{1F1F4}",
  "northern_mariana_islands": "\u{1F1F2}\u{1F1F5}",
  "martinique": "\u{1F1F2}\u{1F1F6}",
  "mauritania": "\u{1F1F2}\u{1F1F7}",
  "montserrat": "\u{1F1F2}\u{1F1F8}",
  "malta": "\u{1F1F2}\u{1F1F9}",
  "mauritius": "\u{1F1F2}\u{1F1FA}",
  "maldives": "\u{1F1F2}\u{1F1FB}",
  "malawi": "\u{1F1F2}\u{1F1FC}",
  "mexico": "\u{1F1F2}\u{1F1FD}",
  "malaysia": "\u{1F1F2}\u{1F1FE}",
  "mozambique": "\u{1F1F2}\u{1F1FF}",
  "namibia": "\u{1F1F3}\u{1F1E6}",
  "new_caledonia": "\u{1F1F3}\u{1F1E8}",
  "niger": "\u{1F1F3}\u{1F1EA}",
  "norfolk_island": "\u{1F1F3}\u{1F1EB}",
  "nigeria": "\u{1F1F3}\u{1F1EC}",
  "nicaragua": "\u{1F1F3}\u{1F1EE}",
  "netherlands": "\u{1F1F3}\u{1F1F1}",
  "norway": "\u{1F1F3}\u{1F1F4}",
  "nepal": "\u{1F1F3}\u{1F1F5}",
  "nauru": "\u{1F1F3}\u{1F1F7}",
  "niue": "\u{1F1F3}\u{1F1FA}",
  "new_zealand": "\u{1F1F3}\u{1F1FF}",
  "oman": "\u{1F1F4}\u{1F1F2}",
  "panama": "\u{1F1F5}\u{1F1E6}",
  "peru": "\u{1F1F5}\u{1F1EA}",
  "french_polynesia": "\u{1F1F5}\u{1F1EB}",
  "papua_new_guinea": "\u{1F1F5}\u{1F1EC}",
  "philippines": "\u{1F1F5}\u{1F1ED}",
  "pakistan": "\u{1F1F5}\u{1F1F0}",
  "poland": "\u{1F1F5}\u{1F1F1}",
  "st_pierre_miquelon": "\u{1F1F5}\u{1F1F2}",
  "pitcairn_islands": "\u{1F1F5}\u{1F1F3}",
  "puerto_rico": "\u{1F1F5}\u{1F1F7}",
  "palestinian_territories": "\u{1F1F5}\u{1F1F8}",
  "portugal": "\u{1F1F5}\u{1F1F9}",
  "palau": "\u{1F1F5}\u{1F1FC}",
  "paraguay": "\u{1F1F5}\u{1F1FE}",
  "qatar": "\u{1F1F6}\u{1F1E6}",
  "reunion": "\u{1F1F7}\u{1F1EA}",
  "romania": "\u{1F1F7}\u{1F1F4}",
  "serbia": "\u{1F1F7}\u{1F1F8}",
  "ru": "\u{1F1F7}\u{1F1FA}",
  "rwanda": "\u{1F1F7}\u{1F1FC}",
  "saudi_arabia": "\u{1F1F8}\u{1F1E6}",
  "solomon_islands": "\u{1F1F8}\u{1F1E7}",
  "seychelles": "\u{1F1F8}\u{1F1E8}",
  "sudan": "\u{1F1F8}\u{1F1E9}",
  "sweden": "\u{1F1F8}\u{1F1EA}",
  "singapore": "\u{1F1F8}\u{1F1EC}",
  "st_helena": "\u{1F1F8}\u{1F1ED}",
  "slovenia": "\u{1F1F8}\u{1F1EE}",
  "svalbard_jan_mayen": "\u{1F1F8}\u{1F1EF}",
  "slovakia": "\u{1F1F8}\u{1F1F0}",
  "sierra_leone": "\u{1F1F8}\u{1F1F1}",
  "san_marino": "\u{1F1F8}\u{1F1F2}",
  "senegal": "\u{1F1F8}\u{1F1F3}",
  "somalia": "\u{1F1F8}\u{1F1F4}",
  "suriname": "\u{1F1F8}\u{1F1F7}",
  "south_sudan": "\u{1F1F8}\u{1F1F8}",
  "sao_tome_principe": "\u{1F1F8}\u{1F1F9}",
  "el_salvador": "\u{1F1F8}\u{1F1FB}",
  "sint_maarten": "\u{1F1F8}\u{1F1FD}",
  "syria": "\u{1F1F8}\u{1F1FE}",
  "swaziland": "\u{1F1F8}\u{1F1FF}",
  "tristan_da_cunha": "\u{1F1F9}\u{1F1E6}",
  "turks_caicos_islands": "\u{1F1F9}\u{1F1E8}",
  "chad": "\u{1F1F9}\u{1F1E9}",
  "french_southern_territories": "\u{1F1F9}\u{1F1EB}",
  "togo": "\u{1F1F9}\u{1F1EC}",
  "thailand": "\u{1F1F9}\u{1F1ED}",
  "tajikistan": "\u{1F1F9}\u{1F1EF}",
  "tokelau": "\u{1F1F9}\u{1F1F0}",
  "timor_leste": "\u{1F1F9}\u{1F1F1}",
  "turkmenistan": "\u{1F1F9}\u{1F1F2}",
  "tunisia": "\u{1F1F9}\u{1F1F3}",
  "tonga": "\u{1F1F9}\u{1F1F4}",
  "tr": "\u{1F1F9}\u{1F1F7}",
  "trinidad_tobago": "\u{1F1F9}\u{1F1F9}",
  "tuvalu": "\u{1F1F9}\u{1F1FB}",
  "taiwan": "\u{1F1F9}\u{1F1FC}",
  "tanzania": "\u{1F1F9}\u{1F1FF}",
  "ukraine": "\u{1F1FA}\u{1F1E6}",
  "uganda": "\u{1F1FA}\u{1F1EC}",
  "us_outlying_islands": "\u{1F1FA}\u{1F1F2}",
  "united_nations": "\u{1F1FA}\u{1F1F3}",
  "us": "\u{1F1FA}\u{1F1F8}",
  "uruguay": "\u{1F1FA}\u{1F1FE}",
  "uzbekistan": "\u{1F1FA}\u{1F1FF}",
  "vatican_city": "\u{1F1FB}\u{1F1E6}",
  "st_vincent_grenadines": "\u{1F1FB}\u{1F1E8}",
  "venezuela": "\u{1F1FB}\u{1F1EA}",
  "british_virgin_islands": "\u{1F1FB}\u{1F1EC}",
  "us_virgin_islands": "\u{1F1FB}\u{1F1EE}",
  "vietnam": "\u{1F1FB}\u{1F1F3}",
  "vanuatu": "\u{1F1FB}\u{1F1FA}",
  "wallis_futuna": "\u{1F1FC}\u{1F1EB}",
  "samoa": "\u{1F1FC}\u{1F1F8}",
  "kosovo": "\u{1F1FD}\u{1F1F0}",
  "yemen": "\u{1F1FE}\u{1F1EA}",
  "mayotte": "\u{1F1FE}\u{1F1F9}",
  "south_africa": "\u{1F1FF}\u{1F1E6}",
  "zambia": "\u{1F1FF}\u{1F1F2}",
  "zimbabwe": "\u{1F1FF}\u{1F1FC}",
  "england": "\u{1F3F4}\u{E0067}\u{E0062}\u{E0065}\u{E006E}\u{E0067}\u{E007F}",
  "scotland": "\u{1F3F4}\u{E0067}\u{E0062}\u{E0073}\u{E0063}\u{E0074}\u{E007F}",
  "wales": "\u{1F3F4}\u{E0067}\u{E0062}\u{E0077}\u{E006C}\u{E0073}\u{E007F}"
}`);
function withEmoji(str) {
  return str.replace(/:(\w+(_\w+))*:/gi, (m3, g1) => {
    return _emojiMap[g1] ?? m3;
  });
}

// src/renderer/icons.tsx
var IssueOpenIcon = () => /* @__PURE__ */ v(
  "svg",
  {
    className: "octicon octicon-issue-opened open",
    viewBox: "0 0 14 16",
    version: "1.1",
    width: "16",
    height: "16",
    "aria-hidden": "true"
  },
  /* @__PURE__ */ v(
    "path",
    {
      "fill-rule": "evenodd",
      d: "M7 2.3c3.14 0 5.7 2.56 5.7 5.7s-2.56 5.7-5.7 5.7A5.71 5.71 0 011.3 8c0-3.14 2.56-5.7 5.7-5.7zM7 1C3.14 1 0 4.14 0 8s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm1 3H6v5h2V4zm0 6H6v2h2v-2z"
    }
  )
);
var IssueClosedIcon = () => /* @__PURE__ */ v(
  "svg",
  {
    className: "octicon octicon-issue-closed closed",
    viewBox: "0 0 16 16",
    version: "1.1",
    width: "16",
    height: "16",
    "aria-hidden": "true"
  },
  /* @__PURE__ */ v(
    "path",
    {
      "fill-rule": "evenodd",
      d: "M7 10h2v2H7v-2zm2-6H7v5h2V4zm1.5 1.5l-1 1L12 9l4-4.5-1-1L12 7l-1.5-1.5zM8 13.7A5.71 5.71 0 012.3 8c0-3.14 2.56-5.7 5.7-5.7 1.83 0 3.45.88 4.5 2.2l.92-.92A6.947 6.947 0 008 1C4.14 1 1 4.14 1 8s3.14 7 7 7 7-3.14 7-7l-1.52 1.52c-.66 2.41-2.86 4.19-5.48 4.19v-.01z"
    }
  )
);
var PRIcon = () => /* @__PURE__ */ v(
  "svg",
  {
    className: "octicon octicon-git-merge merged",
    viewBox: "0 0 12 16",
    version: "1.1",
    width: "16",
    height: "16",
    "aria-hidden": "true"
  },
  /* @__PURE__ */ v(
    "path",
    {
      "fill-rule": "evenodd",
      d: "M10 7c-.73 0-1.38.41-1.73 1.02V8C7.22 7.98 6 7.64 5.14 6.98c-.75-.58-1.5-1.61-1.89-2.44A1.993 1.993 0 002 .99C.89.99 0 1.89 0 3a2 2 0 001 1.72v6.56c-.59.35-1 .99-1 1.72 0 1.11.89 2 2 2a1.993 1.993 0 001-3.72V7.67c.67.7 1.44 1.27 2.3 1.69.86.42 2.03.63 2.97.64v-.02c.36.61 1 1.02 1.73 1.02 1.11 0 2-.89 2-2 0-1.11-.89-2-2-2zm-6.8 6c0 .66-.55 1.2-1.2 1.2-.65 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2zM2 4.2C1.34 4.2.8 3.65.8 3c0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2zm8 6c-.66 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2z"
    }
  )
);

// src/renderer/renderer.tsx
var defaultMaxCount = 13;
var AllItems = ({ items: rawItems }) => {
  const [hidden, setHidden] = m2([]);
  const items = _2(() => {
    const seen = /* @__PURE__ */ new Set();
    return rawItems.filter((item) => {
      if (hidden.includes(item.id)) {
        return false;
      }
      if (seen.has(item.url)) {
        return false;
      }
      seen.add(item.url);
      return true;
    });
  }, [rawItems, hidden]);
  y(() => setHidden([]), [rawItems]);
  const hasManyRepos = items.some((item) => item.repository_url !== items[0].repository_url);
  const renderItem = (item) => /* @__PURE__ */ v(Item, { key: item.id, item, hide: () => setHidden([...hidden, item.id]), showRepo: hasManyRepos });
  if (items.length <= defaultMaxCount) {
    return /* @__PURE__ */ v("div", null, items.map(renderItem));
  }
  const [collapsed, setCollapsed] = m2(true);
  const di = collapsed ? items.slice(0, defaultMaxCount) : items;
  return /* @__PURE__ */ v("div", { className: "large" }, di.map(renderItem), /* @__PURE__ */ v("div", { className: "collapse" }, /* @__PURE__ */ v(CollapseButton, { n: items.length, setCollapsed, collapsed })));
};
var Item = ({ item, showRepo, hide }) => /* @__PURE__ */ v("div", { className: "item-row" }, /* @__PURE__ */ v("div", { className: "item-main" }, /* @__PURE__ */ v("div", { className: "item-state" }, item.pull_request ? /* @__PURE__ */ v(PRIcon, null) : item.closed_at ? /* @__PURE__ */ v(IssueClosedIcon, null) : /* @__PURE__ */ v(IssueOpenIcon, null)), /* @__PURE__ */ v("div", { style: { flex: "auto", flexBasis: 0 } }, showRepo && /* @__PURE__ */ v(RepoLabel, { url: item.repository_url }), /* @__PURE__ */ v("a", { href: item.html_url, className: "title" }, item.title), item.labels.map((label) => /* @__PURE__ */ v(Label, { label, key: label.id }))), /* @__PURE__ */ v("div", { className: "user" }, item.assignees?.map((user) => /* @__PURE__ */ v(Avatar, { user, key: user.id })))), /* @__PURE__ */ v("div", { className: "status" }, /* @__PURE__ */ v("span", null, "#", item.number, " opened ", new Date(item.created_at).toLocaleDateString(), " by ", item.user.login), /* @__PURE__ */ v("span", { style: { flex: 1 } }), /* @__PURE__ */ v("ul", { className: "actions" }, /* @__PURE__ */ v("li", null, /* @__PURE__ */ v("a", { role: "button", onClick: hide }, "Hide")))));
var RepoLabel = ({ url }) => {
  const match = /.+\/(.+\/.+)$/.exec(url);
  return match ? /* @__PURE__ */ v("a", { href: `https://github.com/${match[1]}`, className: "repo title" }, match[1]) : null;
};
var Label = ({ label }) => /* @__PURE__ */ v("span", { className: "label", key: label.id, style: { backgroundColor: `#${label.color}` } }, /* @__PURE__ */ v("a", { style: { color: getContrastColor(label.color) } }, withEmoji(label.name)));
var Avatar = ({ user }) => /* @__PURE__ */ v("a", { key: user.id, href: user.html_url }, /* @__PURE__ */ v("img", { src: user.avatar_url, width: "20", height: "20", alt: `@${user.login}` }));
var CollapseButton = ({ collapsed, setCollapsed, n: n2 }) => collapsed ? /* @__PURE__ */ v("span", { className: "more", onClick: () => setCollapsed(false) }, "\u25BC Show ", n2 - defaultMaxCount, " More") : /* @__PURE__ */ v("span", { className: "less", onClick: () => setCollapsed(true) }, "\u25B2 Show Less");
function getContrastColor(color) {
  const r3 = Number.parseInt(color.substr(0, 2), 16);
  const g3 = Number.parseInt(color.substr(2, 2), 16);
  const b2 = Number.parseInt(color.substr(4, 2), 16);
  return (0.299 * r3 + 0.587 * g3 + 0.114 * b2) / 255 > 0.5 ? "black" : "white";
}

// src/renderer/renderer.css
var renderer_default = ".item-row {\n	padding: 0.5em;\n	color: var(--theme-foreground);\n}\n.item-row:hover {\n	background-color: rgba(0, 0, 0, 0.1);\n}\n.item-main {\n	display: flex;\n	align-items: center;\n}\n.title {\n	color: var(--theme-foreground) !important;\n	font-size: 1.1em;\n	text-decoration: none;\n	margin-right: 3px;\n}\n.title:hover {\n	text-decoration: underline;\n}\n.title.repo {\n	opacity: 70%;\n	padding-right: 8px;\n}\n.label {\n	font-size: 0.85em;\n	margin: 0 2px;\n	padding: 1px 6px 2px 6px;\n	border-radius: 1em;\n}\n.label a {\n	/* Prevents wrap breaking on hypen */\n	display: inline-block;\n}\n.status {\n	display: flex;\n	align-items: center;\n	font-size: 0.8em;\n	opacity: 60%;\n	padding: 0.1em 0 0.1em 0.3em;\n}\n.assignee {\n	flex: shrink;\n}\n.user {\n	display: flex;\n}\n.user img {\n	padding: 0 0.1em;\n	border-radius: 20px;\n	border: solid 1px transparent;\n}\n.item-state {\n	padding-right: 0.3em;\n	opacity: 60%;\n}\n.item-state .octicon {\n	fill: var(--theme-foreground);\n	opacity: 0.7;\n	display: block;\n}\n.item-state .octicon.open {\n	fill: green;\n	opacity: 1;\n}\n.stats {\n	text-align: center;\n	font-size: 0.7em;\n	opacity: 60%;\n	padding-top: 0.6em;\n}\n.collapse {\n	text-align: center;\n	font-size: 0.9em;\n	cursor: pointer;\n	padding: 0.3em 0;\n}\n.collapse:hover > span,\n.collapse:focus > span {\n	background: var(--theme-button-hover-background);\n}\n.collapse > span {\n	color: var(--theme-button-foreground);\n	background: var(--theme-button-background);\n	padding: 3px;\n}\n.item-row .start-working {\n	display: none;\n}\n.item-row .start-working a {\n	color: var(--theme-foreground) !important;\n	font-size: 0.9em;\n	text-decoration: none;\n}\n.item-row .start-working a:hover {\n	text-decoration: underline;\n}\n.item-row:hover .start-working {\n	display: inline;\n}\n.item-row .actions {\n	display: none;\n	margin: 0;\n	padding: 0;\n}\n.item-row:hover .actions,\n.item-row:focus-within .actions {\n	display: flex;\n}\n.item-row .actions li {\n	list-style-type: none;\n	padding: 0;\n	cursor: pointer;\n}\n\n.item-row .actions li a {\n	color: inherit;\n}\n";

// src/renderer/index.tsx
var activate = () => {
  const style = document.createElement("style");
  style.type = "text/css";
  style.textContent = renderer_default;
  return {
    renderOutputItem(info, element) {
      let shadow = element.shadowRoot;
      if (!shadow) {
        shadow = element.attachShadow({ mode: "open" });
        shadow.append(style.cloneNode(true));
        const root = document.createElement("div");
        root.id = "root";
        shadow.append(root);
      }
      M(/* @__PURE__ */ v(AllItems, { items: info.json() }), shadow.querySelector("#root"));
    }
  };
};
export {
  activate
};
//# sourceMappingURL=renderer.js.map
