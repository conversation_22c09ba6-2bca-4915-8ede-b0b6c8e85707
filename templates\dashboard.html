{% extends "base.html" %}

{% block title %}仪表板 - Python Web应用{% endblock %}

{% block content %}
<div class="container-fluid">
    <h2 class="mb-4">
        <i class="fas fa-tachometer-alt"></i> 仪表板
        <small class="text-muted">欢迎回来, {{ session.username }}!</small>
    </h2>

    <!-- 统计卡片 -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stat-card">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ stats.file_count }}</h3>
                        <p class="mb-0">我的文件</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-file fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ stats.total_users }}</h3>
                        <p class="mb-0">总用户数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-users fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3>{{ stats.total_files }}</h3>
                        <p class="mb-0">总文件数</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-folder fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stat-card" style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);">
                <div class="d-flex justify-content-between">
                    <div>
                        <h3 class="timestamp">--:--</h3>
                        <p class="mb-0">当前时间</p>
                    </div>
                    <div class="align-self-center">
                        <i class="fas fa-clock fa-2x"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 最近文件 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-history"></i> 最近上传的文件
                    </h5>
                </div>
                <div class="card-body">
                    {% if stats.recent_files %}
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>文件名</th>
                                        <th>大小</th>
                                        <th>上传时间</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for file in stats.recent_files %}
                                    <tr>
                                        <td>
                                            <i class="fas fa-file-alt text-primary"></i>
                                            {{ file[0] }}
                                        </td>
                                        <td>{{ "%.1f KB"|format(file[2]/1024) if file[2] else 'N/A' }}</td>
                                        <td>{{ file[1] }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    {% else %}
                        <div class="text-center text-muted py-4">
                            <i class="fas fa-inbox fa-3x mb-3"></i>
                            <p>还没有上传任何文件</p>
                            <a href="{{ url_for('upload_file') }}" class="btn btn-primary">
                                <i class="fas fa-upload"></i> 立即上传
                            </a>
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 数据统计图表 -->
        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-chart-bar"></i> 数据统计
                    </h5>
                </div>
                <div class="card-body">
                    <canvas id="statsChart" width="400" height="200"></canvas>
                </div>
            </div>
        </div>
    </div>

    <!-- 快速操作 -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-bolt"></i> 快速操作
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('upload_file') }}" class="btn btn-outline-primary w-100">
                                <i class="fas fa-upload fa-2x d-block mb-2"></i>
                                上传文件
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="{{ url_for('chat') }}" class="btn btn-outline-success w-100">
                                <i class="fas fa-comments fa-2x d-block mb-2"></i>
                                进入聊天室
                            </a>
                        </div>
                        <div class="col-md-3 mb-3">
                            <button onclick="loadSystemInfo()" class="btn btn-outline-info w-100">
                                <i class="fas fa-server fa-2x d-block mb-2"></i>
                                系统信息
                            </button>
                        </div>
                        <div class="col-md-3 mb-3">
                            <a href="/api/stats" target="_blank" class="btn btn-outline-warning w-100">
                                <i class="fas fa-chart-line fa-2x d-block mb-2"></i>
                                API统计
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // 加载统计图表
    function loadStatsChart() {
        fetch('/api/stats')
            .then(response => response.json())
            .then(data => {
                const ctx = document.getElementById('statsChart').getContext('2d');
                
                // 处理数据
                const registrationDates = data.daily_registrations.map(item => item[0]);
                const registrationCounts = data.daily_registrations.map(item => item[1]);
                const uploadDates = data.daily_uploads.map(item => item[0]);
                const uploadCounts = data.daily_uploads.map(item => item[1]);
                
                // 合并日期
                const allDates = [...new Set([...registrationDates, ...uploadDates])].sort();
                
                // 填充数据
                const regData = allDates.map(date => {
                    const index = registrationDates.indexOf(date);
                    return index >= 0 ? registrationCounts[index] : 0;
                });
                
                const uploadData = allDates.map(date => {
                    const index = uploadDates.indexOf(date);
                    return index >= 0 ? uploadCounts[index] : 0;
                });
                
                new Chart(ctx, {
                    type: 'line',
                    data: {
                        labels: allDates,
                        datasets: [{
                            label: '用户注册',
                            data: regData,
                            borderColor: 'rgb(75, 192, 192)',
                            backgroundColor: 'rgba(75, 192, 192, 0.2)',
                            tension: 0.1
                        }, {
                            label: '文件上传',
                            data: uploadData,
                            borderColor: 'rgb(255, 99, 132)',
                            backgroundColor: 'rgba(255, 99, 132, 0.2)',
                            tension: 0.1
                        }]
                    },
                    options: {
                        responsive: true,
                        plugins: {
                            title: {
                                display: true,
                                text: '最近7天活动统计'
                            }
                        },
                        scales: {
                            y: {
                                beginAtZero: true
                            }
                        }
                    }
                });
            })
            .catch(error => {
                console.error('加载统计数据失败:', error);
                document.getElementById('statsChart').style.display = 'none';
            });
    }

    // 页面加载完成后执行
    document.addEventListener('DOMContentLoaded', function() {
        loadStatsChart();
        
        // 定期刷新图表
        setInterval(loadStatsChart, 60000); // 每分钟刷新一次
    });
</script>
{% endblock %}
