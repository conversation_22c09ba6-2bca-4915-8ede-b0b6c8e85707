2025-07-31 13:26:34.204 [warning] Dir "/home/<USER>/.pixi/envs" is not watchable (directory does not exist)
2025-07-31 13:26:34.205 [info] Starting Environment refresh
2025-07-31 13:26:34.205 [info] Searching for interpreters in posix paths locator
2025-07-31 13:26:34.206 [info] Searching for pyenv environments
2025-07-31 13:26:34.206 [info] Searching for conda environments
2025-07-31 13:26:34.206 [info] Searching for global virtual environments
2025-07-31 13:26:34.206 [info] Searching for custom virtual environments
2025-07-31 13:26:34.207 [info] pyenv is not installed
2025-07-31 13:26:34.207 [info] Finished searching for pyenv environments: 93 milliseconds
2025-07-31 13:26:34.212 [info] Finished searching for custom virtual envs: 92 milliseconds
2025-07-31 13:26:34.212 [info] > conda info --json
2025-07-31 13:26:34.213 [info] > hatch env show --json
2025-07-31 13:26:34.213 [info] cwd: .
2025-07-31 13:26:34.213 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:26:34.214 [info] Finished searching for global virtual envs: 99 milliseconds
2025-07-31 13:26:34.252 [info] > /bin/python3 -I ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 13:26:34.254 [info] Finished searching for interpreters in posix paths locator: 159 milliseconds
2025-07-31 13:26:34.285 [error] [Error: Command failed: /bin/python3 -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
/bin/sh: 1: /bin/python3: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at Socket.<anonymous> (node:internal/child_process:456:11)
	at Socket.emit (node:events:518:28)
	at Pipe.<anonymous> (node:net:351:12)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: '/bin/python3 -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py'
}
2025-07-31 13:26:34.719 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:26:35.187 [info] Environments refresh paths discovered (event): 1092 milliseconds
2025-07-31 13:26:35.188 [info] Environments refresh paths discovered: 1093 milliseconds
2025-07-31 13:26:36.307 [info] > /bin/python3 -I ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py ./.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
2025-07-31 13:26:36.309 [error] [Error: Command failed: /bin/python3 -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py
/bin/sh: 1: /bin/python3: not found

	at genericNodeError (node:internal/errors:983:15)
	at wrappedFn (node:internal/errors:537:14)
	at ChildProcess.exithandler (node:child_process:414:12)
	at ChildProcess.emit (node:events:530:35)
	at maybeClose (node:internal/child_process:1101:16)
	at Socket.<anonymous> (node:internal/child_process:456:11)
	at Socket.emit (node:events:518:28)
	at Pipe.<anonymous> (node:net:351:12)] {
  code: 127,
  killed: false,
  signal: null,
  cmd: '/bin/python3 -I /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/get_output_via_markers.py /home/<USER>/.openvscode-server/extensions/ms-python.python-2025.4.0-universal/python_files/interpreterInfo.py'
}
2025-07-31 13:26:36.309 [info] Environments refresh finished (event): 2214 milliseconds
2025-07-31 13:26:36.310 [info] Environment refresh took 2216 milliseconds
2025-07-31 13:26:36.312 [info] > pyenv which python
2025-07-31 13:26:36.312 [info] cwd: .
2025-07-31 13:26:36.313 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:26:36.354 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:26:36.355 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:26:36.358 [error] Unable to start Jedi language server as a valid interpreter is not selected
2025-07-31 13:26:36.360 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:26:36.360 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:26:37.409 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:27:03.380 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:27:12.542 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:27:19.591 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:31:50.611 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:32:17.401 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:32:20.161 [info] Starting Environment refresh
2025-07-31 13:32:20.162 [info] Searching for interpreters in posix paths locator
2025-07-31 13:32:20.162 [info] Searching for pyenv environments
2025-07-31 13:32:20.162 [info] Searching for conda environments
2025-07-31 13:32:20.163 [info] Searching for global virtual environments
2025-07-31 13:32:20.163 [info] Searching for custom virtual environments
2025-07-31 13:32:20.165 [info] Finished searching for custom virtual envs: 3 milliseconds
2025-07-31 13:32:20.165 [info] pyenv is not installed
2025-07-31 13:32:20.165 [info] Finished searching for pyenv environments: 3 milliseconds
2025-07-31 13:32:20.165 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:32:20.166 [info] Finished searching for global virtual envs: 4 milliseconds
2025-07-31 13:32:20.170 [info] Active interpreter [/home/<USER>/bin/python3
2025-07-31 13:32:20.170 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:32:20.171 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:32:20.183 [info] Finished searching for interpreters in posix paths locator: 22 milliseconds
2025-07-31 13:32:20.319 [info] Environments refresh paths discovered (event): 159 milliseconds
2025-07-31 13:32:20.320 [info] Environments refresh finished (event): 159 milliseconds
2025-07-31 13:32:20.320 [info] Environments refresh paths discovered: 160 milliseconds
2025-07-31 13:32:20.321 [info] Environment refresh took 161 milliseconds
2025-07-31 13:32:25.072 [info] Selected workspace /home/<USER>
2025-07-31 13:32:25.074 [info] Starting Environment refresh
2025-07-31 13:32:25.074 [info] Searching for interpreters in posix paths locator
2025-07-31 13:32:25.075 [info] Searching for pyenv environments
2025-07-31 13:32:25.075 [info] Searching for conda environments
2025-07-31 13:32:25.075 [info] Searching for global virtual environments
2025-07-31 13:32:25.075 [info] Searching for custom virtual environments
2025-07-31 13:32:25.076 [info] Finished searching for custom virtual envs: 1 milliseconds
2025-07-31 13:32:25.077 [info] pyenv is not installed
2025-07-31 13:32:25.077 [info] Finished searching for pyenv environments: 1 milliseconds
2025-07-31 13:32:25.077 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:32:25.077 [info] Finished searching for global virtual envs: 2 milliseconds
2025-07-31 13:32:25.081 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:32:25.081 [warning] Failed to check if /bin/python3 is an executable [Error: ENOENT: no such file or directory, lstat '/bin/python3'] {
  errno: -2,
  code: 'ENOENT',
  syscall: 'lstat',
  path: '/bin/python3'
}
2025-07-31 13:32:25.082 [info] Finished searching for interpreters in posix paths locator: 8 milliseconds
2025-07-31 13:32:25.221 [info] Environments refresh paths discovered (event): 147 milliseconds
2025-07-31 13:32:25.221 [info] Environments refresh finished (event): 147 milliseconds
2025-07-31 13:32:25.222 [info] Environments refresh paths discovered: 147 milliseconds
2025-07-31 13:32:25.222 [info] Environment refresh took 148 milliseconds
2025-07-31 13:32:45.105 [error] Virtual env creation requires an interpreter.
