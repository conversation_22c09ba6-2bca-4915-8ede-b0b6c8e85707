steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - task: AzureKeyVault@2
    displayName: "Azure Key Vault: Get Secrets"
    inputs:
      azureSubscription: vscode
      KeyVaultName: vscode-build-secrets
      SecretsFilter: "github-distro-mixin-password"

  - task: AzureKeyVault@2
    displayName: "Azure Key Vault: Get ESRP Secrets"
    inputs:
      azureSubscription: vscode-esrp
      KeyVaultName: vscode-esrp
      SecretsFilter: esrp-auth,esrp-sign

  # allow-any-unicode-next-line
  - pwsh: Write-Host "##vso[build.addbuildtag]🚀"
    displayName: Add build tag

  - pwsh: |
      npm ci
    workingDirectory: build
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Install build dependencies

  - download: current
    patterns: "**/artifacts_processed_*.txt"
    displayName: Download all artifacts_processed text files

  - task: AzureCLI@2
    displayName: Fetch secrets
    inputs:
      azureSubscription: vscode
      scriptType: pscore
      scriptLocation: inlineScript
      addSpnToEnvironment: true
      inlineScript: |
        Write-Host "##vso[task.setvariable variable=AZURE_TENANT_ID]$env:tenantId"
        Write-Host "##vso[task.setvariable variable=AZURE_CLIENT_ID]$env:servicePrincipalId"
        Write-Host "##vso[task.setvariable variable=AZURE_ID_TOKEN;issecret=true]$env:idToken"

  - pwsh: |
      . build/azure-pipelines/win32/exec.ps1

      if (Test-Path "$(Pipeline.Workspace)/artifacts_processed_*/artifacts_processed_*.txt") {
        Write-Host "Artifacts already processed so a build must have already been created."
        return
      }

      $VERSION = node -p "require('./package.json').version"
      Write-Host "Creating build with version: $VERSION"
      exec { node build/azure-pipelines/common/createBuild.js $VERSION }
    env:
      AZURE_TENANT_ID: "$(AZURE_TENANT_ID)"
      AZURE_CLIENT_ID: "$(AZURE_CLIENT_ID)"
      AZURE_ID_TOKEN: "$(AZURE_ID_TOKEN)"
    displayName: Create build if it hasn't been created before

  - pwsh: |
      $publishAuthTokens = (node build/azure-pipelines/common/getPublishAuthTokens)
      Write-Host "##vso[task.setvariable variable=PUBLISH_AUTH_TOKENS;issecret=true]$publishAuthTokens"
    env:
      AZURE_TENANT_ID: "$(AZURE_TENANT_ID)"
      AZURE_CLIENT_ID: "$(AZURE_CLIENT_ID)"
      AZURE_ID_TOKEN: "$(AZURE_ID_TOKEN)"
    displayName: Get publish auth tokens

  - pwsh: node build/azure-pipelines/common/publish.js
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
      AZURE_TENANT_ID: "$(AZURE_TENANT_ID)"
      AZURE_CLIENT_ID: "$(AZURE_CLIENT_ID)"
      AZURE_ID_TOKEN: "$(AZURE_ID_TOKEN)"
      SYSTEM_ACCESSTOKEN: $(System.AccessToken)
      PUBLISH_AUTH_TOKENS: "$(PUBLISH_AUTH_TOKENS)"
      RELEASE_TENANT_ID: "$(ESRP_TENANT_ID)"
      RELEASE_CLIENT_ID: "$(ESRP_CLIENT_ID)"
      RELEASE_AUTH_CERT: "$(esrp-auth)"
      RELEASE_REQUEST_SIGNING_CERT: "$(esrp-sign)"
    displayName: Process artifacts
    retryCountOnTaskFailure: 3

  - task: 1ES.PublishPipelineArtifact@1
    inputs:
      targetPath: $(Pipeline.Workspace)/artifacts_processed_$(System.StageAttempt)/artifacts_processed_$(System.StageAttempt).txt
      artifactName: artifacts_processed_$(System.StageAttempt)
      sbomEnabled: false
    displayName: Publish the artifacts processed for this stage attempt
    condition: always()
