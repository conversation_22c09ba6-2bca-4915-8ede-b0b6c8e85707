{"runtimeTarget": {"name": ".NETCoreApp,Version=v9.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v9.0": {"rzls/10.0.0-preview.25322.1": {"dependencies": {"Microsoft.AspNetCore.Razor.LanguageServer": "10.0.0-preview.25322.1", "Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.DiaSymReader.Pdb2Pdb": "1.1.0-beta2-19575-01", "Microsoft.DotNet.XliffTasks": "9.0.0-beta.25255.5", "Microsoft.Net.Compilers.Toolset": "5.0.0-1.25316.1", "Roslyn.Diagnostics.Analyzers": "3.11.0-beta1.24508.2"}, "runtime": {"rzls.dll": {}}}, "Humanizer.Core/2.14.1": {"runtime": {"lib/net6.0/Humanizer.dll": {"assemblyVersion": "2.14.0.0", "fileVersion": "2.14.1.48190"}}}, "ICSharpCode.Decompiler/9.1.0.7988": {"dependencies": {"System.Collections.Immutable": "9.0.0", "System.Reflection.Metadata": "9.0.0"}, "runtime": {"lib/netstandard2.0/ICSharpCode.Decompiler.dll": {"assemblyVersion": "9.1.0.7988", "fileVersion": "9.1.0.7988"}}}, "MessagePack/2.5.192": {"dependencies": {"MessagePack.Annotations": "2.5.192", "Microsoft.NET.StringTools": "17.6.3"}, "runtime": {"lib/net6.0/MessagePack.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.192.54228"}}}, "MessagePack.Annotations/2.5.192": {"runtime": {"lib/netstandard2.0/MessagePack.Annotations.dll": {"assemblyVersion": "2.5.0.0", "fileVersion": "2.5.192.54228"}}}, "Microsoft.CodeAnalysis.Analyzers/3.12.0-beta1.25230.6": {}, "Microsoft.CodeAnalysis.AnalyzerUtilities/5.0.0-1.25316.1": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.Common": "5.0.0-1.25316.1", "System.Buffers": "4.5.1", "System.Collections.Immutable": "9.0.0", "System.Memory": "4.5.5", "System.Numerics.Vectors": "4.5.0", "System.Reflection.Metadata": "9.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Text.Encoding.CodePages": "7.0.0", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.AnalyzerUtilities.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}}, "Microsoft.CodeAnalysis.BannedApiAnalyzers/3.11.0-beta1.24508.2": {}, "Microsoft.CodeAnalysis.Common/5.0.0-1.25316.1": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "System.Collections.Immutable": "9.0.0", "System.Reflection.Metadata": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp/5.0.0-1.25316.1": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.Common": "5.0.0-1.25316.1", "System.Collections.Immutable": "9.0.0", "System.Reflection.Metadata": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.CSharp.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Features/5.0.0-1.25316.1": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.CSharp": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Common": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.Features": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-1.25316.1", "Microsoft.DiaSymReader": "2.0.0", "System.Collections.Immutable": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Diagnostics.EventLog": "9.0.0", "System.IO.Pipelines": "9.0.0", "System.Reflection.Metadata": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.CSharp.Features.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Features.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.CSharp.Workspaces/5.0.0-1.25316.1": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.CSharp": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Common": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-1.25316.1", "System.Collections.Immutable": "9.0.0", "System.Composition": "9.0.0", "System.IO.Pipelines": "9.0.0", "System.Reflection.Metadata": "9.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.CSharp.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0", "System.Data.DataSetExtensions": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.CodeAnalysis.Elfie.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.16"}}}, "Microsoft.CodeAnalysis.ExternalAccess.Razor.Features/5.0.0-1.25316.1": {"dependencies": {"Humanizer.Core": "2.14.1", "ICSharpCode.Decompiler": "9.1.0.7988", "Microsoft.CSharp": "4.7.0", "Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.CSharp.Features": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.LanguageServer.Protocol": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Remote.Workspaces": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-1.25316.1", "Microsoft.DiaSymReader": "2.0.0", "Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.ServiceHub.Client": "4.2.1017", "Microsoft.ServiceHub.Framework": "4.8.3", "Microsoft.VisualStudio.Composition": "17.12.20", "Microsoft.VisualStudio.Composition.Analyzers": "17.12.20", "Microsoft.VisualStudio.RemoteControl": "16.3.52", "Microsoft.VisualStudio.Telemetry": "17.14.8", "Microsoft.VisualStudio.Threading": "17.13.2", "Microsoft.VisualStudio.Utilities.Internal": "16.3.90", "Microsoft.Win32.Registry": "5.0.0", "Nerdbank.Streams": "2.11.79", "Newtonsoft.Json": "13.0.3", "StreamJsonRpc": "2.21.10", "System.CodeDom": "8.0.0", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Diagnostics.EventLog": "9.0.0", "System.IO.Pipelines": "9.0.0", "System.Management": "7.0.0", "System.Memory": "4.5.5", "System.Reflection.Metadata": "9.0.0", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.ExternalAccess.Razor.Features.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}}, "Microsoft.CodeAnalysis.Features/5.0.0-1.25316.1": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.AnalyzerUtilities": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.Common": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.Scripting.Common": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-1.25316.1", "Microsoft.DiaSymReader": "2.0.0", "System.Collections.Immutable": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Diagnostics.EventLog": "9.0.0", "System.IO.Pipelines": "9.0.0", "System.Reflection.Metadata": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.Features.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.Features.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.LanguageServer.Protocol/5.0.0-1.25316.1": {"dependencies": {"Humanizer.Core": "2.14.1", "ICSharpCode.Decompiler": "9.1.0.7988", "Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.CSharp": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.Features": "5.0.0-1.25316.1", "Microsoft.DiaSymReader": "2.0.0", "Microsoft.VisualStudio.Threading": "17.13.2", "Nerdbank.Streams": "2.11.79", "Newtonsoft.Json": "13.0.3", "StreamJsonRpc": "2.21.10", "System.Collections.Immutable": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Diagnostics.EventLog": "9.0.0", "System.IO.Pipelines": "9.0.0", "System.Reflection.Metadata": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Text.Encodings.Web": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.LanguageServer.Protocol.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.LanguageServer.Protocol.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.PublicApiAnalyzers/3.11.0-beta1.24508.2": {}, "Microsoft.CodeAnalysis.Remote.Workspaces/5.0.0-1.25316.1": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.Elfie": "1.0.0", "Microsoft.CodeAnalysis.Features": "5.0.0-1.25316.1", "Microsoft.DiaSymReader": "2.0.0", "Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.ServiceHub.Client": "4.2.1017", "Microsoft.ServiceHub.Framework": "4.8.3", "Microsoft.VisualStudio.Composition": "17.12.20", "Microsoft.VisualStudio.Composition.Analyzers": "17.12.20", "Microsoft.VisualStudio.Threading": "17.13.2", "Microsoft.VisualStudio.Utilities.Internal": "16.3.90", "Microsoft.Win32.Registry": "5.0.0", "Nerdbank.Streams": "2.11.79", "Newtonsoft.Json": "13.0.3", "StreamJsonRpc": "2.21.10", "System.Collections.Immutable": "9.0.0", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Configuration.ConfigurationManager": "9.0.0", "System.Diagnostics.EventLog": "9.0.0", "System.IO.Pipelines": "9.0.0", "System.Reflection.Metadata": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0", "System.Text.Encodings.Web": "9.0.0", "System.Text.Json": "9.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net8.0/Microsoft.CodeAnalysis.Remote.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net8.0/cs/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "cs"}, "lib/net8.0/de/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "de"}, "lib/net8.0/es/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "es"}, "lib/net8.0/fr/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "fr"}, "lib/net8.0/it/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "it"}, "lib/net8.0/ja/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/Microsoft.CodeAnalysis.Remote.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Scripting.Common/5.0.0-1.25316.1": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.Common": "5.0.0-1.25316.1", "System.Collections.Immutable": "9.0.0", "System.Reflection.Metadata": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.Scripting.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.Scripting.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Workspaces.Common/5.0.0-1.25316.1": {"dependencies": {"Humanizer.Core": "2.14.1", "Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.CodeAnalysis.Common": "5.0.0-1.25316.1", "System.Collections.Immutable": "9.0.0", "System.Composition": "9.0.0", "System.IO.Pipelines": "9.0.0", "System.Reflection.Metadata": "9.0.0", "System.Threading.Channels": "7.0.0"}, "runtime": {"lib/net9.0/Microsoft.CodeAnalysis.Workspaces.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.25.31601"}}, "resources": {"lib/net9.0/cs/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "cs"}, "lib/net9.0/de/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "de"}, "lib/net9.0/es/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "es"}, "lib/net9.0/fr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "fr"}, "lib/net9.0/it/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "it"}, "lib/net9.0/ja/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ja"}, "lib/net9.0/ko/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ko"}, "lib/net9.0/pl/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pl"}, "lib/net9.0/pt-BR/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "pt-BR"}, "lib/net9.0/ru/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "ru"}, "lib/net9.0/tr/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "tr"}, "lib/net9.0/zh-Hans/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Hans"}, "lib/net9.0/zh-Hant/Microsoft.CodeAnalysis.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.DiaSymReader/2.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.DiaSymReader.dll": {"assemblyVersion": "2.0.0.0", "fileVersion": "2.0.23.22804"}}}, "Microsoft.DiaSymReader.Pdb2Pdb/1.1.0-beta2-19575-01": {}, "Microsoft.DotNet.XliffTasks/9.0.0-beta.25255.5": {}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0"}, "runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"runtime": {"lib/net9.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "Microsoft.Extensions.ObjectPool/8.0.0": {"runtime": {"lib/net8.0/Microsoft.Extensions.ObjectPool.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53112"}}}, "Microsoft.Net.Compilers.Toolset/5.0.0-1.25316.1": {}, "Microsoft.NET.StringTools/17.6.3": {"runtime": {"lib/net7.0/Microsoft.NET.StringTools.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "17.6.3.22601"}}}, "Microsoft.NETCore.Platforms/5.0.0": {}, "Microsoft.NETCore.Targets/1.1.0": {}, "Microsoft.ServiceHub.Analyzers/4.8.3": {}, "Microsoft.ServiceHub.Client/4.2.1017": {"dependencies": {"Microsoft.ServiceHub.Framework": "4.8.3", "Microsoft.ServiceHub.Resources": "4.2.1017", "Microsoft.VisualStudio.Telemetry": "17.14.8", "Microsoft.VisualStudio.Utilities.Internal": "16.3.90", "StreamJsonRpc": "2.21.10", "System.Collections.Immutable": "9.0.0"}, "runtime": {"lib/net6.0/Microsoft.ServiceHub.Client.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.1017.60695"}}}, "Microsoft.ServiceHub.Framework/4.8.3": {"dependencies": {"Microsoft.ServiceHub.Analyzers": "4.8.3", "Microsoft.VisualStudio.Composition": "17.12.20", "Microsoft.VisualStudio.Threading": "17.13.2", "Microsoft.VisualStudio.Validation": "17.8.8", "Nerdbank.Streams": "2.11.79", "StreamJsonRpc": "2.21.10", "System.Collections.Immutable": "9.0.0", "System.Text.Json": "9.0.0"}, "runtime": {"lib/net6.0/Microsoft.ServiceHub.Framework.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.3.36953"}}, "resources": {"lib/net6.0/cs/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.ServiceHub.Framework.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.ServiceHub.Resources/4.2.1017": {"runtime": {"lib/netstandard2.0/Microsoft.ServiceHub.Resources.dll": {"assemblyVersion": "*******", "fileVersion": "4.2.1017.60695"}}, "resources": {"lib/netstandard2.0/cs/Microsoft.ServiceHub.Resources.dll": {"locale": "cs"}, "lib/netstandard2.0/de/Microsoft.ServiceHub.Resources.dll": {"locale": "de"}, "lib/netstandard2.0/es/Microsoft.ServiceHub.Resources.dll": {"locale": "es"}, "lib/netstandard2.0/fr/Microsoft.ServiceHub.Resources.dll": {"locale": "fr"}, "lib/netstandard2.0/it/Microsoft.ServiceHub.Resources.dll": {"locale": "it"}, "lib/netstandard2.0/ja/Microsoft.ServiceHub.Resources.dll": {"locale": "ja"}, "lib/netstandard2.0/ko/Microsoft.ServiceHub.Resources.dll": {"locale": "ko"}, "lib/netstandard2.0/pl/Microsoft.ServiceHub.Resources.dll": {"locale": "pl"}, "lib/netstandard2.0/pt-BR/Microsoft.ServiceHub.Resources.dll": {"locale": "pt-BR"}, "lib/netstandard2.0/ru/Microsoft.ServiceHub.Resources.dll": {"locale": "ru"}, "lib/netstandard2.0/tr/Microsoft.ServiceHub.Resources.dll": {"locale": "tr"}, "lib/netstandard2.0/zh-Hans/Microsoft.ServiceHub.Resources.dll": {"locale": "zh-Hans"}, "lib/netstandard2.0/zh-Hant/Microsoft.ServiceHub.Resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Composition/17.12.20": {"dependencies": {"MessagePack": "2.5.192", "Microsoft.VisualStudio.Composition.Analyzers": "17.12.20", "Microsoft.VisualStudio.Validation": "17.8.8", "System.ComponentModel.Composition": "9.0.0", "System.Composition": "9.0.0", "System.Composition.AttributedModel": "9.0.0"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Composition.dll": {"assemblyVersion": "17.12.0.0", "fileVersion": "17.12.20.60194"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Composition.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Composition.Analyzers/17.12.20": {}, "Microsoft.VisualStudio.RemoteControl/16.3.52": {"dependencies": {"Microsoft.VisualStudio.Utilities.Internal": "16.3.90", "System.Configuration.ConfigurationManager": "9.0.0", "System.IO.FileSystem.AccessControl": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.RemoteControl.dll": {"assemblyVersion": "1*******", "fileVersion": "16.3.52.20732"}}}, "Microsoft.VisualStudio.RpcContracts/17.13.7": {"dependencies": {"Microsoft.ServiceHub.Framework": "4.8.3", "System.Text.Json": "9.0.0", "System.Threading.Tasks.Dataflow": "7.0.0"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.RpcContracts.dll": {"assemblyVersion": "17.13.7.0", "fileVersion": "17.13.7.19359"}}}, "Microsoft.VisualStudio.Telemetry/17.14.8": {"dependencies": {"Microsoft.CSharp": "4.7.0", "Microsoft.VisualStudio.RemoteControl": "16.3.52", "Microsoft.VisualStudio.Utilities.Internal": "16.3.90", "Microsoft.Win32.Registry": "5.0.0", "Newtonsoft.Json": "13.0.3", "System.Diagnostics.PerformanceCounter": "7.0.0", "System.Diagnostics.Tracing": "4.3.0", "System.Management": "7.0.0", "System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0", "System.Security.Principal.Windows": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Telemetry.dll": {"assemblyVersion": "********", "fileVersion": "17.14.8.58212"}}}, "Microsoft.VisualStudio.Threading/17.13.2": {"dependencies": {"Microsoft.VisualStudio.Threading.Analyzers": "17.13.2", "Microsoft.VisualStudio.Validation": "17.8.8"}, "runtime": {"lib/net6.0/Microsoft.VisualStudio.Threading.dll": {"assemblyVersion": "*********", "fileVersion": "17.13.2.4911"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Threading.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.VisualStudio.Threading.Analyzers/17.13.2": {}, "Microsoft.VisualStudio.Utilities.Internal/16.3.90": {"dependencies": {"Microsoft.Win32.Registry": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.VisualStudio.Utilities.Internal.dll": {"assemblyVersion": "1*******", "fileVersion": "16.3.90.25995"}}}, "Microsoft.VisualStudio.Validation/17.8.8": {"runtime": {"lib/net6.0/Microsoft.VisualStudio.Validation.dll": {"assemblyVersion": "17.8.0.0", "fileVersion": "17.8.8.15457"}}, "resources": {"lib/net6.0/cs/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "cs"}, "lib/net6.0/de/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "de"}, "lib/net6.0/es/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "es"}, "lib/net6.0/fr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "fr"}, "lib/net6.0/it/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "it"}, "lib/net6.0/ja/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ja"}, "lib/net6.0/ko/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ko"}, "lib/net6.0/pl/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pl"}, "lib/net6.0/pt-BR/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "pt-BR"}, "lib/net6.0/ru/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "ru"}, "lib/net6.0/tr/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "tr"}, "lib/net6.0/zh-Hans/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Hans"}, "lib/net6.0/zh-Hant/Microsoft.VisualStudio.Validation.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.Win32.Registry/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "Nerdbank.Streams/2.11.79": {"dependencies": {"Microsoft.VisualStudio.Threading": "17.13.2", "Microsoft.VisualStudio.Validation": "17.8.8", "System.IO.Pipelines": "9.0.0"}, "runtime": {"lib/net6.0/Nerdbank.Streams.dll": {"assemblyVersion": "********", "fileVersion": "2.11.79.1412"}}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/net6.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}}, "Roslyn.Diagnostics.Analyzers/3.11.0-beta1.24508.2": {"dependencies": {"Microsoft.CodeAnalysis.BannedApiAnalyzers": "3.11.0-beta1.24508.2", "Microsoft.CodeAnalysis.PublicApiAnalyzers": "3.11.0-beta1.24508.2"}}, "StreamJsonRpc/2.21.10": {"dependencies": {"MessagePack": "2.5.192", "Microsoft.VisualStudio.Threading": "17.13.2", "Microsoft.VisualStudio.Threading.Analyzers": "17.13.2", "Microsoft.VisualStudio.Validation": "17.8.8", "Nerdbank.Streams": "2.11.79", "Newtonsoft.Json": "13.0.3", "System.IO.Pipelines": "9.0.0"}, "runtime": {"lib/net8.0/StreamJsonRpc.dll": {"assemblyVersion": "********", "fileVersion": "2.21.10.36848"}}, "resources": {"lib/net8.0/cs/StreamJsonRpc.resources.dll": {"locale": "cs"}, "lib/net8.0/de/StreamJsonRpc.resources.dll": {"locale": "de"}, "lib/net8.0/es/StreamJsonRpc.resources.dll": {"locale": "es"}, "lib/net8.0/fr/StreamJsonRpc.resources.dll": {"locale": "fr"}, "lib/net8.0/it/StreamJsonRpc.resources.dll": {"locale": "it"}, "lib/net8.0/ja/StreamJsonRpc.resources.dll": {"locale": "ja"}, "lib/net8.0/ko/StreamJsonRpc.resources.dll": {"locale": "ko"}, "lib/net8.0/pl/StreamJsonRpc.resources.dll": {"locale": "pl"}, "lib/net8.0/pt-BR/StreamJsonRpc.resources.dll": {"locale": "pt-BR"}, "lib/net8.0/ru/StreamJsonRpc.resources.dll": {"locale": "ru"}, "lib/net8.0/tr/StreamJsonRpc.resources.dll": {"locale": "tr"}, "lib/net8.0/zh-Hans/StreamJsonRpc.resources.dll": {"locale": "zh-Hans"}, "lib/net8.0/zh-Hant/StreamJsonRpc.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "System.Buffers/4.5.1": {}, "System.CodeDom/8.0.0": {"runtime": {"lib/net8.0/System.CodeDom.dll": {"assemblyVersion": "*******", "fileVersion": "8.0.23.53103"}}}, "System.Collections.Immutable/9.0.0": {}, "System.ComponentModel.Composition/9.0.0": {"runtime": {"lib/net9.0/System.ComponentModel.Composition.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Composition/9.0.0": {"dependencies": {"System.Composition.AttributedModel": "9.0.0", "System.Composition.Convention": "9.0.0", "System.Composition.Hosting": "9.0.0", "System.Composition.Runtime": "9.0.0", "System.Composition.TypedParts": "9.0.0"}}, "System.Composition.AttributedModel/9.0.0": {"runtime": {"lib/net9.0/System.Composition.AttributedModel.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Composition.Convention/9.0.0": {"dependencies": {"System.Composition.AttributedModel": "9.0.0"}, "runtime": {"lib/net9.0/System.Composition.Convention.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Composition.Hosting/9.0.0": {"dependencies": {"System.Composition.Runtime": "9.0.0"}, "runtime": {"lib/net9.0/System.Composition.Hosting.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Composition.Runtime/9.0.0": {"runtime": {"lib/net9.0/System.Composition.Runtime.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Composition.TypedParts/9.0.0": {"dependencies": {"System.Composition.AttributedModel": "9.0.0", "System.Composition.Hosting": "9.0.0", "System.Composition.Runtime": "9.0.0"}, "runtime": {"lib/net9.0/System.Composition.TypedParts.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Configuration.ConfigurationManager/9.0.0": {"dependencies": {"System.Diagnostics.EventLog": "9.0.0", "System.Security.Cryptography.ProtectedData": "9.0.0"}, "runtime": {"lib/net9.0/System.Configuration.ConfigurationManager.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Data.DataSetExtensions/4.5.0": {}, "System.Diagnostics.EventLog/9.0.0": {"runtime": {"lib/net9.0/System.Diagnostics.EventLog.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}, "runtimeTargets": {"runtimes/win/lib/net9.0/System.Diagnostics.EventLog.Messages.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "0.0.0.0"}, "runtimes/win/lib/net9.0/System.Diagnostics.EventLog.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Diagnostics.PerformanceCounter/7.0.0": {"dependencies": {"System.Configuration.ConfigurationManager": "9.0.0"}, "runtime": {"lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Diagnostics.PerformanceCounter.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO.FileSystem.AccessControl/5.0.0": {"dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.Pipelines/9.0.0": {}, "System.Management/7.0.0": {"dependencies": {"System.CodeDom": "8.0.0"}, "runtime": {"lib/net7.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}, "runtimeTargets": {"runtimes/win/lib/net7.0/System.Management.dll": {"rid": "win", "assetType": "runtime", "assemblyVersion": "*******", "fileVersion": "7.0.22.51805"}}}, "System.Memory/4.5.5": {}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection.Metadata/9.0.0": {}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {}, "System.Security.AccessControl/5.0.0": {"dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.ProtectedData/9.0.0": {"runtime": {"lib/net9.0/System.Security.Cryptography.ProtectedData.dll": {"assemblyVersion": "*******", "fileVersion": "9.0.24.52809"}}}, "System.Security.Principal.Windows/5.0.0": {}, "System.Text.Encoding.CodePages/7.0.0": {}, "System.Text.Encodings.Web/9.0.0": {}, "System.Text.Json/9.0.0": {}, "System.Threading.Channels/7.0.0": {}, "System.Threading.Tasks.Dataflow/7.0.0": {}, "System.Threading.Tasks.Extensions/4.5.4": {}, "Microsoft.AspNetCore.Razor.LanguageServer/10.0.0-preview.25322.1": {"dependencies": {"Microsoft.CodeAnalysis.CSharp.Workspaces": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.LanguageServer.Protocol": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Razor.Compiler": "10.0.0-preview.25322.1", "Microsoft.CodeAnalysis.Razor.Workspaces": "10.0.0-preview.25322.1", "Microsoft.Extensions.DependencyInjection": "9.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.0", "Microsoft.VisualStudio.RpcContracts": "17.13.7"}, "runtime": {"Microsoft.AspNetCore.Razor.LanguageServer.dll": {"assemblyVersion": "10.0.0-preview.25322.1", "fileVersion": "10.0.25.32201"}}, "resources": {"cs/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "cs"}, "de/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "de"}, "es/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "es"}, "fr/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "fr"}, "it/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "it"}, "ja/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "ja"}, "ko/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "ko"}, "pl/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "ru"}, "tr/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.AspNetCore.Razor.LanguageServer.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.AspNetCore.Razor.Utilities.Shared/10.0.0-preview.25322.1": {"dependencies": {"Microsoft.CodeAnalysis.Analyzers": "3.12.0-beta1.25230.6", "Microsoft.Extensions.ObjectPool": "8.0.0", "Roslyn.Diagnostics.Analyzers": "3.11.0-beta1.24508.2"}, "runtime": {"Microsoft.AspNetCore.Razor.Utilities.Shared.dll": {"assemblyVersion": "10.0.0-preview.25322.1", "fileVersion": "10.0.25.32201"}}, "resources": {"cs/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "cs"}, "de/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "de"}, "es/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "es"}, "fr/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "fr"}, "it/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "it"}, "ja/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "ja"}, "ko/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "ko"}, "pl/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "ru"}, "tr/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.AspNetCore.Razor.Utilities.Shared.resources.dll": {"locale": "zh-Han<PERSON>"}}}, "Microsoft.CodeAnalysis.Razor.Compiler/10.0.0-preview.25322.1": {"dependencies": {"Microsoft.AspNetCore.Razor.Utilities.Shared": "10.0.0-preview.25322.1", "Microsoft.CodeAnalysis.CSharp": "5.0.0-1.25316.1"}, "runtime": {"Microsoft.CodeAnalysis.Razor.Compiler.dll": {"assemblyVersion": "10.0.0-preview.25322.1", "fileVersion": "10.0.25.32201"}}}, "Microsoft.CodeAnalysis.Razor.Workspaces/10.0.0-preview.25322.1": {"dependencies": {"Microsoft.AspNetCore.Razor.Utilities.Shared": "10.0.0-preview.25322.1", "Microsoft.CodeAnalysis.CSharp": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.ExternalAccess.Razor.Features": "5.0.0-1.25316.1", "Microsoft.CodeAnalysis.Razor.Compiler": "10.0.0-preview.25322.1", "Microsoft.CodeAnalysis.Workspaces.Common": "5.0.0-1.25316.1"}, "runtime": {"Microsoft.CodeAnalysis.Razor.Workspaces.dll": {"assemblyVersion": "10.0.0-preview.25322.1", "fileVersion": "10.0.25.32201"}}, "resources": {"cs/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "cs"}, "de/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "de"}, "es/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "es"}, "fr/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "fr"}, "it/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "it"}, "ja/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "ja"}, "ko/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "ko"}, "pl/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "pl"}, "pt-BR/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "pt-BR"}, "ru/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "ru"}, "tr/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "tr"}, "zh-Hans/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "zh-Hans"}, "zh-Hant/Microsoft.CodeAnalysis.Razor.Workspaces.resources.dll": {"locale": "zh-Han<PERSON>"}}}}}, "libraries": {"rzls/10.0.0-preview.25322.1": {"type": "project", "serviceable": false, "sha512": ""}, "Humanizer.Core/2.14.1": {"type": "package", "serviceable": true, "sha512": "sha512-lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw==", "path": "humanizer.core/2.14.1", "hashPath": "humanizer.core.2.14.1.nupkg.sha512"}, "ICSharpCode.Decompiler/9.1.0.7988": {"type": "package", "serviceable": true, "sha512": "sha512-N5HEh7n/9GpnkH6ojwPbzWbO1Ma+SFZCKafz69uRLnkt/bWzPON7wf7NLxTdU/fGWPnG2TEXRsu5DnRyvIQSaQ==", "path": "icsharpcode.decompiler/9.1.0.7988", "hashPath": "icsharpcode.decompiler.9.1.0.7988.nupkg.sha512"}, "MessagePack/2.5.192": {"type": "package", "serviceable": true, "sha512": "sha512-Jtle5MaFeIFkdXtxQeL9Tu2Y3HsAQGoSntOzrn6Br/jrl6c8QmG22GEioT5HBtZJR0zw0s46OnKU8ei2M3QifA==", "path": "messagepack/2.5.192", "hashPath": "messagepack.2.5.192.nupkg.sha512"}, "MessagePack.Annotations/2.5.192": {"type": "package", "serviceable": true, "sha512": "sha512-ja<PERSON><PERSON>w<PERSON>govWIZ8Zysdyf3b7b34/BrADw4v82GaEZymUhDd3ScMPrYd/cttekeDteJJPXseJxp04yTIcxiVUjTWg==", "path": "messagepack.annotations/2.5.192", "hashPath": "messagepack.annotations.2.5.192.nupkg.sha512"}, "Microsoft.CodeAnalysis.Analyzers/3.12.0-beta1.25230.6": {"type": "package", "serviceable": true, "sha512": "sha512-8QbwvhTp8KY+estpWR/bFasvSA0R4JT55tHP3ss3OqEZmw4ZdiufOe5ak2dYmhVTO7cJq/bNs5gOQicNYPmZ5A==", "path": "microsoft.codeanalysis.analyzers/3.12.0-beta1.25230.6", "hashPath": "microsoft.codeanalysis.analyzers.3.12.0-beta1.25230.6.nupkg.sha512"}, "Microsoft.CodeAnalysis.AnalyzerUtilities/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-S/RhaIgZD/raUOzKU9vt8sVGQyul8wnfWzoaQ0RllRB2qrJjKYOmD1B2lYEcPzIZKaBu3Jk46qqpDyir8ju0LA==", "path": "microsoft.codeanalysis.analyzerutilities/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.analyzerutilities.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.BannedApiAnalyzers/3.11.0-beta1.24508.2": {"type": "package", "serviceable": true, "sha512": "sha512-4OcMcKI+HZntce4KNnqhI22dHM+qkwjt52hkNkQ7z21x9prYWv+IinUZEvaOdVCSN1WAC22i1owtNLuMxBw5Pw==", "path": "microsoft.codeanalysis.bannedapianalyzers/3.11.0-beta1.24508.2", "hashPath": "microsoft.codeanalysis.bannedapianalyzers.3.11.0-beta1.24508.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.Common/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-tSu3Nnp08VnsHRiO6ADPO+oNazbaHxXlWJxbTR4lCrqEfIcGpLOTS36oAMvix407OHdDviyeparizHKGuoPg1Q==", "path": "microsoft.codeanalysis.common/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.common.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-r8UmEB8GTh3trwGBO7308SQ6kJZ5KLR0mgaVkDpoBdeXI8cjlxXkDHAwHPWY5WbU7Aq6sspD4wVcLfBZu7kpMg==", "path": "microsoft.codeanalysis.csharp/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.csharp.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Features/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-NSwiPw/1HmTYYJQSaybdesBlJZhNLsPuT5Qp9acVUXBsAhqd67m4Esb1+wWPk7A6bOWToS3DFSS6gVV/tUDfEg==", "path": "microsoft.codeanalysis.csharp.features/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.csharp.features.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.CSharp.Workspaces/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-qkefZXvoLgosd1JVdx/oVi1/9gpTgmeo5rDBpE6XR14B+JmR99k/qTkaEBd4yuaUVA3GfbcrUvL3b/EjHneslQ==", "path": "microsoft.codeanalysis.csharp.workspaces/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.csharp.workspaces.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Elfie/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-r12elUp4MRjdnRfxEP+xqVSUUfG3yIJTBEJGwbfvF5oU4m0jb9HC0gFG28V/dAkYGMkRmHVi3qvrnBLQSw9X3Q==", "path": "microsoft.codeanalysis.elfie/1.0.0", "hashPath": "microsoft.codeanalysis.elfie.1.0.0.nupkg.sha512"}, "Microsoft.CodeAnalysis.ExternalAccess.Razor.Features/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-l27iyi79WvRnnXKgf/PnX4QwJnlIp8su2Nx4TycbM8DdJkanVyOHj+gAfmg4LpQZiftV/yusC4RRTt98mjgMpA==", "path": "microsoft.codeanalysis.externalaccess.razor.features/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.externalaccess.razor.features.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Features/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-Kr+Oh6nB9hUHeZthTPJEC9QIuMUw17NGm165QTvU3MMr/MeNKJuGwpmnrKX2w/dkXaJZgTSfxLtepV4jyoEesg==", "path": "microsoft.codeanalysis.features/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.features.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.LanguageServer.Protocol/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-y/xZxx6WVBxVZEXnC5HxvJ9cX5iAlBS5P6i4SzmF+DpZ7fNskq8IqpOzNneX+CFGfBBzgunHWAsDROwTb0LevQ==", "path": "microsoft.codeanalysis.languageserver.protocol/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.languageserver.protocol.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.PublicApiAnalyzers/3.11.0-beta1.24508.2": {"type": "package", "serviceable": true, "sha512": "sha512-1VTnmmxVli2lRQ9iAnPdeGk4dxkpLnrv2x6lgJyKkYY/JixMvUUne81vsZuU71JsOn7YHVY9l7P6R8EbJEH2kg==", "path": "microsoft.codeanalysis.publicapianalyzers/3.11.0-beta1.24508.2", "hashPath": "microsoft.codeanalysis.publicapianalyzers.3.11.0-beta1.24508.2.nupkg.sha512"}, "Microsoft.CodeAnalysis.Remote.Workspaces/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-mduus/5eHAR/08n+IdpHbxCZA6uzIHOiyNz7amTNenaWrFPXcOBTiUhltAx8iqxuWVm0kc4tinlPtg1QDfgSpA==", "path": "microsoft.codeanalysis.remote.workspaces/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.remote.workspaces.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Scripting.Common/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-c80ZnvL2shOBZR/YBLcmh7FOeJQCV+LMZW0yqBlceSsvSvmmAZ/YKO1uYxHyLI6ZBwplsFEqC7jxExzujxQW0g==", "path": "microsoft.codeanalysis.scripting.common/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.scripting.common.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CodeAnalysis.Workspaces.Common/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-TsSRDKkkzEUdNkNWfDWm8Eqn2LQnmYs1v6rhN5gdX4HeUOpYRcP7Q1bUQhBum8o6QNimh3FRe1mm7ScweDb+9w==", "path": "microsoft.codeanalysis.workspaces.common/5.0.0-1.25316.1", "hashPath": "microsoft.codeanalysis.workspaces.common.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.DiaSymReader/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QcZrCETsBJqy/vQpFtJc+jSXQ0K5sucQ6NUFbTNVHD4vfZZOwjZ/3sBzczkC4DityhD3AVO/+K/+9ioLs1AgRA==", "path": "microsoft.diasymreader/2.0.0", "hashPath": "microsoft.diasymreader.2.0.0.nupkg.sha512"}, "Microsoft.DiaSymReader.Pdb2Pdb/1.1.0-beta2-19575-01": {"type": "package", "serviceable": true, "sha512": "sha512-kY6eTNkeWLHvfOjg97Q7tgQKrPpX+Y3fR6fS4nyfpgFLHBxHriLBR4v3e9t71it91gIMEeKUqOqrFJ7Pj48eHA==", "path": "microsoft.diasymreader.pdb2pdb/1.1.0-beta2-19575-01", "hashPath": "microsoft.diasymreader.pdb2pdb.1.1.0-beta2-19575-01.nupkg.sha512"}, "Microsoft.DotNet.XliffTasks/9.0.0-beta.25255.5": {"type": "package", "serviceable": true, "sha512": "sha512-bb0fZB5ViPscdfYeWlmtyXJMzNkgcpkV5RWmXktfV9lwIUZgNZmFotUXrdcTyZzrN7v1tQK/Y6BGnbkP9gEsXg==", "path": "microsoft.dotnet.xlifftasks/9.0.0-beta.25255.5", "hashPath": "microsoft.dotnet.xlifftasks.9.0.0-beta.25255.5.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MCPrg7v3QgNMr0vX4vzRXvkNGgLg8vKWX0nKCWUxu2uPyMsaRgiRc1tHBnbTcfJMhMKj2slE/j2M9oGkd25DNw==", "path": "microsoft.extensions.dependencyinjection/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+6f2qv2a3dLwd5w6JanPIPs47CxRbnk+ZocMJUhv9NxP88VlOcJYZs9jY+MYSjxvady08bUZn6qgiNh7DadGgg==", "path": "microsoft.extensions.dependencyinjection.abstractions/9.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-4pm+XgxSukskwjzDDfSjG4KNUIOdFF2VaqZZDtTzoyQMOVSnlV6ZM8a9aVu5dg9LVZTB//utzSc8fOi0b0Mb2Q==", "path": "microsoft.extensions.objectpool/8.0.0", "hashPath": "microsoft.extensions.objectpool.8.0.0.nupkg.sha512"}, "Microsoft.Net.Compilers.Toolset/5.0.0-1.25316.1": {"type": "package", "serviceable": true, "sha512": "sha512-5pBUepJTKH5IcZN0C4fRD5u80H3LeD7neu43TbU8gBvbzAL8kJNsV9BQHOUBQvZiVN94VK3Uk3syLFGs2FLVAw==", "path": "microsoft.net.compilers.toolset/5.0.0-1.25316.1", "hashPath": "microsoft.net.compilers.toolset.5.0.0-1.25316.1.nupkg.sha512"}, "Microsoft.NET.StringTools/17.6.3": {"type": "package", "serviceable": true, "sha512": "sha512-N0ZIanl1QCgvUumEL1laasU0a7sOE5ZwLZVTn0pAePnfhq8P7SvTjF8Axq+CnavuQkmdQpGNXQ1efZtu5kDFbA==", "path": "microsoft.net.stringtools/17.6.3", "hashPath": "microsoft.net.stringtools.17.6.3.nupkg.sha512"}, "Microsoft.NETCore.Platforms/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ==", "path": "microsoft.netcore.platforms/5.0.0", "hashPath": "microsoft.netcore.platforms.5.0.0.nupkg.sha512"}, "Microsoft.NETCore.Targets/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg==", "path": "microsoft.netcore.targets/1.1.0", "hashPath": "microsoft.netcore.targets.1.1.0.nupkg.sha512"}, "Microsoft.ServiceHub.Analyzers/4.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-U0US0D1BovhLVPXmo6DGhgBK+JiUwU10jKHwG1eF1bhxAikKFxB9OH7UdG6mR/iGyKLVvx1XqF4e6v2EargbMw==", "path": "microsoft.servicehub.analyzers/4.8.3", "hashPath": "microsoft.servicehub.analyzers.4.8.3.nupkg.sha512"}, "Microsoft.ServiceHub.Client/4.2.1017": {"type": "package", "serviceable": true, "sha512": "sha512-g6YVQbnHaVJy4UkpkAtOJbpTWYd42G7SxtG/csCNS44tDZcSxUr4ORwjjVm9hOEElgpOwbOv20xAWcedsnbFnA==", "path": "microsoft.servicehub.client/4.2.1017", "hashPath": "microsoft.servicehub.client.4.2.1017.nupkg.sha512"}, "Microsoft.ServiceHub.Framework/4.8.3": {"type": "package", "serviceable": true, "sha512": "sha512-JMTVNxP89uzsMHUSsUp9LrjyfOwlpHFSHzz5Mbl3xsIN4Ccb0iSDsYg9Bq3e2g76mUsy3xwyrvi8H91CWVkvmw==", "path": "microsoft.servicehub.framework/4.8.3", "hashPath": "microsoft.servicehub.framework.4.8.3.nupkg.sha512"}, "Microsoft.ServiceHub.Resources/4.2.1017": {"type": "package", "serviceable": true, "sha512": "sha512-BNsF3TOQRz1aCKmIvAot1Mo1D5oQbGQCN8KGITqMeTUVfnMkcJ12Te2yeRb/pX5JrKtXpxdg1EY6Es9HbSAD9A==", "path": "microsoft.servicehub.resources/4.2.1017", "hashPath": "microsoft.servicehub.resources.4.2.1017.nupkg.sha512"}, "Microsoft.VisualStudio.Composition/17.12.20": {"type": "package", "serviceable": true, "sha512": "sha512-yRjO6wI6bq7+B+4VSG2KaYC0ZN7LDS1PB6lZi+ERM0L0FRC2ESL+3RejnQifj2/dZJDECENK9OUc2hS5w/5csw==", "path": "microsoft.visualstudio.composition/17.12.20", "hashPath": "microsoft.visualstudio.composition.17.12.20.nupkg.sha512"}, "Microsoft.VisualStudio.Composition.Analyzers/17.12.20": {"type": "package", "serviceable": true, "sha512": "sha512-P5auqyL+DKNFL/E7e4KZbYLFCBVmQmqdYw6xVGIuuEVphjnYSCZ6QS4zl1iqjjRlRguW8EV1JLIF2j9ZaMledg==", "path": "microsoft.visualstudio.composition.analyzers/17.12.20", "hashPath": "microsoft.visualstudio.composition.analyzers.17.12.20.nupkg.sha512"}, "Microsoft.VisualStudio.RemoteControl/16.3.52": {"type": "package", "serviceable": true, "sha512": "sha512-+MgP1+Rtt1uJZyqhf7+H6KAQ57wc7v00ixuLhEgFggIbmW2/29YXfPK7gLvXw+vU7vimuM47cqAHrnB7RWYqtg==", "path": "microsoft.visualstudio.remotecontrol/16.3.52", "hashPath": "microsoft.visualstudio.remotecontrol.16.3.52.nupkg.sha512"}, "Microsoft.VisualStudio.RpcContracts/17.13.7": {"type": "package", "serviceable": true, "sha512": "sha512-pzfWDV4Fnv4JAqS+I8EgAFwFuM+RS77yQYeGHBV4/VyjqIlpvXrgNZpDW5tHeDnVCk+Ua1szk8d2wrGqn5WdQQ==", "path": "microsoft.visualstudio.rpccontracts/17.13.7", "hashPath": "microsoft.visualstudio.rpccontracts.17.13.7.nupkg.sha512"}, "Microsoft.VisualStudio.Telemetry/17.14.8": {"type": "package", "serviceable": true, "sha512": "sha512-m7Co6nDHuuALCWtOU9FbIE8d3FM5VLH6n05iQ6HSULzJCSTfd0GhQYpnbpyVS/79Yrg6uH1Awn2DYdtjl55qkQ==", "path": "microsoft.visualstudio.telemetry/17.14.8", "hashPath": "microsoft.visualstudio.telemetry.17.14.8.nupkg.sha512"}, "Microsoft.VisualStudio.Threading/17.13.2": {"type": "package", "serviceable": true, "sha512": "sha512-BmbIlTqUI5q9dUfpzW/CJXeQUefUhcLVvOgcsjAq8OqnSTS30e8D0Sj7Vmst+p+Y2eKJ33Ml0FlB9jKyPR4pZQ==", "path": "microsoft.visualstudio.threading/17.13.2", "hashPath": "microsoft.visualstudio.threading.17.13.2.nupkg.sha512"}, "Microsoft.VisualStudio.Threading.Analyzers/17.13.2": {"type": "package", "serviceable": true, "sha512": "sha512-Qcd8IlaTXZVq3wolBnzby1P7kWihdWaExtD8riumiKuG1sHa8EgjV/o70TMjTaeUMhomBbhfdC9OPwAHoZfnjQ==", "path": "microsoft.visualstudio.threading.analyzers/17.13.2", "hashPath": "microsoft.visualstudio.threading.analyzers.17.13.2.nupkg.sha512"}, "Microsoft.VisualStudio.Utilities.Internal/16.3.90": {"type": "package", "serviceable": true, "sha512": "sha512-joWRXcBhOAzEaBfgV4OHa++VESjShwUGB0kqy7vdEiJg08wXpxYt9afQDEwiwy8XGKPKabByYXA+CGMLxYo/TQ==", "path": "microsoft.visualstudio.utilities.internal/16.3.90", "hashPath": "microsoft.visualstudio.utilities.internal.16.3.90.nupkg.sha512"}, "Microsoft.VisualStudio.Validation/17.8.8": {"type": "package", "serviceable": true, "sha512": "sha512-rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g==", "path": "microsoft.visualstudio.validation/17.8.8", "hashPath": "microsoft.visualstudio.validation.17.8.8.nupkg.sha512"}, "Microsoft.Win32.Registry/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dDoKi0PnDz31yAyETfRntsLArTlVAVzUzCIvvEDsDsucrl33Dl8pIJG06ePTJTI3tGpeyHS9Cq7Foc/s4EeKcg==", "path": "microsoft.win32.registry/5.0.0", "hashPath": "microsoft.win32.registry.5.0.0.nupkg.sha512"}, "Nerdbank.Streams/2.11.79": {"type": "package", "serviceable": true, "sha512": "sha512-eF4uizOaGqde/sTA8pDf+fUMHrca4OKpIHbduBgYFRy/Sme8PMHDdZedfkDjBLlmyk7h2aYIiDXv+dE6lCb7Mg==", "path": "nerdbank.streams/2.11.79", "hashPath": "nerdbank.streams.2.11.79.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Roslyn.Diagnostics.Analyzers/3.11.0-beta1.24508.2": {"type": "package", "serviceable": true, "sha512": "sha512-/Wu5N82zpfLDCD6puzXCMJXLMyo6VQMyeuysMTSLphB2ZvU83fbj99H1YYywTDXEVa5x2mCWBcoPMj8LagQgVg==", "path": "roslyn.diagnostics.analyzers/3.11.0-beta1.24508.2", "hashPath": "roslyn.diagnostics.analyzers.3.11.0-beta1.24508.2.nupkg.sha512"}, "StreamJsonRpc/2.21.10": {"type": "package", "serviceable": true, "sha512": "sha512-wjBlFiaE+npUco9Jj4K11EEZfmAg4poRFYhcCJOiVdiHZ3UxapbGemtLNRcVYGXa/p8nqvZ38TfJPHnlrromDg==", "path": "streamjsonrpc/2.21.10", "hashPath": "streamjsonrpc.2.21.10.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.CodeDom/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-WTlRjL6KWIMr/pAaq3rYqh0TJlzpouaQ/W1eelssHgtlwHAH25jXTkUphTYx9HaIIf7XA6qs/0+YhtLEQRkJ+Q==", "path": "system.codedom/8.0.0", "hashPath": "system.codedom.8.0.0.nupkg.sha512"}, "System.Collections.Immutable/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-QhkXUl2gNrQtvPmtBTQHb0YsUrDiDQ2QS09YbtTTiSjGcf7NBqtYbrG/BE06zcBPCKEwQGzIv13IVdXNOSub2w==", "path": "system.collections.immutable/9.0.0", "hashPath": "system.collections.immutable.9.0.0.nupkg.sha512"}, "System.ComponentModel.Composition/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-HwA28KOsRUSN2Hiei4RvXKDeMlmQe/E6WemYDWY8Iv4AYFS04obS6rDVCE4UNDbc3bsa1Cg9tJ5Dko3T0eDjyA==", "path": "system.componentmodel.composition/9.0.0", "hashPath": "system.componentmodel.composition.9.0.0.nupkg.sha512"}, "System.Composition/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Djj70fFTraOarSKmRnmRy/zm4YurICm+kiCtI0dYRqGJnLX6nJ+G3WYuFJ173cAPax/gh96REcbNiVqcrypFQ==", "path": "system.composition/9.0.0", "hashPath": "system.composition.9.0.0.nupkg.sha512"}, "System.Composition.AttributedModel/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-iri00l/zIX9g4lHMY+Nz0qV1n40+jFYAmgsaiNn16xvt2RDwlqByNG4wgblagnDYxm3YSQQ0jLlC/7Xlk9CzyA==", "path": "system.composition.attributedmodel/9.0.0", "hashPath": "system.composition.attributedmodel.9.0.0.nupkg.sha512"}, "System.Composition.Convention/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-+vuqVP6xpi582XIjJi6OCsIxuoTZfR0M7WWufk3uGDeCl3wGW6KnpylUJ3iiXdPByPE0vR5TjJgR6hDLez4FQg==", "path": "system.composition.convention/9.0.0", "hashPath": "system.composition.convention.9.0.0.nupkg.sha512"}, "System.Composition.Hosting/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-OFqSeFeJYr7kHxDfaViGM1ymk7d4JxK//VSoNF9Ux0gpqkLsauDZpu89kTHHNdCWfSljbFcvAafGyBoY094btQ==", "path": "system.composition.hosting/9.0.0", "hashPath": "system.composition.hosting.9.0.0.nupkg.sha512"}, "System.Composition.Runtime/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-w1HOlQY1zsOWYussjFGZCEYF2UZXgvoYnS94NIu2CBnAGMbXFAX8PY8c92KwUItPmowal68jnVLBCzdrWLeEKA==", "path": "system.composition.runtime/9.0.0", "hashPath": "system.composition.runtime.9.0.0.nupkg.sha512"}, "System.Composition.TypedParts/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-aRZlojCCGEHDKqh43jaDgaVpYETsgd7Nx4g1zwLKMtv4iTo0627715ajEFNpEEBTgLmvZuv8K0EVxc3sM4NWJA==", "path": "system.composition.typedparts/9.0.0", "hashPath": "system.composition.typedparts.9.0.0.nupkg.sha512"}, "System.Configuration.ConfigurationManager/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-PdkuMrwDhXoKFo/JxISIi9E8L+QGn9Iquj2OKDWHB6Y/HnUOuBouF7uS3R4Hw3FoNmwwMo6hWgazQdyHIIs27A==", "path": "system.configuration.configurationmanager/9.0.0", "hashPath": "system.configuration.configurationmanager.9.0.0.nupkg.sha512"}, "System.Data.DataSetExtensions/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-221clPs1445HkTBZPL+K9sDBdJRB8UN8rgjO3ztB0CQ26z//fmJXtlsr6whGatscsKGBrhJl5bwJuKSA8mwFOw==", "path": "system.data.datasetextensions/4.5.0", "hashPath": "system.data.datasetextensions.4.5.0.nupkg.sha512"}, "System.Diagnostics.EventLog/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qd01+AqPhbAG14KtdtIqFk+cxHQFZ/oqRSCoxU1F+Q6Kv0cl726sl7RzU9yLFGd4BUOKdN4XojXF0pQf/R6YeA==", "path": "system.diagnostics.eventlog/9.0.0", "hashPath": "system.diagnostics.eventlog.9.0.0.nupkg.sha512"}, "System.Diagnostics.PerformanceCounter/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-L+zIMEaXp1vA4wZk1KLMpk6tvU0xy94R0IfmhkmTWeC4KwShsmAfbg5I19LgjsCTYp6GVdXZ2aHluVWL0QqBdA==", "path": "system.diagnostics.performancecounter/7.0.0", "hashPath": "system.diagnostics.performancecounter.7.0.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "path": "system.io.filesystem.accesscontrol/5.0.0", "hashPath": "system.io.filesystem.accesscontrol.5.0.0.nupkg.sha512"}, "System.IO.Pipelines/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-eA3cinogwaNB4jdjQHOP3Z3EuyiDII7MT35jgtnsA4vkn0LUrrSHsU0nzHTzFzmaFYeKV7MYyMxOocFzsBHpTw==", "path": "system.io.pipelines/9.0.0", "hashPath": "system.io.pipelines.9.0.0.nupkg.sha512"}, "System.Management/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-A4jed4QUviDOm7fJNKAJObEAEkEUXmkGL/w0iyCYTzrl1rezTj8LGFHfsVst4Vb9JwFcTpboiDrvdST48avBpw==", "path": "system.management/7.0.0", "hashPath": "system.management.7.0.0.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Metadata/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ANiqLu3DxW9kol/hMmTWbt3414t9ftdIuiIU7j80okq2YzAueo120M442xk1kDJWtmZTqWQn7wHDvMRipVOEOQ==", "path": "system.reflection.metadata/9.0.0", "hashPath": "system.reflection.metadata.9.0.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "path": "system.security.accesscontrol/5.0.0", "hashPath": "system.security.accesscontrol.5.0.0.nupkg.sha512"}, "System.Security.Cryptography.ProtectedData/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-<PERSON>J<PERSON>+x/F6fmRQ7N6K8paasTw9PDZp4t7G76UjGNlSDgoHPF0h08vTzLYbLZpOLEJSg35d5wy2jCXGo84EN05DpQ==", "path": "system.security.cryptography.protecteddata/9.0.0", "hashPath": "system.security.cryptography.protecteddata.9.0.0.nupkg.sha512"}, "System.Security.Principal.Windows/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA==", "path": "system.security.principal.windows/5.0.0", "hashPath": "system.security.principal.windows.5.0.0.nupkg.sha512"}, "System.Text.Encoding.CodePages/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LSyCblMpvOe0N3E+8e0skHcrIhgV2huaNcjUUEa8hRtgEAm36aGkRoC8Jxlb6Ra6GSfF29ftduPNywin8XolzQ==", "path": "system.text.encoding.codepages/7.0.0", "hashPath": "system.text.encoding.codepages.7.0.0.nupkg.sha512"}, "System.Text.Encodings.Web/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-e2hMgAErLbKyUUwt18qSBf9T5Y+SFAL3ZedM8fLupkVj8Rj2PZ9oxQ37XX2LF8fTO1wNIxvKpihD7Of7D/NxZw==", "path": "system.text.encodings.web/9.0.0", "hashPath": "system.text.encodings.web.9.0.0.nupkg.sha512"}, "System.Text.Json/9.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-js7+qAu/9mQvnhA4EfGMZNEzXtJCDxgkgj8ohuxq/Qxv+R56G+ljefhiJHOxTNiw54q8vmABCWUwkMulNdlZ4A==", "path": "system.text.json/9.0.0", "hashPath": "system.text.json.9.0.0.nupkg.sha512"}, "System.Threading.Channels/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-qmeeYNROMsONF6ndEZcIQ+VxR4Q/TX/7uIVLJqtwIWL7dDWeh0l1UIqgo4wYyjG//5lUNhwkLDSFl+pAWO6oiA==", "path": "system.threading.channels/7.0.0", "hashPath": "system.threading.channels.7.0.0.nupkg.sha512"}, "System.Threading.Tasks.Dataflow/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-BmSJ4b0e2nlplV/RdWVxvH7WECTHACofv06dx/JwOYc0n56eK1jIWdQKNYYsReSO4w8n1QA5stOzSQcfaVBkJg==", "path": "system.threading.tasks.dataflow/7.0.0", "hashPath": "system.threading.tasks.dataflow.7.0.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "Microsoft.AspNetCore.Razor.LanguageServer/10.0.0-preview.25322.1": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Utilities.Shared/10.0.0-preview.25322.1": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.CodeAnalysis.Razor.Compiler/10.0.0-preview.25322.1": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.CodeAnalysis.Razor.Workspaces/10.0.0-preview.25322.1": {"type": "project", "serviceable": false, "sha512": ""}}}