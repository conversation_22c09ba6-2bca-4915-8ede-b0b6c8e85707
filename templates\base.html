<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Python Web应用{% endblock %}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/socket.io/4.0.1/socket.io.js"></script>
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .main-content {
            padding: 20px;
        }
        .stat-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .chat-container {
            height: 400px;
            overflow-y: auto;
            border: 1px solid #ddd;
            padding: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
            background-color: white;
        }
        .message-username {
            font-weight: bold;
            color: #007bff;
        }
        .message-time {
            font-size: 0.8em;
            color: #666;
        }
        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            background-color: #f9f9f9;
        }
        .upload-area:hover {
            border-color: #007bff;
            background-color: #f0f8ff;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{{ url_for('index') }}">
                <i class="fas fa-rocket"></i> Python Web应用
            </a>

            <div class="navbar-nav ms-auto">
                {% if session.username %}
                    <span class="navbar-text me-3">
                        <i class="fas fa-user"></i> 欢迎, {{ session.username }}
                    </span>
                    <a class="nav-link" href="{{ url_for('logout') }}">
                        <i class="fas fa-sign-out-alt"></i> 登出
                    </a>
                {% else %}
                    <a class="nav-link" href="{{ url_for('login') }}">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </a>
                    <a class="nav-link" href="{{ url_for('register') }}">
                        <i class="fas fa-user-plus"></i> 注册
                    </a>
                {% endif %}
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            {% if session.username %}
            <!-- 侧边栏 -->
            <div class="col-md-2 sidebar">
                <div class="d-flex flex-column p-3">
                    <ul class="nav nav-pills flex-column mb-auto">
                        <li class="nav-item">
                            <a href="{{ url_for('dashboard') }}" class="nav-link">
                                <i class="fas fa-tachometer-alt"></i> 仪表板
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('upload_file') }}" class="nav-link">
                                <i class="fas fa-upload"></i> 文件上传
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="{{ url_for('chat') }}" class="nav-link">
                                <i class="fas fa-comments"></i> 聊天室
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="#" class="nav-link" onclick="loadSystemInfo()">
                                <i class="fas fa-server"></i> 系统信息
                            </a>
                        </li>
                        <li class="nav-item">
                            <a href="/api/stats" class="nav-link" target="_blank">
                                <i class="fas fa-chart-line"></i> API统计
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            {% endif %}

            <!-- 主内容区 -->
            <div class="col-md-{{ '10' if session.username else '12' }} main-content">
                <!-- Flash消息 -->
                {% with messages = get_flashed_messages() %}
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-info alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}
                {% endwith %}

                <!-- 页面内容 -->
                {% block content %}{% endblock %}
            </div>
        </div>
    </div>

    <!-- 系统信息模态框 -->
    <div class="modal fade" id="systemInfoModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">系统信息</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="systemInfoContent">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">加载中...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // 加载系统信息
        function loadSystemInfo() {
            const modal = new bootstrap.Modal(document.getElementById('systemInfoModal'));
            modal.show();

            fetch('/api/system-info')
                .then(response => response.json())
                .then(data => {
                    let html = '<div class="row">';

                    // 基本信息
                    html += '<div class="col-md-6">';
                    html += '<h6><i class="fas fa-desktop"></i> 基本信息</h6>';
                    html += `<p><strong>平台:</strong> ${data.platform}</p>`;
                    html += `<p><strong>架构:</strong> ${data.architecture}</p>`;
                    html += `<p><strong>主机名:</strong> ${data.hostname}</p>`;
                    html += '</div>';

                    // 性能信息
                    if (data.cpu_count) {
                        html += '<div class="col-md-6">';
                        html += '<h6><i class="fas fa-microchip"></i> 性能信息</h6>';
                        html += `<p><strong>CPU核心:</strong> ${data.cpu_count}</p>`;
                        html += `<p><strong>CPU使用率:</strong> ${data.cpu_percent}%</p>`;
                        if (data.memory) {
                            html += `<p><strong>内存使用率:</strong> ${data.memory.percent}%</p>`;
                        }
                        html += '</div>';
                    }

                    html += '</div>';

                    if (data.note) {
                        html += `<div class="alert alert-info">${data.note}</div>`;
                    }

                    document.getElementById('systemInfoContent').innerHTML = html;
                })
                .catch(error => {
                    document.getElementById('systemInfoContent').innerHTML =
                        '<div class="alert alert-danger">加载系统信息失败</div>';
                });
        }

        // 自动刷新时间戳
        function updateTimestamp() {
            const elements = document.querySelectorAll('.timestamp');
            elements.forEach(el => {
                const time = new Date();
                el.textContent = time.toLocaleString();
            });
        }

        setInterval(updateTimestamp, 1000);
    </script>

    {% block scripts %}{% endblock %}
</body>
</html>
