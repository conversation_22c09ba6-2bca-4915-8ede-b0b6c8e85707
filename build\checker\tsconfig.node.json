{
	"extends": "../../src/tsconfig.base.json",
	"compilerOptions": {
		"lib": [
			"ES2022"
		],
		"types": [
			"node"
		],
		"noEmit": true,
		"skipLibCheck": true
	},
	"include": [
		"../../src/*.ts",
		"../../src/**/common/**/*.ts",
		"../../src/**/node/**/*.ts",
		"../../src/typings/*.d.ts",
		"../../src/vs/monaco.d.ts",
		"../../src/vscode-dts/vscode.proposed.*.d.ts",
		"../../src/vscode-dts/vscode.d.ts",
		"../../node_modules/@types/trusted-types/index.d.ts",
	],
	"exclude": [
		"../../src/**/test/**",
		"../../src/**/fixtures/**"
	]
}
