{"$schema": "https://raw.githubusercontent.com/microsoft/vscode-github-triage-actions/stable/classifier-deep/apply/apply-labels/deep-classifier-config.schema.json", "vacation": [], "assignees": {"nameToOverrideAccuracyOf": {"accuracy": 0.8}}, "labels": {"accessibility": {"assign": ["meganrogge"]}, "api": {"assign": ["j<PERSON>ken"]}, "api-finalization": {"assign": []}, "api-proposal": {"assign": ["j<PERSON>ken"]}, "authentication": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "bisect-ext": {"assign": ["j<PERSON>ken"]}, "bot-proposal": {"assign": ["lramos15"]}, "bracket-pair-colorization": {"assign": ["hediet"]}, "bracket-pair-guides": {"assign": ["hediet"]}, "breadcrumbs": {"assign": ["j<PERSON>ken"]}, "callhierarchy": {"assign": ["j<PERSON>ken"]}, "chrome-devtools": {"assign": ["deepak1556"]}, "cloud-changes": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "code-cli": {"assign": ["connor4312"]}, "code-lens": {"assign": ["j<PERSON>ken"]}, "code-server-web": {"assign": ["a<PERSON><PERSON><PERSON>"]}, "command-center": {"assign": ["j<PERSON>ken"]}, "comments": {"assign": ["alexr00"]}, "config": {"assign": ["sandy081"]}, "containers": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "context-keys": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "continue-working-on": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "css-less-scss": {"assign": ["a<PERSON><PERSON><PERSON>"]}, "custom-editors": {"assign": ["mjbvz"]}, "debug": {"assign": ["rob<PERSON><PERSON>"]}, "debug-disassembly": {"assign": []}, "dialogs": {"assign": ["sbatten"]}, "diff-editor": {"assign": ["hediet"]}, "dropdown": {"assign": ["lramos15"]}, "editor-api": {"assign": ["alex<PERSON><PERSON>"]}, "editor-autoclosing": {"assign": ["alex<PERSON><PERSON>"]}, "editor-autoindent": {"assign": ["rebornix"]}, "editor-bracket-matching": {"assign": ["hediet"]}, "editor-clipboard": {"assign": ["alex<PERSON><PERSON>", "rebornix"]}, "editor-code-actions": {"assign": ["mjbvz", "justschen"]}, "editor-color-picker": {"assign": ["aiday-mar"]}, "editor-columnselect": {"assign": ["alex<PERSON><PERSON>"]}, "editor-commands": {"assign": ["alex<PERSON><PERSON>"]}, "editor-comments": {"assign": ["alex<PERSON><PERSON>"]}, "editor-contrib": {"assign": ["alex<PERSON><PERSON>"]}, "editor-core": {"assign": ["alex<PERSON><PERSON>"]}, "editor-drag-and-drop": {"assign": ["rebornix"]}, "editor-error-widget": {"assign": ["sandy081"]}, "editor-find": {"assign": ["rebornix"]}, "editor-folding": {"assign": ["a<PERSON><PERSON><PERSON>"]}, "editor-highlight": {"assign": ["alex<PERSON><PERSON>"]}, "editor-hover": {"assign": ["aiday-mar"]}, "editor-indent-detection": {"assign": ["alex<PERSON><PERSON>"]}, "editor-indent-guides": {"assign": ["hediet"]}, "editor-input": {"assign": ["aiday-mar"]}, "editor-input-IME": {"assign": ["aiday-mar"]}, "editor-insets": {"assign": ["j<PERSON>ken"]}, "editor-minimap": {"assign": ["alex<PERSON><PERSON>"]}, "editor-multicursor": {"assign": ["alex<PERSON><PERSON>"]}, "editor-parameter-hints": {"assign": ["mjbvz"]}, "editor-render-whitespace": {"assign": ["alex<PERSON><PERSON>"]}, "editor-rendering": {"assign": ["alex<PERSON><PERSON>"]}, "editor-RTL": {"assign": ["alex<PERSON><PERSON>"]}, "editor-scrollbar": {"assign": ["alex<PERSON><PERSON>"]}, "editor-sorting": {"assign": ["alex<PERSON><PERSON>"]}, "editor-sticky-scroll": {"assign": ["aiday-mar"]}, "editor-symbols": {"assign": ["j<PERSON>ken"]}, "editor-synced-region": {"assign": ["a<PERSON><PERSON><PERSON>"]}, "editor-textbuffer": {"assign": ["alex<PERSON><PERSON>", "rebornix"]}, "editor-theming": {"assign": ["alex<PERSON><PERSON>"]}, "editor-wordnav": {"assign": ["alex<PERSON><PERSON>"]}, "editor-wrapping": {"assign": ["alex<PERSON><PERSON>"]}, "emmet": {"assign": ["rzhao271"]}, "emmet-parse": {"assign": ["rzhao271"]}, "error-list": {"assign": ["sandy081"]}, "extension-activation": {"assign": ["alex<PERSON><PERSON>"]}, "extension-host": {"assign": ["alex<PERSON><PERSON>"]}, "extension-prerelease": {"assign": ["sandy081"]}, "extension-recommendations": {"assign": ["sandy081"]}, "extensions": {"assign": ["sandy081"]}, "extensions-development": {"assign": []}, "file-decorations": {"assign": ["j<PERSON>ken"]}, "file-encoding": {"assign": ["bpasero"]}, "file-explorer": {"assign": ["lramos15"]}, "file-glob": {"assign": ["bpasero"]}, "file-io": {"assign": ["bpasero"]}, "file-nesting": {"assign": ["lramos15"]}, "file-watcher": {"assign": ["bpasero"]}, "font-rendering": {"assign": ["rzhao271"]}, "formatting": {"assign": ["j<PERSON>ken"]}, "getting-started": {"assign": ["bhavy<PERSON>"]}, "ghost-text": {"assign": ["hediet"]}, "git": {"assign": ["lszomoru"]}, "github": {"assign": ["lszomoru"]}, "github-authentication": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "github-repositories": {"assign": ["lszomoru"]}, "gpu": {"assign": ["deepak1556"]}, "grammar": {"assign": ["mjbvz"]}, "grid-widget": {"assign": ["joa<PERSON>reno"]}, "html": {"assign": ["a<PERSON><PERSON><PERSON>"]}, "icon-brand": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "icons-product": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "image-preview": {"assign": ["mjbvz"]}, "inlay-hints": {"assign": ["j<PERSON>ken", "hediet"]}, "inline-completions": {"assign": ["hediet"]}, "install-update": {"assign": ["joa<PERSON>reno"], "accuracy": 0.85}, "intellisense-config": {"assign": ["rzhao271"]}, "interactive-playground": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "interactive-window": {"assign": ["amunger", "rebornix"]}, "ipc": {"assign": ["joa<PERSON>reno"]}, "issue-bot": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "issue-reporter": {"assign": ["justschen"]}, "javascript": {"assign": ["mjbvz"]}, "json": {"assign": ["a<PERSON><PERSON><PERSON>"]}, "json-sorting": {"assign": ["aiday-mar"]}, "keybindings": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "keybindings-editor": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "keyboard-layout": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "L10N": {"assign": ["<PERSON><PERSON><PERSON><PERSON>", "csigs"]}, "l10n-platform": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "label-provider": {"assign": ["lramos15"]}, "languages-basic": {"assign": ["a<PERSON><PERSON><PERSON>"]}, "languages-diagnostics": {"assign": ["j<PERSON>ken"]}, "languages-guessing": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "layout": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "lcd-text-rendering": {"assign": []}, "list-widget": {"assign": ["joa<PERSON>reno"]}, "live-preview": {"assign": []}, "log": {"assign": ["sandy081"]}, "markdown": {"assign": ["mjbvz"]}, "marketplace": {"assign": ["<PERSON><PERSON><PERSON>"]}, "menus": {"assign": ["sbatten"]}, "merge-conflict": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "merge-editor": {"assign": ["hediet"]}, "merge-editor-workbench": {"assign": ["j<PERSON>ken"]}, "monaco-editor": {"assign": []}, "native-file-dialog": {"assign": ["deepak1556"]}, "network": {"assign": ["deepak1556"]}, "notebook": {"assign": ["rebornix"]}, "notebook-api": {"assign": []}, "notebook-builtin-renderers": {"assign": []}, "notebook-cell-editor": {"assign": []}, "notebook-celltoolbar": {"assign": []}, "notebook-clipboard": {"assign": []}, "notebook-commenting": {"assign": []}, "notebook-debugging": {"assign": []}, "notebook-diff": {"assign": []}, "notebook-dnd": {"assign": []}, "notebook-execution": {"assign": []}, "notebook-find": {"assign": []}, "notebook-folding": {"assign": []}, "notebook-getting-started": {"assign": []}, "notebook-getting-globaltoolbar": {"assign": []}, "notebook-ipynb": {"assign": []}, "notebook-kernel": {"assign": []}, "notebook-kernel-picker": {"assign": []}, "notebook-keybinding": {"assign": []}, "notebook-language": {"assign": []}, "notebook-layout": {"assign": []}, "notebook-markdown": {"assign": []}, "notebook-math": {"assign": []}, "notebook-minimap": {"assign": []}, "notebook-multiselect": {"assign": []}, "notebook-output": {"assign": []}, "notebook-perf": {"assign": []}, "notebook-remote": {"assign": []}, "notebook-rendering": {"assign": []}, "notebook-serialization": {"assign": []}, "notebook-serverless-web": {"assign": []}, "notebook-statusbar": {"assign": []}, "notebook-toc-outline": {"assign": []}, "notebook-undo-redo": {"assign": []}, "notebook-variables": {"assign": []}, "notebook-workbench-integration": {"assign": []}, "notebook-workflow": {"assign": []}, "open-editors": {"assign": ["lramos15"]}, "opener": {"assign": ["mjbvz"]}, "outline": {"assign": ["j<PERSON>ken"]}, "output": {"assign": ["sandy081"]}, "perf": {"assign": []}, "perf-bloat": {"assign": []}, "perf-startup": {"assign": []}, "php": {"assign": ["rob<PERSON><PERSON>"]}, "portable-mode": {"assign": ["joa<PERSON>reno"]}, "proxy": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "quick-open": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "quick-pick": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "references-viewlet": {"assign": ["j<PERSON>ken"]}, "release-notes": {"assign": []}, "remote": {"assign": []}, "remote-connection": {"assign": ["alex<PERSON><PERSON>"]}, "remote-explorer": {"assign": ["alexr00"]}, "remote-tunnel": {"assign": ["a<PERSON><PERSON><PERSON>", "connor4312"]}, "rename": {"assign": ["j<PERSON>ken"]}, "runCommands": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "sandbox": {"assign": ["deepak1556"]}, "sash-widget": {"assign": ["joa<PERSON>reno"]}, "scm": {"assign": ["lszomoru"]}, "screencast-mode": {"assign": ["joa<PERSON>reno"]}, "search": {"assign": ["osortega"]}, "search-api": {"assign": ["osortega"]}, "search-editor": {"assign": ["osortega"]}, "search-replace": {"assign": ["sandy081"]}, "semantic-tokens": {"assign": ["alex<PERSON><PERSON>", "a<PERSON><PERSON><PERSON>"]}, "server": {"assign": ["alex<PERSON><PERSON>"]}, "settings-editor": {"assign": ["rzhao271"]}, "settings-search": {"assign": ["rzhao271"]}, "settings-sync": {"assign": ["sandy081"]}, "settings-sync-server": {"assign": ["<PERSON><PERSON><PERSON>", "lszomoru"]}, "shared-process": {"assign": []}, "simple-file-dialog": {"assign": ["alexr00"]}, "smart-select": {"assign": ["j<PERSON>ken"]}, "snap": {"assign": ["deepak1556"]}, "snippets": {"assign": ["j<PERSON>ken"]}, "splitview-widget": {"assign": ["joa<PERSON>reno"]}, "suggest": {"assign": ["j<PERSON>ken"]}, "table-widget": {"assign": ["joa<PERSON>reno"]}, "tasks": {"assign": ["meganrogge"], "accuracy": 0.85}, "telemetry": {"assign": ["lramos15"]}, "terminal": {"assign": ["meganrogge"]}, "terminal-conpty": {"assign": ["meganrogge"]}, "terminal-editors": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-external": {"assign": ["meganrogge"]}, "terminal-find": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-input": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-layout": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-links": {"assign": ["<PERSON><PERSON><PERSON>"]}, "terminal-local-echo": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-persistence": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-process": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-profiles": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-quick-fix": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-rendering": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-search": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-shell-bash": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-shell-cmd": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-shell-fish": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-shell-git-bash": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-shell-integration": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-shell-pwsh": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-shell-zsh": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "terminal-tabs": {"assign": ["meganrogge"]}, "terminal-winpty": {"assign": ["<PERSON><PERSON><PERSON>", "meganrogge"]}, "testing": {"assign": ["connor4312"]}, "themes": {"assign": ["a<PERSON><PERSON><PERSON>"]}, "timeline": {"assign": ["lramos15"]}, "timeline-git": {"assign": ["lszomoru"]}, "timeline-local-history": {"assign": ["bpasero"]}, "titlebar": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "tokenization": {"assign": ["alex<PERSON><PERSON>"]}, "touch/pointer": {"assign": []}, "trackpad/scroll": {"assign": []}, "tree-sticky-scroll": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "tree-views": {"assign": ["alexr00"]}, "tree-widget": {"assign": ["joa<PERSON>reno"]}, "typehierarchy": {"assign": ["j<PERSON>ken"]}, "typescript": {"assign": ["mjbvz"]}, "undo-redo": {"assign": ["alex<PERSON><PERSON>"]}, "unicode-highlight": {"assign": ["hediet"]}, "uri": {"assign": ["j<PERSON>ken"]}, "user-profiles": {"assign": ["sandy081"]}, "ux": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "variable-resolving": {"assign": ["alexr00"]}, "VIM": {"assign": ["rebornix"]}, "virtual-workspaces": {"assign": []}, "vscode.dev": {"assign": []}, "vscode-build": {"assign": []}, "vscode-website": {"assign": ["rzhao271"]}, "web": {"assign": []}, "webview": {"assign": ["mjbvz"]}, "webview-views": {"assign": ["mjbvz"]}, "workbench-actions": {"assign": ["bpasero"]}, "workbench-auxwindow": {"assign": ["bpasero"]}, "workbench-banner": {"assign": ["lszomoru", "sbatten"]}, "workbench-cli": {"assign": ["bpasero"]}, "workbench-diagnostics": {"assign": ["<PERSON><PERSON><PERSON>"]}, "workbench-dnd": {"assign": ["bpasero"]}, "workbench-editor-grid": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "workbench-editor-groups": {"assign": ["bpasero"]}, "workbench-editor-resolver": {"assign": ["lramos15"]}, "workbench-editors": {"assign": ["bpasero"]}, "workbench-electron": {"assign": ["deepak1556"]}, "workbench-fonts": {"assign": []}, "workbench-history": {"assign": ["bpasero"]}, "workbench-hot-exit": {"assign": ["bpasero"]}, "workbench-hover": {"assign": ["<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>"]}, "workbench-launch": {"assign": []}, "workbench-link": {"assign": []}, "workbench-multiroot": {"assign": ["bpasero"]}, "workbench-notifications": {"assign": ["bpasero"]}, "workbench-os-integration": {"assign": ["bpasero"]}, "workbench-rapid-render": {"assign": ["j<PERSON>ken"]}, "workbench-run-as-admin": {"assign": ["bpasero"]}, "workbench-state": {"assign": ["bpasero"]}, "workbench-status": {"assign": ["bpasero"]}, "workbench-tabs": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "workbench-touchbar": {"assign": ["bpasero"]}, "workbench-untitled-editors": {"assign": ["bpasero"]}, "workbench-views": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "workbench-welcome": {"assign": ["lramos15"]}, "workbench-window": {"assign": ["bpasero"]}, "workbench-workspace": {"assign": []}, "workbench-zen": {"assign": ["<PERSON><PERSON><PERSON><PERSON>"]}, "workspace-edit": {"assign": ["j<PERSON>ken"]}, "workspace-symbols": {"assign": []}, "workspace-trust": {"assign": ["lszomoru", "sbatten"]}, "zoom": {"assign": ["alex<PERSON><PERSON>"]}}}