{"version": 3, "sources": ["../node_modules/preact/src/constants.js", "../node_modules/preact/src/options.js", "../node_modules/preact/src/create-element.js", "../node_modules/preact/src/component.js", "../node_modules/preact/src/render.js", "../node_modules/preact/src/create-context.js", "../node_modules/preact/src/util.js", "../node_modules/preact/src/diff/children.js", "../node_modules/preact/src/diff/props.js", "../node_modules/preact/src/diff/index.js", "../node_modules/preact/src/clone-element.js", "../node_modules/preact/src/diff/catch-error.js", "../node_modules/preact/hooks/src/index.js", "../src/common/emoji.ts", "../src/renderer/icons.tsx", "../src/renderer/renderer.tsx", "../src/renderer/renderer.css", "../src/renderer/index.tsx"], "sourcesContent": ["export const EMPTY_OBJ = {};\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL = /acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { _catchError } from './diff/catch-error';\n\n/**\n * The `option` object can potentially contain callback functions\n * that are called during various stages of our renderer. This is the\n * foundation on which all our addons like `preact/debug`, `preact/compat`,\n * and `preact/hooks` are based on. See the `Options` type in `internal.d.ts`\n * for a full list of available option hooks (most editors/IDEs allow you to\n * ctrl+click or cmd+click on mac the type definition below).\n * @type {import('./internal').Options}\n */\nconst options = {\n\t_catchError\n};\n\nexport default options;\n", "import options from './options';\n\n/**\n * Create an virtual node (used for JSX)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * constructor for this virtual node\n * @param {object | null | undefined} [props] The properties of the virtual node\n * @param {Array<import('.').ComponentChildren>} [children] The children of the virtual node\n * @returns {import('./internal').VNode}\n */\nexport function createElement(type, props, children) {\n\tlet normalizedProps = {},\n\t\ti;\n\tfor (i in props) {\n\t\tif (i !== 'key' && i !== 'ref') normalizedProps[i] = props[i];\n\t}\n\n\tif (arguments.length > 3) {\n\t\tchildren = [children];\n\t\t// https://github.com/preactjs/preact/issues/1916\n\t\tfor (i = 3; i < arguments.length; i++) {\n\t\t\tchildren.push(arguments[i]);\n\t\t}\n\t}\n\tif (children != null) {\n\t\tnormalizedProps.children = children;\n\t}\n\n\t// If a Component VNode, check for and apply defaultProps\n\t// Note: type may be undefined in development, must never error here.\n\tif (typeof type == 'function' && type.defaultProps != null) {\n\t\tfor (i in type.defaultProps) {\n\t\t\tif (normalizedProps[i] === undefined) {\n\t\t\t\tnormalizedProps[i] = type.defaultProps[i];\n\t\t\t}\n\t\t}\n\t}\n\n\treturn createVNode(\n\t\ttype,\n\t\tnormalizedProps,\n\t\tprops && props.key,\n\t\tprops && props.ref,\n\t\tnull\n\t);\n}\n\n/**\n * Create a VNode (used internally by Preact)\n * @param {import('./internal').VNode[\"type\"]} type The node name or Component\n * Constructor for this virtual node\n * @param {object | string | number | null} props The properties of this virtual node.\n * If this virtual node represents a text node, this is the text of the node (string or number).\n * @param {string | number | null} key The key for this virtual node, used when\n * diffing it against its children\n * @param {import('./internal').VNode[\"ref\"]} ref The ref property that will\n * receive a reference to its created child\n * @returns {import('./internal').VNode}\n */\nexport function createVNode(type, props, key, ref, original) {\n\t// V8 seems to be better at detecting type shapes if the object is allocated from the same call site\n\t// Do not inline into createElement and coerceToVNode!\n\tconst vnode = {\n\t\ttype,\n\t\tprops,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t// _nextDom must be initialized to undefined b/c it will eventually\n\t\t// be set to dom.nextSibling which can return `null` and it is important\n\t\t// to be able to distinguish between an uninitialized _nextDom and\n\t\t// a _nextDom that has been set to `null`\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\tconstructor: undefined,\n\t\t_original: original\n\t};\n\n\tif (original == null) vnode._original = vnode;\n\tif (options.vnode) options.vnode(vnode);\n\n\treturn vnode;\n}\n\nexport function createRef() {\n\treturn { current: null };\n}\n\nexport function Fragment(props) {\n\treturn props.children;\n}\n\n/**\n * Check if a the argument is a valid Preact VNode.\n * @param {*} vnode\n * @returns {vnode is import('./internal').VNode}\n */\nexport const isValidElement = vnode =>\n\tvnode != null && vnode.constructor === undefined;\n", "import { assign } from './util';\nimport { diff, commitRoot } from './diff/index';\nimport options from './options';\nimport { Fragment } from './create-element';\n\n/**\n * Base Component class. Provides `setState()` and `forceUpdate()`, which\n * trigger rendering\n * @param {object} props The initial component props\n * @param {object} context The initial context from parent components'\n * getChildContext\n */\nexport function Component(props, context) {\n\tthis.props = props;\n\tthis.context = context;\n}\n\n/**\n * Update component state and schedule a re-render.\n * @param {object | ((s: object, p: object) => object)} update A hash of state\n * properties to update with new values or a function that given the current\n * state and props returns a new partial state\n * @param {() => void} [callback] A function to be called once component state is\n * updated\n */\nComponent.prototype.setState = function(update, callback) {\n\t// only clone state when copying to nextState the first time.\n\tlet s;\n\tif (this._nextState !== this.state) {\n\t\ts = this._nextState;\n\t} else {\n\t\ts = this._nextState = assign({}, this.state);\n\t}\n\n\tif (typeof update == 'function') {\n\t\tupdate = update(s, this.props);\n\t}\n\n\tif (update) {\n\t\tassign(s, update);\n\t}\n\n\t// Skip update if updater function returned null\n\tif (update == null) return;\n\n\tif (this._vnode) {\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Immediately perform a synchronous re-render of the component\n * @param {() => void} [callback] A function to be called after component is\n * re-rendered\n */\nComponent.prototype.forceUpdate = function(callback) {\n\tif (this._vnode) {\n\t\t// Set render mode so that we can differentiate where the render request\n\t\t// is coming from. We need this because forceUpdate should never call\n\t\t// shouldComponentUpdate\n\t\tthis._force = true;\n\t\tif (callback) this._renderCallbacks.push(callback);\n\t\tenqueueRender(this);\n\t}\n};\n\n/**\n * Accepts `props` and `state`, and returns a new Virtual DOM tree to build.\n * Virtual DOM is generally constructed via [JSX](http://jasonformat.com/wtf-is-jsx).\n * @param {object} props Props (eg: JSX attributes) received from parent\n * element/component\n * @param {object} state The component's current state\n * @param {object} context Context object, as returned by the nearest\n * ancestor's `getChildContext()`\n * @returns {import('./index').ComponentChildren | void}\n */\nComponent.prototype.render = Fragment;\n\n/**\n * @param {import('./internal').VNode} vnode\n * @param {number | null} [childIndex]\n */\nexport function getDomSibling(vnode, childIndex) {\n\tif (childIndex == null) {\n\t\t// Use childIndex==null as a signal to resume the search from the vnode's sibling\n\t\treturn vnode._parent\n\t\t\t? getDomSibling(vnode._parent, vnode._parent._children.indexOf(vnode) + 1)\n\t\t\t: null;\n\t}\n\n\tlet sibling;\n\tfor (; childIndex < vnode._children.length; childIndex++) {\n\t\tsibling = vnode._children[childIndex];\n\n\t\tif (sibling != null && sibling._dom != null) {\n\t\t\t// Since updateParentDomPointers keeps _dom pointer correct,\n\t\t\t// we can rely on _dom to tell us if this subtree contains a\n\t\t\t// rendered DOM node, and what the first rendered DOM node is\n\t\t\treturn sibling._dom;\n\t\t}\n\t}\n\n\t// If we get here, we have not found a DOM node in this vnode's children.\n\t// We must resume from this vnode's sibling (in it's parent _children array)\n\t// Only climb up and search the parent if we aren't searching through a DOM\n\t// VNode (meaning we reached the DOM parent of the original vnode that began\n\t// the search)\n\treturn typeof vnode.type == 'function' ? getDomSibling(vnode) : null;\n}\n\n/**\n * Trigger in-place re-rendering of a component.\n * @param {import('./internal').Component} component The component to rerender\n */\nfunction renderComponent(component) {\n\tlet vnode = component._vnode,\n\t\toldDom = vnode._dom,\n\t\tparentDom = component._parentDom;\n\n\tif (parentDom) {\n\t\tlet commitQueue = [];\n\t\tconst oldVNode = assign({}, vnode);\n\t\toldVNode._original = oldVNode;\n\n\t\tlet newDom = diff(\n\t\t\tparentDom,\n\t\t\tvnode,\n\t\t\toldVNode,\n\t\t\tcomponent._globalContext,\n\t\t\tparentDom.ownerSVGElement !== undefined,\n\t\t\tnull,\n\t\t\tcommitQueue,\n\t\t\toldDom == null ? getDomSibling(vnode) : oldDom\n\t\t);\n\t\tcommitRoot(commitQueue, vnode);\n\n\t\tif (newDom != oldDom) {\n\t\t\tupdateParentDomPointers(vnode);\n\t\t}\n\t}\n}\n\n/**\n * @param {import('./internal').VNode} vnode\n */\nfunction updateParentDomPointers(vnode) {\n\tif ((vnode = vnode._parent) != null && vnode._component != null) {\n\t\tvnode._dom = vnode._component.base = null;\n\t\tfor (let i = 0; i < vnode._children.length; i++) {\n\t\t\tlet child = vnode._children[i];\n\t\t\tif (child != null && child._dom != null) {\n\t\t\t\tvnode._dom = vnode._component.base = child._dom;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\n\t\treturn updateParentDomPointers(vnode);\n\t}\n}\n\n/**\n * The render queue\n * @type {Array<import('./internal').Component>}\n */\nlet rerenderQueue = [];\n\n/**\n * Asynchronously schedule a callback\n * @type {(cb: () => void) => void}\n */\n/* istanbul ignore next */\n// Note the following line isn't tree-shaken by rollup cuz of rollup/rollup#2566\nconst defer =\n\ttypeof Promise == 'function'\n\t\t? Promise.prototype.then.bind(Promise.resolve())\n\t\t: setTimeout;\n\n/*\n * The value of `Component.debounce` must asynchronously invoke the passed in callback. It is\n * important that contributors to Preact can consistently reason about what calls to `setState`, etc.\n * do, and when their effects will be applied. See the links below for some further reading on designing\n * asynchronous APIs.\n * * [Designing APIs for Asynchrony](https://blog.izs.me/2013/08/designing-apis-for-asynchrony)\n * * [Callbacks synchronous and asynchronous](https://blog.ometer.com/2011/07/24/callbacks-synchronous-and-asynchronous/)\n */\n\nlet prevDebounce;\n\n/**\n * Enqueue a rerender of a component\n * @param {import('./internal').Component} c The component to rerender\n */\nexport function enqueueRender(c) {\n\tif (\n\t\t(!c._dirty &&\n\t\t\t(c._dirty = true) &&\n\t\t\trerenderQueue.push(c) &&\n\t\t\t!process._rerenderCount++) ||\n\t\tprevDebounce !== options.debounceRendering\n\t) {\n\t\tprevDebounce = options.debounceRendering;\n\t\t(prevDebounce || defer)(process);\n\t}\n}\n\n/** Flush the render queue by rerendering all queued components */\nfunction process() {\n\tlet queue;\n\twhile ((process._rerenderCount = rerenderQueue.length)) {\n\t\tqueue = rerenderQueue.sort((a, b) => a._vnode._depth - b._vnode._depth);\n\t\trerenderQueue = [];\n\t\t// Don't update `renderCount` yet. Keep its value non-zero to prevent unnecessary\n\t\t// process() calls from getting scheduled while `queue` is still being consumed.\n\t\tqueue.some(c => {\n\t\t\tif (c._dirty) renderComponent(c);\n\t\t});\n\t}\n}\nprocess._rerenderCount = 0;\n", "import { EMPTY_OBJ, EMPTY_ARR } from './constants';\nimport { commitRoot, diff } from './diff/index';\nimport { createElement, Fragment } from './create-element';\nimport options from './options';\n\nconst IS_HYDRATE = EMPTY_OBJ;\n\n/**\n * Render a Preact virtual node into a DOM element\n * @param {import('./index').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to\n * render into\n * @param {Element | Text} [replaceNode] Optional: Attempt to re-use an\n * existing DOM tree rooted at `replaceNode`\n */\nexport function render(vnode, parentDom, replaceNode) {\n\tif (options._root) options._root(vnode, parentDom);\n\n\t// We abuse the `replaceNode` parameter in `hydrate()` to signal if we\n\t// are in hydration mode or not by passing `IS_HYDRATE` instead of a\n\t// DOM element.\n\tlet isHydrating = replaceNode === IS_HYDRATE;\n\n\t// To be able to support calling `render()` multiple times on the same\n\t// DOM node, we need to obtain a reference to the previous tree. We do\n\t// this by assigning a new `_children` property to DOM nodes which points\n\t// to the last rendered tree. By default this property is not present, which\n\t// means that we are mounting a new tree for the first time.\n\tlet oldVNode = isHydrating\n\t\t? null\n\t\t: (replaceNode && replaceNode._children) || parentDom._children;\n\tvnode = createElement(Fragment, null, [vnode]);\n\n\t// List of effects that need to be called after diffing.\n\tlet commitQueue = [];\n\tdiff(\n\t\tparentDom,\n\t\t// Determine the new vnode tree and store it on the DOM element on\n\t\t// our custom `_children` property.\n\t\t((isHydrating ? parentDom : replaceNode || parentDom)._children = vnode),\n\t\toldVNode || EMPTY_OBJ,\n\t\tEMPTY_OBJ,\n\t\tparentDom.ownerSVGElement !== undefined,\n\t\treplaceNode && !isHydrating\n\t\t\t? [replaceNode]\n\t\t\t: oldVNode\n\t\t\t? null\n\t\t\t: parentDom.childNodes.length\n\t\t\t? EMPTY_ARR.slice.call(parentDom.childNodes)\n\t\t\t: null,\n\t\tcommitQueue,\n\t\treplaceNode || EMPTY_OBJ,\n\t\tisHydrating\n\t);\n\n\t// Flush all queued effects\n\tcommitRoot(commitQueue, vnode);\n}\n\n/**\n * Update an existing DOM element with data from a Preact virtual node\n * @param {import('./index').ComponentChild} vnode The virtual node to render\n * @param {import('./internal').PreactElement} parentDom The DOM element to\n * update\n */\nexport function hydrate(vnode, parentDom) {\n\trender(vnode, parentDom, IS_HYDRATE);\n}\n", "import { enqueueRender } from './component';\n\nexport let i = 0;\n\nexport function createContext(defaultValue) {\n\tconst ctx = {};\n\n\tconst context = {\n\t\t_id: '__cC' + i++,\n\t\t_defaultValue: defaultValue,\n\t\tConsumer(props, context) {\n\t\t\treturn props.children(context);\n\t\t},\n\t\tProvider(props) {\n\t\t\tif (!this.getChildContext) {\n\t\t\t\tconst subs = [];\n\t\t\t\tthis.getChildContext = () => {\n\t\t\t\t\tctx[context._id] = this;\n\t\t\t\t\treturn ctx;\n\t\t\t\t};\n\n\t\t\t\tthis.shouldComponentUpdate = _props => {\n\t\t\t\t\tif (this.props.value !== _props.value) {\n\t\t\t\t\t\tsubs.some(c => {\n\t\t\t\t\t\t\tc.context = _props.value;\n\t\t\t\t\t\t\tenqueueRender(c);\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t};\n\n\t\t\t\tthis.sub = c => {\n\t\t\t\t\tsubs.push(c);\n\t\t\t\t\tlet old = c.componentWillUnmount;\n\t\t\t\t\tc.componentWillUnmount = () => {\n\t\t\t\t\t\tsubs.splice(subs.indexOf(c), 1);\n\t\t\t\t\t\told && old.call(c);\n\t\t\t\t\t};\n\t\t\t\t};\n\t\t\t}\n\n\t\t\treturn props.children;\n\t\t}\n\t};\n\n\tcontext.Consumer.contextType = context;\n\n\t// Devtools needs access to the context object when it\n\t// encounters a Provider. This is necessary to support\n\t// setting `displayName` on the context object instead\n\t// of on the component itself. See:\n\t// https://reactjs.org/docs/context.html#contextdisplayname\n\tcontext.Provider._contextRef = context;\n\n\treturn context;\n}\n", "/**\n * Assign properties from `props` to `obj`\n * @template O, P The obj and props types\n * @param {O} obj The object to copy properties to\n * @param {P} props The object to copy properties from\n * @returns {O & P}\n */\nexport function assign(obj, props) {\n\tfor (let i in props) obj[i] = props[i];\n\treturn /** @type {O & P} */ (obj);\n}\n\n/**\n * Remove a child node from its parent if attached. This is a workaround for\n * IE11 which doesn't support `Element.prototype.remove()`. Using this function\n * is smaller than including a dedicated polyfill.\n * @param {Node} node The node to remove\n */\nexport function removeNode(node) {\n\tlet parentNode = node.parentNode;\n\tif (parentNode) parentNode.removeChild(node);\n}\n", "import { diff, unmount, applyRef } from './index';\nimport { createVNode, Fragment } from '../create-element';\nimport { EMPTY_OBJ, EMPTY_ARR } from '../constants';\nimport { removeNode } from '../util';\nimport { getDomSibling } from '../component';\n\n/**\n * Diff the children of a virtual node\n * @param {import('../internal').PreactElement} parentDom The DOM element whose\n * children are being diffed\n * @param {import('../index').ComponentChildren[]} renderResult\n * @param {import('../internal').VNode} newParentVNode The new virtual\n * node whose children should be diff'ed against oldParentVNode\n * @param {import('../internal').VNode} oldParentVNode The old virtual\n * node whose children should be diff'ed against newParentVNode\n * @param {object} globalContext The current context object - modified by getChildContext\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {Node | Text} oldDom The current attached DOM\n * element any new dom elements should be placed around. Likely `null` on first\n * render (except when hydrating). Can be a sibling DOM element when diffing\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} isHydrating Whether or not we are in hydration\n */\nexport function diffChildren(\n\tparentDom,\n\trenderResult,\n\tnewParentVNode,\n\toldParentVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating\n) {\n\tlet i, j, oldVNode, childVNode, newDom, firstChildDom, refs;\n\n\t// This is a compression of oldParentVNode!=null && oldParentVNode != EMPTY_OBJ && oldParentVNode._children || EMPTY_ARR\n\t// as EMPTY_OBJ._children should be `undefined`.\n\tlet oldChildren = (oldParentVNode && oldParentVNode._children) || EMPTY_ARR;\n\n\tlet oldChildrenLength = oldChildren.length;\n\n\t// Only in very specific places should this logic be invoked (top level `render` and `diffElementNodes`).\n\t// I'm using `EMPTY_OBJ` to signal when `diffChildren` is invoked in these situations. I can't use `null`\n\t// for this purpose, because `null` is a valid value for `oldDom` which can mean to skip to this logic\n\t// (e.g. if mounting a new tree in which the old DOM should be ignored (usually for Fragments).\n\tif (oldDom == EMPTY_OBJ) {\n\t\tif (excessDomChildren != null) {\n\t\t\toldDom = excessDomChildren[0];\n\t\t} else if (oldChildrenLength) {\n\t\t\toldDom = getDomSibling(oldParentVNode, 0);\n\t\t} else {\n\t\t\toldDom = null;\n\t\t}\n\t}\n\n\tnewParentVNode._children = [];\n\tfor (i = 0; i < renderResult.length; i++) {\n\t\tchildVNode = renderResult[i];\n\n\t\tif (childVNode == null || typeof childVNode == 'boolean') {\n\t\t\tchildVNode = newParentVNode._children[i] = null;\n\t\t}\n\t\t// If this newVNode is being reused (e.g. <div>{reuse}{reuse}</div>) in the same diff,\n\t\t// or we are rendering a component (e.g. setState) copy the oldVNodes so it can have\n\t\t// it's own DOM & etc. pointers\n\t\telse if (typeof childVNode == 'string' || typeof childVNode == 'number') {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tnull,\n\t\t\t\tchildVNode,\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tchildVNode\n\t\t\t);\n\t\t} else if (Array.isArray(childVNode)) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tFragment,\n\t\t\t\t{ children: childVNode },\n\t\t\t\tnull,\n\t\t\t\tnull,\n\t\t\t\tnull\n\t\t\t);\n\t\t} else if (childVNode._dom != null || childVNode._component != null) {\n\t\t\tchildVNode = newParentVNode._children[i] = createVNode(\n\t\t\t\tchildVNode.type,\n\t\t\t\tchildVNode.props,\n\t\t\t\tchildVNode.key,\n\t\t\t\tnull,\n\t\t\t\tchildVNode._original\n\t\t\t);\n\t\t} else {\n\t\t\tchildVNode = newParentVNode._children[i] = childVNode;\n\t\t}\n\n\t\t// Terser removes the `continue` here and wraps the loop body\n\t\t// in a `if (childVNode) { ... } condition\n\t\tif (childVNode == null) {\n\t\t\tcontinue;\n\t\t}\n\n\t\tchildVNode._parent = newParentVNode;\n\t\tchildVNode._depth = newParentVNode._depth + 1;\n\n\t\t// Check if we find a corresponding element in oldChildren.\n\t\t// If found, delete the array item by setting to `undefined`.\n\t\t// We use `undefined`, as `null` is reserved for empty placeholders\n\t\t// (holes).\n\t\toldVNode = oldChildren[i];\n\n\t\tif (\n\t\t\toldVNode === null ||\n\t\t\t(oldVNode &&\n\t\t\t\tchildVNode.key == oldVNode.key &&\n\t\t\t\tchildVNode.type === oldVNode.type)\n\t\t) {\n\t\t\toldChildren[i] = undefined;\n\t\t} else {\n\t\t\t// Either oldVNode === undefined or oldChildrenLength > 0,\n\t\t\t// so after this loop oldVNode == null or oldVNode is a valid value.\n\t\t\tfor (j = 0; j < oldChildrenLength; j++) {\n\t\t\t\toldVNode = oldChildren[j];\n\t\t\t\t// If childVNode is unkeyed, we only match similarly unkeyed nodes, otherwise we match by key.\n\t\t\t\t// We always match by type (in either case).\n\t\t\t\tif (\n\t\t\t\t\toldVNode &&\n\t\t\t\t\tchildVNode.key == oldVNode.key &&\n\t\t\t\t\tchildVNode.type === oldVNode.type\n\t\t\t\t) {\n\t\t\t\t\toldChildren[j] = undefined;\n\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\toldVNode = null;\n\t\t\t}\n\t\t}\n\n\t\toldVNode = oldVNode || EMPTY_OBJ;\n\n\t\t// Morph the old element into the new one, but don't append it to the dom yet\n\t\tnewDom = diff(\n\t\t\tparentDom,\n\t\t\tchildVNode,\n\t\t\toldVNode,\n\t\t\tglobalContext,\n\t\t\tisSvg,\n\t\t\texcessDomChildren,\n\t\t\tcommitQueue,\n\t\t\toldDom,\n\t\t\tisHydrating\n\t\t);\n\n\t\tif ((j = childVNode.ref) && oldVNode.ref != j) {\n\t\t\tif (!refs) refs = [];\n\t\t\tif (oldVNode.ref) refs.push(oldVNode.ref, null, childVNode);\n\t\t\trefs.push(j, childVNode._component || newDom, childVNode);\n\t\t}\n\n\t\tif (newDom != null) {\n\t\t\tif (firstChildDom == null) {\n\t\t\t\tfirstChildDom = newDom;\n\t\t\t}\n\n\t\t\toldDom = placeChild(\n\t\t\t\tparentDom,\n\t\t\t\tchildVNode,\n\t\t\t\toldVNode,\n\t\t\t\toldChildren,\n\t\t\t\texcessDomChildren,\n\t\t\t\tnewDom,\n\t\t\t\toldDom\n\t\t\t);\n\n\t\t\t// Browsers will infer an option's `value` from `textContent` when\n\t\t\t// no value is present. This essentially bypasses our code to set it\n\t\t\t// later in `diff()`. It works fine in all browsers except for IE11\n\t\t\t// where it breaks setting `select.value`. There it will be always set\n\t\t\t// to an empty string. Re-applying an options value will fix that, so\n\t\t\t// there are probably some internal data structures that aren't\n\t\t\t// updated properly.\n\t\t\t//\n\t\t\t// To fix it we make sure to reset the inferred value, so that our own\n\t\t\t// value check in `diff()` won't be skipped.\n\t\t\tif (newParentVNode.type == 'option') {\n\t\t\t\tparentDom.value = '';\n\t\t\t} else if (typeof newParentVNode.type == 'function') {\n\t\t\t\t// Because the newParentVNode is Fragment-like, we need to set it's\n\t\t\t\t// _nextDom property to the nextSibling of its last child DOM node.\n\t\t\t\t//\n\t\t\t\t// `oldDom` contains the correct value here because if the last child\n\t\t\t\t// is a Fragment-like, then oldDom has already been set to that child's _nextDom.\n\t\t\t\t// If the last child is a DOM VNode, then oldDom will be set to that DOM\n\t\t\t\t// node's nextSibling.\n\t\t\t\tnewParentVNode._nextDom = oldDom;\n\t\t\t}\n\t\t} else if (\n\t\t\toldDom &&\n\t\t\toldVNode._dom == oldDom &&\n\t\t\toldDom.parentNode != parentDom\n\t\t) {\n\t\t\t// The above condition is to handle null placeholders. See test in placeholder.test.js:\n\t\t\t// `efficiently replace null placeholders in parent rerenders`\n\t\t\toldDom = getDomSibling(oldVNode);\n\t\t}\n\t}\n\n\tnewParentVNode._dom = firstChildDom;\n\n\t// Remove children that are not part of any vnode.\n\tif (excessDomChildren != null && typeof newParentVNode.type != 'function') {\n\t\tfor (i = excessDomChildren.length; i--; ) {\n\t\t\tif (excessDomChildren[i] != null) removeNode(excessDomChildren[i]);\n\t\t}\n\t}\n\n\t// Remove remaining oldChildren if there are any.\n\tfor (i = oldChildrenLength; i--; ) {\n\t\tif (oldChildren[i] != null) unmount(oldChildren[i], oldChildren[i]);\n\t}\n\n\t// Set refs only after unmount\n\tif (refs) {\n\t\tfor (i = 0; i < refs.length; i++) {\n\t\t\tapplyRef(refs[i], refs[++i], refs[++i]);\n\t\t}\n\t}\n}\n\n/**\n * Flatten and loop through the children of a virtual node\n * @param {import('../index').ComponentChildren} children The unflattened\n * children of a virtual node\n * @returns {import('../internal').VNode[]}\n */\nexport function toChildArray(children) {\n\tif (children == null || typeof children == 'boolean') {\n\t\treturn [];\n\t} else if (Array.isArray(children)) {\n\t\treturn EMPTY_ARR.concat.apply([], children.map(toChildArray));\n\t}\n\n\treturn [children];\n}\n\nexport function placeChild(\n\tparentDom,\n\tchildVNode,\n\toldVNode,\n\toldChildren,\n\texcessDomChildren,\n\tnewDom,\n\toldDom\n) {\n\tlet nextDom;\n\tif (childVNode._nextDom !== undefined) {\n\t\t// Only Fragments or components that return Fragment like VNodes will\n\t\t// have a non-undefined _nextDom. Continue the diff from the sibling\n\t\t// of last DOM child of this child VNode\n\t\tnextDom = childVNode._nextDom;\n\n\t\t// Eagerly cleanup _nextDom. We don't need to persist the value because\n\t\t// it is only used by `diffChildren` to determine where to resume the diff after\n\t\t// diffing Components and Fragments. Once we store it the nextDOM local var, we\n\t\t// can clean up the property\n\t\tchildVNode._nextDom = undefined;\n\t} else if (\n\t\texcessDomChildren == oldVNode ||\n\t\tnewDom != oldDom ||\n\t\tnewDom.parentNode == null\n\t) {\n\t\t// NOTE: excessDomChildren==oldVNode above:\n\t\t// This is a compression of excessDomChildren==null && oldVNode==null!\n\t\t// The values only have the same type when `null`.\n\n\t\touter: if (oldDom == null || oldDom.parentNode !== parentDom) {\n\t\t\tparentDom.appendChild(newDom);\n\t\t\tnextDom = null;\n\t\t} else {\n\t\t\t// `j<oldChildrenLength; j+=2` is an alternative to `j++<oldChildrenLength/2`\n\t\t\tfor (\n\t\t\t\tlet sibDom = oldDom, j = 0;\n\t\t\t\t(sibDom = sibDom.nextSibling) && j < oldChildren.length;\n\t\t\t\tj += 2\n\t\t\t) {\n\t\t\t\tif (sibDom == newDom) {\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\t\t\t}\n\t\t\tparentDom.insertBefore(newDom, oldDom);\n\t\t\tnextDom = oldDom;\n\t\t}\n\t}\n\n\t// If we have pre-calculated the nextDOM node, use it. Else calculate it now\n\t// Strictly check for `undefined` here cuz `null` is a valid value of `nextDom`.\n\t// See more detail in create-element.js:createVNode\n\tif (nextDom !== undefined) {\n\t\toldDom = nextDom;\n\t} else {\n\t\toldDom = newDom.nextSibling;\n\t}\n\n\treturn oldDom;\n}\n", "import { IS_NON_DIMENSIONAL } from '../constants';\nimport options from '../options';\n\n/**\n * Diff the old and new properties of a VNode and apply changes to the DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to apply\n * changes to\n * @param {object} newProps The new props\n * @param {object} oldProps The old props\n * @param {boolean} isSvg Whether or not this node is an SVG node\n * @param {boolean} hydrate Whether or not we are in hydration mode\n */\nexport function diffProps(dom, newProps, oldProps, isSvg, hydrate) {\n\tlet i;\n\n\tfor (i in oldProps) {\n\t\tif (i !== 'children' && i !== 'key' && !(i in newProps)) {\n\t\t\tsetProperty(dom, i, null, oldProps[i], isSvg);\n\t\t}\n\t}\n\n\tfor (i in newProps) {\n\t\tif (\n\t\t\t(!hydrate || typeof newProps[i] == 'function') &&\n\t\t\ti !== 'children' &&\n\t\t\ti !== 'key' &&\n\t\t\ti !== 'value' &&\n\t\t\ti !== 'checked' &&\n\t\t\toldProps[i] !== newProps[i]\n\t\t) {\n\t\t\tsetProperty(dom, i, newProps[i], oldProps[i], isSvg);\n\t\t}\n\t}\n}\n\nfunction setStyle(style, key, value) {\n\tif (key[0] === '-') {\n\t\tstyle.setProperty(key, value);\n\t} else if (\n\t\ttypeof value == 'number' &&\n\t\tIS_NON_DIMENSIONAL.test(key) === false\n\t) {\n\t\tstyle[key] = value + 'px';\n\t} else if (value == null) {\n\t\tstyle[key] = '';\n\t} else {\n\t\tstyle[key] = value;\n\t}\n}\n\n/**\n * Set a property value on a DOM node\n * @param {import('../internal').PreactElement} dom The DOM node to modify\n * @param {string} name The name of the property to set\n * @param {*} value The value to set the property to\n * @param {*} oldValue The old value the property had\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node or not\n */\nexport function setProperty(dom, name, value, oldValue, isSvg) {\n\tlet s, useCapture, nameLower;\n\n\tif (isSvg) {\n\t\tif (name === 'className') {\n\t\t\tname = 'class';\n\t\t}\n\t} else if (name === 'class') {\n\t\tname = 'className';\n\t}\n\n\tif (name === 'style') {\n\t\ts = dom.style;\n\n\t\tif (typeof value == 'string') {\n\t\t\ts.cssText = value;\n\t\t} else {\n\t\t\tif (typeof oldValue == 'string') {\n\t\t\t\ts.cssText = '';\n\t\t\t\toldValue = null;\n\t\t\t}\n\n\t\t\tif (oldValue) {\n\t\t\t\tfor (let i in oldValue) {\n\t\t\t\t\tif (!(value && i in value)) {\n\t\t\t\t\t\tsetStyle(s, i, '');\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (value) {\n\t\t\t\tfor (let i in value) {\n\t\t\t\t\tif (!oldValue || value[i] !== oldValue[i]) {\n\t\t\t\t\t\tsetStyle(s, i, value[i]);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n\t// Benchmark for comparison: https://esbench.com/bench/574c954bdb965b9a00965ac6\n\telse if (name[0] === 'o' && name[1] === 'n') {\n\t\tuseCapture = name !== (name = name.replace(/Capture$/, ''));\n\t\tnameLower = name.toLowerCase();\n\t\tname = (nameLower in dom ? nameLower : name).slice(2);\n\n\t\tif (value) {\n\t\t\tif (!oldValue) dom.addEventListener(name, eventProxy, useCapture);\n\t\t\t(dom._listeners || (dom._listeners = {}))[name] = value;\n\t\t} else {\n\t\t\tdom.removeEventListener(name, eventProxy, useCapture);\n\t\t}\n\t} else if (\n\t\tname !== 'list' &&\n\t\tname !== 'tagName' &&\n\t\t// HTMLButtonElement.form and HTMLInputElement.form are read-only but can be set using\n\t\t// setAttribute\n\t\tname !== 'form' &&\n\t\tname !== 'type' &&\n\t\tname !== 'size' &&\n\t\t!isSvg &&\n\t\tname in dom\n\t) {\n\t\tdom[name] = value == null ? '' : value;\n\t} else if (typeof value != 'function' && name !== 'dangerouslySetInnerHTML') {\n\t\tif (name !== (name = name.replace(/^xlink:?/, ''))) {\n\t\t\tif (value == null || value === false) {\n\t\t\t\tdom.removeAttributeNS(\n\t\t\t\t\t'http://www.w3.org/1999/xlink',\n\t\t\t\t\tname.toLowerCase()\n\t\t\t\t);\n\t\t\t} else {\n\t\t\t\tdom.setAttributeNS(\n\t\t\t\t\t'http://www.w3.org/1999/xlink',\n\t\t\t\t\tname.toLowerCase(),\n\t\t\t\t\tvalue\n\t\t\t\t);\n\t\t\t}\n\t\t} else if (\n\t\t\tvalue == null ||\n\t\t\t(value === false &&\n\t\t\t\t// ARIA-attributes have a different notion of boolean values.\n\t\t\t\t// The value `false` is different from the attribute not\n\t\t\t\t// existing on the DOM, so we can't remove it. For non-boolean\n\t\t\t\t// ARIA-attributes we could treat false as a removal, but the\n\t\t\t\t// amount of exceptions would cost us too many bytes. On top of\n\t\t\t\t// that other VDOM frameworks also always stringify `false`.\n\t\t\t\t!/^ar/.test(name))\n\t\t) {\n\t\t\tdom.removeAttribute(name);\n\t\t} else {\n\t\t\tdom.setAttribute(name, value);\n\t\t}\n\t}\n}\n\n/**\n * Proxy an event to hooked event handlers\n * @param {Event} e The event object from the browser\n * @private\n */\nfunction eventProxy(e) {\n\tthis._listeners[e.type](options.event ? options.event(e) : e);\n}\n", "import { EMPTY_OBJ, EMPTY_ARR } from '../constants';\nimport { Component } from '../component';\nimport { Fragment } from '../create-element';\nimport { diffChildren, placeChild } from './children';\nimport { diffProps, setProperty } from './props';\nimport { assign, removeNode } from '../util';\nimport options from '../options';\n\nfunction reorderChildren(newVNode, oldDom, parentDom) {\n\tfor (let tmp = 0; tmp < newVNode._children.length; tmp++) {\n\t\tconst vnode = newVNode._children[tmp];\n\t\tif (vnode) {\n\t\t\tvnode._parent = newVNode;\n\n\t\t\tif (vnode._dom) {\n\t\t\t\tif (typeof vnode.type == 'function' && vnode._children.length > 1) {\n\t\t\t\t\treorderChildren(vnode, oldDom, parentDom);\n\t\t\t\t}\n\n\t\t\t\toldDom = placeChild(\n\t\t\t\t\tparentDom,\n\t\t\t\t\tvnode,\n\t\t\t\t\tvnode,\n\t\t\t\t\tnewVNode._children,\n\t\t\t\t\tnull,\n\t\t\t\t\tvnode._dom,\n\t\t\t\t\toldDom\n\t\t\t\t);\n\n\t\t\t\tif (typeof newVNode.type == 'function') {\n\t\t\t\t\tnewVNode._nextDom = oldDom;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\t}\n}\n\n/**\n * Diff two virtual nodes and apply proper changes to the DOM\n * @param {import('../internal').PreactElement} parentDom The parent of the DOM element\n * @param {import('../internal').VNode} newVNode The new virtual node\n * @param {import('../internal').VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object. Modified by getChildContext\n * @param {boolean} isSvg Whether or not this element is an SVG node\n * @param {Array<import('../internal').PreactElement>} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {Element | Text} oldDom The current attached DOM\n * element any new dom elements should be placed around. Likely `null` on first\n * render (except when hydrating). Can be a sibling DOM element when diffing\n * Fragments that have siblings. In most cases, it starts out as `oldChildren[0]._dom`.\n * @param {boolean} [isHydrating] Whether or not we are in hydration\n */\nexport function diff(\n\tparentDom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\toldDom,\n\tisHydrating\n) {\n\tlet tmp,\n\t\tnewType = newVNode.type;\n\n\t// When passing through createElement it assigns the object\n\t// constructor as undefined. This to prevent JSON-injection.\n\tif (newVNode.constructor !== undefined) return null;\n\n\tif ((tmp = options._diff)) tmp(newVNode);\n\n\ttry {\n\t\touter: if (typeof newType == 'function') {\n\t\t\tlet c, isNew, oldProps, oldState, snapshot, clearProcessingException;\n\t\t\tlet newProps = newVNode.props;\n\n\t\t\t// Necessary for createContext api. Setting this property will pass\n\t\t\t// the context value as `this.context` just for this component.\n\t\t\ttmp = newType.contextType;\n\t\t\tlet provider = tmp && globalContext[tmp._id];\n\t\t\tlet componentContext = tmp\n\t\t\t\t? provider\n\t\t\t\t\t? provider.props.value\n\t\t\t\t\t: tmp._defaultValue\n\t\t\t\t: globalContext;\n\n\t\t\t// Get component and set it to `c`\n\t\t\tif (oldVNode._component) {\n\t\t\t\tc = newVNode._component = oldVNode._component;\n\t\t\t\tclearProcessingException = c._processingException = c._pendingError;\n\t\t\t} else {\n\t\t\t\t// Instantiate the new component\n\t\t\t\tif ('prototype' in newType && newType.prototype.render) {\n\t\t\t\t\tnewVNode._component = c = new newType(newProps, componentContext); // eslint-disable-line new-cap\n\t\t\t\t} else {\n\t\t\t\t\tnewVNode._component = c = new Component(newProps, componentContext);\n\t\t\t\t\tc.constructor = newType;\n\t\t\t\t\tc.render = doRender;\n\t\t\t\t}\n\t\t\t\tif (provider) provider.sub(c);\n\n\t\t\t\tc.props = newProps;\n\t\t\t\tif (!c.state) c.state = {};\n\t\t\t\tc.context = componentContext;\n\t\t\t\tc._globalContext = globalContext;\n\t\t\t\tisNew = c._dirty = true;\n\t\t\t\tc._renderCallbacks = [];\n\t\t\t}\n\n\t\t\t// Invoke getDerivedStateFromProps\n\t\t\tif (c._nextState == null) {\n\t\t\t\tc._nextState = c.state;\n\t\t\t}\n\t\t\tif (newType.getDerivedStateFromProps != null) {\n\t\t\t\tif (c._nextState == c.state) {\n\t\t\t\t\tc._nextState = assign({}, c._nextState);\n\t\t\t\t}\n\n\t\t\t\tassign(\n\t\t\t\t\tc._nextState,\n\t\t\t\t\tnewType.getDerivedStateFromProps(newProps, c._nextState)\n\t\t\t\t);\n\t\t\t}\n\n\t\t\toldProps = c.props;\n\t\t\toldState = c.state;\n\n\t\t\t// Invoke pre-render lifecycle methods\n\t\t\tif (isNew) {\n\t\t\t\tif (\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tc.componentWillMount != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillMount();\n\t\t\t\t}\n\n\t\t\t\tif (c.componentDidMount != null) {\n\t\t\t\t\tc._renderCallbacks.push(c.componentDidMount);\n\t\t\t\t}\n\t\t\t} else {\n\t\t\t\tif (\n\t\t\t\t\tnewType.getDerivedStateFromProps == null &&\n\t\t\t\t\tnewProps !== oldProps &&\n\t\t\t\t\tc.componentWillReceiveProps != null\n\t\t\t\t) {\n\t\t\t\t\tc.componentWillReceiveProps(newProps, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (\n\t\t\t\t\t(!c._force &&\n\t\t\t\t\t\tc.shouldComponentUpdate != null &&\n\t\t\t\t\t\tc.shouldComponentUpdate(\n\t\t\t\t\t\t\tnewProps,\n\t\t\t\t\t\t\tc._nextState,\n\t\t\t\t\t\t\tcomponentContext\n\t\t\t\t\t\t) === false) ||\n\t\t\t\t\tnewVNode._original === oldVNode._original\n\t\t\t\t) {\n\t\t\t\t\tc.props = newProps;\n\t\t\t\t\tc.state = c._nextState;\n\t\t\t\t\t// More info about this here: https://gist.github.com/JoviDeCroock/bec5f2ce93544d2e6070ef8e0036e4e8\n\t\t\t\t\tif (newVNode._original !== oldVNode._original) c._dirty = false;\n\t\t\t\t\tc._vnode = newVNode;\n\t\t\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t\t\t\tnewVNode._children = oldVNode._children;\n\t\t\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\t\t\tcommitQueue.push(c);\n\t\t\t\t\t}\n\n\t\t\t\t\treorderChildren(newVNode, oldDom, parentDom);\n\t\t\t\t\tbreak outer;\n\t\t\t\t}\n\n\t\t\t\tif (c.componentWillUpdate != null) {\n\t\t\t\t\tc.componentWillUpdate(newProps, c._nextState, componentContext);\n\t\t\t\t}\n\n\t\t\t\tif (c.componentDidUpdate != null) {\n\t\t\t\t\tc._renderCallbacks.push(() => {\n\t\t\t\t\t\tc.componentDidUpdate(oldProps, oldState, snapshot);\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tc.context = componentContext;\n\t\t\tc.props = newProps;\n\t\t\tc.state = c._nextState;\n\n\t\t\tif ((tmp = options._render)) tmp(newVNode);\n\n\t\t\tc._dirty = false;\n\t\t\tc._vnode = newVNode;\n\t\t\tc._parentDom = parentDom;\n\n\t\t\ttmp = c.render(c.props, c.state, c.context);\n\n\t\t\t// Handle setState called in render, see #2553\n\t\t\tc.state = c._nextState;\n\n\t\t\tif (c.getChildContext != null) {\n\t\t\t\tglobalContext = assign(assign({}, globalContext), c.getChildContext());\n\t\t\t}\n\n\t\t\tif (!isNew && c.getSnapshotBeforeUpdate != null) {\n\t\t\t\tsnapshot = c.getSnapshotBeforeUpdate(oldProps, oldState);\n\t\t\t}\n\n\t\t\tlet isTopLevelFragment =\n\t\t\t\ttmp != null && tmp.type == Fragment && tmp.key == null;\n\t\t\tlet renderResult = isTopLevelFragment ? tmp.props.children : tmp;\n\n\t\t\tdiffChildren(\n\t\t\t\tparentDom,\n\t\t\t\tArray.isArray(renderResult) ? renderResult : [renderResult],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\toldDom,\n\t\t\t\tisHydrating\n\t\t\t);\n\n\t\t\tc.base = newVNode._dom;\n\n\t\t\tif (c._renderCallbacks.length) {\n\t\t\t\tcommitQueue.push(c);\n\t\t\t}\n\n\t\t\tif (clearProcessingException) {\n\t\t\t\tc._pendingError = c._processingException = null;\n\t\t\t}\n\n\t\t\tc._force = false;\n\t\t} else if (\n\t\t\texcessDomChildren == null &&\n\t\t\tnewVNode._original === oldVNode._original\n\t\t) {\n\t\t\tnewVNode._children = oldVNode._children;\n\t\t\tnewVNode._dom = oldVNode._dom;\n\t\t} else {\n\t\t\tnewVNode._dom = diffElementNodes(\n\t\t\t\toldVNode._dom,\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tisSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\tisHydrating\n\t\t\t);\n\t\t}\n\n\t\tif ((tmp = options.diffed)) tmp(newVNode);\n\t} catch (e) {\n\t\tnewVNode._original = null;\n\t\toptions._catchError(e, newVNode, oldVNode);\n\t}\n\n\treturn newVNode._dom;\n}\n\n/**\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {import('../internal').VNode} root\n */\nexport function commitRoot(commitQueue, root) {\n\tif (options._commit) options._commit(root, commitQueue);\n\n\tcommitQueue.some(c => {\n\t\ttry {\n\t\t\tcommitQueue = c._renderCallbacks;\n\t\t\tc._renderCallbacks = [];\n\t\t\tcommitQueue.some(cb => {\n\t\t\t\tcb.call(c);\n\t\t\t});\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t});\n}\n\n/**\n * Diff two virtual nodes representing DOM element\n * @param {import('../internal').PreactElement} dom The DOM element representing\n * the virtual nodes being diffed\n * @param {import('../internal').VNode} newVNode The new virtual node\n * @param {import('../internal').VNode} oldVNode The old virtual node\n * @param {object} globalContext The current context object\n * @param {boolean} isSvg Whether or not this DOM node is an SVG node\n * @param {*} excessDomChildren\n * @param {Array<import('../internal').Component>} commitQueue List of components\n * which have callbacks to invoke in commitRoot\n * @param {boolean} isHydrating Whether or not we are in hydration\n * @returns {import('../internal').PreactElement}\n */\nfunction diffElementNodes(\n\tdom,\n\tnewVNode,\n\toldVNode,\n\tglobalContext,\n\tisSvg,\n\texcessDomChildren,\n\tcommitQueue,\n\tisHydrating\n) {\n\tlet i;\n\tlet oldProps = oldVNode.props;\n\tlet newProps = newVNode.props;\n\n\t// Tracks entering and exiting SVG namespace when descending through the tree.\n\tisSvg = newVNode.type === 'svg' || isSvg;\n\n\tif (excessDomChildren != null) {\n\t\tfor (i = 0; i < excessDomChildren.length; i++) {\n\t\t\tconst child = excessDomChildren[i];\n\n\t\t\t// if newVNode matches an element in excessDomChildren or the `dom`\n\t\t\t// argument matches an element in excessDomChildren, remove it from\n\t\t\t// excessDomChildren so it isn't later removed in diffChildren\n\t\t\tif (\n\t\t\t\tchild != null &&\n\t\t\t\t((newVNode.type === null\n\t\t\t\t\t? child.nodeType === 3\n\t\t\t\t\t: child.localName === newVNode.type) ||\n\t\t\t\t\tdom == child)\n\t\t\t) {\n\t\t\t\tdom = child;\n\t\t\t\texcessDomChildren[i] = null;\n\t\t\t\tbreak;\n\t\t\t}\n\t\t}\n\t}\n\n\tif (dom == null) {\n\t\tif (newVNode.type === null) {\n\t\t\treturn document.createTextNode(newProps);\n\t\t}\n\n\t\tdom = isSvg\n\t\t\t? document.createElementNS('http://www.w3.org/2000/svg', newVNode.type)\n\t\t\t: document.createElement(\n\t\t\t\t\tnewVNode.type,\n\t\t\t\t\tnewProps.is && { is: newProps.is }\n\t\t\t  );\n\t\t// we created a new parent, so none of the previously attached children can be reused:\n\t\texcessDomChildren = null;\n\t\t// we are creating a new node, so we can assume this is a new subtree (in case we are hydrating), this deopts the hydrate\n\t\tisHydrating = false;\n\t}\n\n\tif (newVNode.type === null) {\n\t\tif (oldProps !== newProps && dom.data != newProps) {\n\t\t\tdom.data = newProps;\n\t\t}\n\t} else {\n\t\tif (excessDomChildren != null) {\n\t\t\texcessDomChildren = EMPTY_ARR.slice.call(dom.childNodes);\n\t\t}\n\n\t\toldProps = oldVNode.props || EMPTY_OBJ;\n\n\t\tlet oldHtml = oldProps.dangerouslySetInnerHTML;\n\t\tlet newHtml = newProps.dangerouslySetInnerHTML;\n\n\t\t// During hydration, props are not diffed at all (including dangerouslySetInnerHTML)\n\t\t// @TODO we should warn in debug mode when props don't match here.\n\t\tif (!isHydrating) {\n\t\t\t// But, if we are in a situation where we are using existing DOM (e.g. replaceNode)\n\t\t\t// we should read the existing DOM attributes to diff them\n\t\t\tif (excessDomChildren != null) {\n\t\t\t\toldProps = {};\n\t\t\t\tfor (let i = 0; i < dom.attributes.length; i++) {\n\t\t\t\t\toldProps[dom.attributes[i].name] = dom.attributes[i].value;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (newHtml || oldHtml) {\n\t\t\t\t// Avoid re-applying the same '__html' if it did not changed between re-render\n\t\t\t\tif (!newHtml || !oldHtml || newHtml.__html != oldHtml.__html) {\n\t\t\t\t\tdom.innerHTML = (newHtml && newHtml.__html) || '';\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tdiffProps(dom, newProps, oldProps, isSvg, isHydrating);\n\n\t\t// If the new vnode didn't have dangerouslySetInnerHTML, diff its children\n\t\tif (newHtml) {\n\t\t\tnewVNode._children = [];\n\t\t} else {\n\t\t\ti = newVNode.props.children;\n\t\t\tdiffChildren(\n\t\t\t\tdom,\n\t\t\t\tArray.isArray(i) ? i : [i],\n\t\t\t\tnewVNode,\n\t\t\t\toldVNode,\n\t\t\t\tglobalContext,\n\t\t\t\tnewVNode.type === 'foreignObject' ? false : isSvg,\n\t\t\t\texcessDomChildren,\n\t\t\t\tcommitQueue,\n\t\t\t\tEMPTY_OBJ,\n\t\t\t\tisHydrating\n\t\t\t);\n\t\t}\n\n\t\t// (as above, don't diff props during hydration)\n\t\tif (!isHydrating) {\n\t\t\tif (\n\t\t\t\t'value' in newProps &&\n\t\t\t\t(i = newProps.value) !== undefined &&\n\t\t\t\ti !== dom.value\n\t\t\t) {\n\t\t\t\tsetProperty(dom, 'value', i, oldProps.value, false);\n\t\t\t}\n\t\t\tif (\n\t\t\t\t'checked' in newProps &&\n\t\t\t\t(i = newProps.checked) !== undefined &&\n\t\t\t\ti !== dom.checked\n\t\t\t) {\n\t\t\t\tsetProperty(dom, 'checked', i, oldProps.checked, false);\n\t\t\t}\n\t\t}\n\t}\n\n\treturn dom;\n}\n\n/**\n * Invoke or update a ref, depending on whether it is a function or object ref.\n * @param {object|function} ref\n * @param {any} value\n * @param {import('../internal').VNode} vnode\n */\nexport function applyRef(ref, value, vnode) {\n\ttry {\n\t\tif (typeof ref == 'function') ref(value);\n\t\telse ref.current = value;\n\t} catch (e) {\n\t\toptions._catchError(e, vnode);\n\t}\n}\n\n/**\n * Unmount a virtual node from the tree and apply DOM changes\n * @param {import('../internal').VNode} vnode The virtual node to unmount\n * @param {import('../internal').VNode} parentVNode The parent of the VNode that\n * initiated the unmount\n * @param {boolean} [skipRemove] Flag that indicates that a parent node of the\n * current element is already detached from the DOM.\n */\nexport function unmount(vnode, parentVNode, skipRemove) {\n\tlet r;\n\tif (options.unmount) options.unmount(vnode);\n\n\tif ((r = vnode.ref)) {\n\t\tif (!r.current || r.current === vnode._dom) applyRef(r, null, parentVNode);\n\t}\n\n\tlet dom;\n\tif (!skipRemove && typeof vnode.type != 'function') {\n\t\tskipRemove = (dom = vnode._dom) != null;\n\t}\n\n\t// Must be set to `undefined` to properly clean up `_nextDom`\n\t// for which `null` is a valid value. See comment in `create-element.js`\n\tvnode._dom = vnode._nextDom = undefined;\n\n\tif ((r = vnode._component) != null) {\n\t\tif (r.componentWillUnmount) {\n\t\t\ttry {\n\t\t\t\tr.componentWillUnmount();\n\t\t\t} catch (e) {\n\t\t\t\toptions._catchError(e, parentVNode);\n\t\t\t}\n\t\t}\n\n\t\tr.base = r._parentDom = null;\n\t}\n\n\tif ((r = vnode._children)) {\n\t\tfor (let i = 0; i < r.length; i++) {\n\t\t\tif (r[i]) unmount(r[i], parentVNode, skipRemove);\n\t\t}\n\t}\n\n\tif (dom != null) removeNode(dom);\n}\n\n/** The `.render()` method for a PFC backing instance. */\nfunction doRender(props, state, context) {\n\treturn this.constructor(props, context);\n}\n", "import { assign } from './util';\nimport { EMPTY_ARR } from './constants';\nimport { createVNode } from './create-element';\n\n/**\n * Clones the given VNode, optionally adding attributes/props and replacing its children.\n * @param {import('./internal').VNode} vnode The virtual DOM element to clone\n * @param {object} props Attributes/props to add when cloning\n * @param {Array<import('./index').ComponentChildren>} rest Any additional arguments will be used as replacement children.\n * @returns {import('./internal').VNode}\n */\nexport function cloneElement(vnode, props) {\n\tprops = assign(assign({}, vnode.props), props);\n\tif (arguments.length > 2) props.children = EMPTY_ARR.slice.call(arguments, 2);\n\tlet normalizedProps = {};\n\tfor (const i in props) {\n\t\tif (i !== 'key' && i !== 'ref') normalizedProps[i] = props[i];\n\t}\n\n\treturn createVNode(\n\t\tvnode.type,\n\t\tnormalizedProps,\n\t\tprops.key || vnode.key,\n\t\tprops.ref || vnode.ref,\n\t\tnull\n\t);\n}\n", "import { enqueueRender } from '../component';\n\n/**\n * Find the closest error boundary to a thrown error and call it\n * @param {object} error The thrown value\n * @param {import('../internal').VNode} vnode The vnode that threw\n * the error that was caught (except for unmounting when this parameter\n * is the highest parent that was being unmounted)\n */\nexport function _catchError(error, vnode) {\n\t/** @type {import('../internal').Component} */\n\tlet component, hasCaught;\n\n\tfor (; (vnode = vnode._parent); ) {\n\t\tif ((component = vnode._component) && !component._processingException) {\n\t\t\ttry {\n\t\t\t\tif (\n\t\t\t\t\tcomponent.constructor &&\n\t\t\t\t\tcomponent.constructor.getDerivedStateFromError != null\n\t\t\t\t) {\n\t\t\t\t\thasCaught = true;\n\t\t\t\t\tcomponent.setState(\n\t\t\t\t\t\tcomponent.constructor.getDerivedStateFromError(error)\n\t\t\t\t\t);\n\t\t\t\t}\n\n\t\t\t\tif (component.componentDidCatch != null) {\n\t\t\t\t\thasCaught = true;\n\t\t\t\t\tcomponent.componentDidCatch(error);\n\t\t\t\t}\n\n\t\t\t\tif (hasCaught)\n\t\t\t\t\treturn enqueueRender((component._pendingError = component));\n\t\t\t} catch (e) {\n\t\t\t\terror = e;\n\t\t\t}\n\t\t}\n\t}\n\n\tthrow error;\n}\n", "import { options } from 'preact';\n\n/** @type {number} */\nlet currentIndex;\n\n/** @type {import('./internal').Component} */\nlet currentComponent;\n\n/** @type {number} */\nlet currentHook = 0;\n\n/** @type {Array<import('./internal').Component>} */\nlet afterPaintEffects = [];\n\nlet oldBeforeRender = options._render;\nlet oldAfterDiff = options.diffed;\nlet oldCommit = options._commit;\nlet oldBeforeUnmount = options.unmount;\n\nconst RAF_TIMEOUT = 100;\nlet prevRaf;\n\noptions._render = vnode => {\n\tif (oldBeforeRender) oldBeforeRender(vnode);\n\n\tcurrentComponent = vnode._component;\n\tcurrentIndex = 0;\n\n\tconst hooks = currentComponent.__hooks;\n\tif (hooks) {\n\t\thooks._pendingEffects.forEach(invokeCleanup);\n\t\thooks._pendingEffects.forEach(invokeEffect);\n\t\thooks._pendingEffects = [];\n\t}\n};\n\noptions.diffed = vnode => {\n\tif (oldAfterDiff) oldAfterDiff(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks && c.__hooks._pendingEffects.length) {\n\t\tafterPaint(afterPaintEffects.push(c));\n\t}\n};\n\noptions._commit = (vnode, commitQueue) => {\n\tcommitQueue.some(component => {\n\t\ttry {\n\t\t\tcomponent._renderCallbacks.forEach(invokeCleanup);\n\t\t\tcomponent._renderCallbacks = component._renderCallbacks.filter(cb =>\n\t\t\t\tcb._value ? invokeEffect(cb) : true\n\t\t\t);\n\t\t} catch (e) {\n\t\t\tcommitQueue.some(c => {\n\t\t\t\tif (c._renderCallbacks) c._renderCallbacks = [];\n\t\t\t});\n\t\t\tcommitQueue = [];\n\t\t\toptions._catchError(e, component._vnode);\n\t\t}\n\t});\n\n\tif (oldCommit) oldCommit(vnode, commitQueue);\n};\n\noptions.unmount = vnode => {\n\tif (oldBeforeUnmount) oldBeforeUnmount(vnode);\n\n\tconst c = vnode._component;\n\tif (c && c.__hooks) {\n\t\ttry {\n\t\t\tc.__hooks._list.forEach(invokeCleanup);\n\t\t} catch (e) {\n\t\t\toptions._catchError(e, c._vnode);\n\t\t}\n\t}\n};\n\n/**\n * Get a hook's state from the currentComponent\n * @param {number} index The index of the hook to get\n * @param {number} type The index of the hook to get\n * @returns {import('./internal').HookState}\n */\nfunction getHookState(index, type) {\n\tif (options._hook) {\n\t\toptions._hook(currentComponent, index, currentHook || type);\n\t}\n\tcurrentHook = 0;\n\n\t// Largely inspired by:\n\t// * https://github.com/michael-klein/funcy.js/blob/f6be73468e6ec46b0ff5aa3cc4c9baf72a29025a/src/hooks/core_hooks.mjs\n\t// * https://github.com/michael-klein/funcy.js/blob/650beaa58c43c33a74820a3c98b3c7079cf2e333/src/renderer.mjs\n\t// Other implementations to look at:\n\t// * https://codesandbox.io/s/mnox05qp8\n\tconst hooks =\n\t\tcurrentComponent.__hooks ||\n\t\t(currentComponent.__hooks = {\n\t\t\t_list: [],\n\t\t\t_pendingEffects: []\n\t\t});\n\n\tif (index >= hooks._list.length) {\n\t\thooks._list.push({});\n\t}\n\treturn hooks._list[index];\n}\n\n/**\n * @param {import('./index').StateUpdater<any>} initialState\n */\nexport function useState(initialState) {\n\tcurrentHook = 1;\n\treturn useReducer(invokeOrReturn, initialState);\n}\n\n/**\n * @param {import('./index').Reducer<any, any>} reducer\n * @param {import('./index').StateUpdater<any>} initialState\n * @param {(initialState: any) => void} [init]\n * @returns {[ any, (state: any) => void ]}\n */\nexport function useReducer(reducer, initialState, init) {\n\t/** @type {import('./internal').ReducerHookState} */\n\tconst hookState = getHookState(currentIndex++, 2);\n\thookState._reducer = reducer;\n\tif (!hookState._component) {\n\t\thookState._component = currentComponent;\n\n\t\thookState._value = [\n\t\t\t!init ? invokeOrReturn(undefined, initialState) : init(initialState),\n\n\t\t\taction => {\n\t\t\t\tconst nextValue = hookState._reducer(hookState._value[0], action);\n\t\t\t\tif (hookState._value[0] !== nextValue) {\n\t\t\t\t\thookState._value = [nextValue, hookState._value[1]];\n\t\t\t\t\thookState._component.setState({});\n\t\t\t\t}\n\t\t\t}\n\t\t];\n\t}\n\n\treturn hookState._value;\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {any[]} args\n */\nexport function useEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 3);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._args = args;\n\n\t\tcurrentComponent.__hooks._pendingEffects.push(state);\n\t}\n}\n\n/**\n * @param {import('./internal').Effect} callback\n * @param {any[]} args\n */\nexport function useLayoutEffect(callback, args) {\n\t/** @type {import('./internal').EffectHookState} */\n\tconst state = getHookState(currentIndex++, 4);\n\tif (!options._skipEffects && argsChanged(state._args, args)) {\n\t\tstate._value = callback;\n\t\tstate._args = args;\n\n\t\tcurrentComponent._renderCallbacks.push(state);\n\t}\n}\n\nexport function useRef(initialValue) {\n\tcurrentHook = 5;\n\treturn useMemo(() => ({ current: initialValue }), []);\n}\n\n/**\n * @param {object} ref\n * @param {() => object} createHandle\n * @param {any[]} args\n */\nexport function useImperativeHandle(ref, createHandle, args) {\n\tcurrentHook = 6;\n\tuseLayoutEffect(\n\t\t() => {\n\t\t\tif (typeof ref == 'function') ref(createHandle());\n\t\t\telse if (ref) ref.current = createHandle();\n\t\t},\n\t\targs == null ? args : args.concat(ref)\n\t);\n}\n\n/**\n * @param {() => any} factory\n * @param {any[]} args\n */\nexport function useMemo(factory, args) {\n\t/** @type {import('./internal').MemoHookState} */\n\tconst state = getHookState(currentIndex++, 7);\n\tif (argsChanged(state._args, args)) {\n\t\tstate._args = args;\n\t\tstate._factory = factory;\n\t\treturn (state._value = factory());\n\t}\n\n\treturn state._value;\n}\n\n/**\n * @param {() => void} callback\n * @param {any[]} args\n */\nexport function useCallback(callback, args) {\n\tcurrentHook = 8;\n\treturn useMemo(() => callback, args);\n}\n\n/**\n * @param {import('./internal').PreactContext} context\n */\nexport function useContext(context) {\n\tconst provider = currentComponent.context[context._id];\n\t// We could skip this call here, but than we'd not call\n\t// `options._hook`. We need to do that in order to make\n\t// the devtools aware of this hook.\n\tconst state = getHookState(currentIndex++, 9);\n\t// The devtools needs access to the context object to\n\t// be able to pull of the default value when no provider\n\t// is present in the tree.\n\tstate._context = context;\n\tif (!provider) return context._defaultValue;\n\t// This is probably not safe to convert to \"!\"\n\tif (state._value == null) {\n\t\tstate._value = true;\n\t\tprovider.sub(currentComponent);\n\t}\n\treturn provider.props.value;\n}\n\n/**\n * Display a custom label for a custom hook for the devtools panel\n * @type {<T>(value: T, cb?: (value: T) => string | number) => void}\n */\nexport function useDebugValue(value, formatter) {\n\tif (options.useDebugValue) {\n\t\toptions.useDebugValue(formatter ? formatter(value) : value);\n\t}\n}\n\nexport function useErrorBoundary(cb) {\n\tconst state = getHookState(currentIndex++, 10);\n\tconst errState = useState();\n\tstate._value = cb;\n\tif (!currentComponent.componentDidCatch) {\n\t\tcurrentComponent.componentDidCatch = err => {\n\t\t\tif (state._value) state._value(err);\n\t\t\terrState[1](err);\n\t\t};\n\t}\n\treturn [\n\t\terrState[0],\n\t\t() => {\n\t\t\terrState[1](undefined);\n\t\t}\n\t];\n}\n\n/**\n * After paint effects consumer.\n */\nfunction flushAfterPaintEffects() {\n\tafterPaintEffects.some(component => {\n\t\tif (component._parentDom) {\n\t\t\ttry {\n\t\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeCleanup);\n\t\t\t\tcomponent.__hooks._pendingEffects.forEach(invokeEffect);\n\t\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t\t} catch (e) {\n\t\t\t\tcomponent.__hooks._pendingEffects = [];\n\t\t\t\toptions._catchError(e, component._vnode);\n\t\t\t\treturn true;\n\t\t\t}\n\t\t}\n\t});\n\tafterPaintEffects = [];\n}\n\nlet HAS_RAF = typeof requestAnimationFrame == 'function';\n\n/**\n * Schedule a callback to be invoked after the browser has a chance to paint a new frame.\n * Do this by combining requestAnimationFrame (rAF) + setTimeout to invoke a callback after\n * the next browser frame.\n *\n * Also, schedule a timeout in parallel to the the rAF to ensure the callback is invoked\n * even if RAF doesn't fire (for example if the browser tab is not visible)\n *\n * @param {() => void} callback\n */\nfunction afterNextFrame(callback) {\n\tconst done = () => {\n\t\tclearTimeout(timeout);\n\t\tif (HAS_RAF) cancelAnimationFrame(raf);\n\t\tsetTimeout(callback);\n\t};\n\tconst timeout = setTimeout(done, RAF_TIMEOUT);\n\n\tlet raf;\n\tif (HAS_RAF) {\n\t\traf = requestAnimationFrame(done);\n\t}\n}\n\n// Note: if someone used options.debounceRendering = requestAnimationFrame,\n// then effects will ALWAYS run on the NEXT frame instead of the current one, incurring a ~16ms delay.\n// Perhaps this is not such a big deal.\n/**\n * Schedule afterPaintEffects flush after the browser paints\n * @param {number} newQueueLength\n */\nfunction afterPaint(newQueueLength) {\n\tif (newQueueLength === 1 || prevRaf !== options.requestAnimationFrame) {\n\t\tprevRaf = options.requestAnimationFrame;\n\t\t(prevRaf || afterNextFrame)(flushAfterPaintEffects);\n\t}\n}\n\n/**\n * @param {import('./internal').EffectHookState} hook\n */\nfunction invokeCleanup(hook) {\n\tif (typeof hook._cleanup == 'function') hook._cleanup();\n}\n\n/**\n * Invoke a Hook's effect\n * @param {import('./internal').EffectHookState} hook\n */\nfunction invokeEffect(hook) {\n\thook._cleanup = hook._value();\n}\n\n/**\n * @param {any[]} oldArgs\n * @param {any[]} newArgs\n */\nfunction argsChanged(oldArgs, newArgs) {\n\treturn !oldArgs || newArgs.some((arg, index) => arg !== oldArgs[index]);\n}\n\nfunction invokeOrReturn(arg, f) {\n\treturn typeof f == 'function' ? f(arg) : f;\n}\n", "/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\n\nconst _emojiMap: Record<string, string> = JSON.parse(`{\n  \"100\": \"💯\",\n  \"1234\": \"🔢\",\n  \"grinning\": \"😀\",\n  \"smiley\": \"😃\",\n  \"smile\": \"😄\",\n  \"grin\": \"😁\",\n  \"laughing\": \"😆\",\n  \"satisfied\": \"😆\",\n  \"sweat_smile\": \"😅\",\n  \"rofl\": \"🤣\",\n  \"joy\": \"😂\",\n  \"slightly_smiling_face\": \"🙂\",\n  \"upside_down_face\": \"🙃\",\n  \"wink\": \"😉\",\n  \"blush\": \"😊\",\n  \"innocent\": \"😇\",\n  \"smiling_face_with_three_hearts\": \"🥰\",\n  \"heart_eyes\": \"😍\",\n  \"star_struck\": \"🤩\",\n  \"kissing_heart\": \"😘\",\n  \"kissing\": \"😗\",\n  \"relaxed\": \"☺️\",\n  \"kissing_closed_eyes\": \"😚\",\n  \"kissing_smiling_eyes\": \"😙\",\n  \"smiling_face_with_tear\": \"🥲\",\n  \"yum\": \"😋\",\n  \"stuck_out_tongue\": \"😛\",\n  \"stuck_out_tongue_winking_eye\": \"😜\",\n  \"zany_face\": \"🤪\",\n  \"stuck_out_tongue_closed_eyes\": \"😝\",\n  \"money_mouth_face\": \"🤑\",\n  \"hugs\": \"🤗\",\n  \"hand_over_mouth\": \"🤭\",\n  \"shushing_face\": \"🤫\",\n  \"thinking\": \"🤔\",\n  \"zipper_mouth_face\": \"🤐\",\n  \"raised_eyebrow\": \"🤨\",\n  \"neutral_face\": \"😐\",\n  \"expressionless\": \"😑\",\n  \"no_mouth\": \"😶\",\n  \"smirk\": \"😏\",\n  \"unamused\": \"😒\",\n  \"roll_eyes\": \"🙄\",\n  \"grimacing\": \"😬\",\n  \"lying_face\": \"🤥\",\n  \"relieved\": \"😌\",\n  \"pensive\": \"😔\",\n  \"sleepy\": \"😪\",\n  \"drooling_face\": \"🤤\",\n  \"sleeping\": \"😴\",\n  \"mask\": \"😷\",\n  \"face_with_thermometer\": \"🤒\",\n  \"face_with_head_bandage\": \"🤕\",\n  \"nauseated_face\": \"🤢\",\n  \"vomiting_face\": \"🤮\",\n  \"sneezing_face\": \"🤧\",\n  \"hot_face\": \"🥵\",\n  \"cold_face\": \"🥶\",\n  \"woozy_face\": \"🥴\",\n  \"dizzy_face\": \"😵\",\n  \"exploding_head\": \"🤯\",\n  \"cowboy_hat_face\": \"🤠\",\n  \"partying_face\": \"🥳\",\n  \"disguised_face\": \"🥸\",\n  \"sunglasses\": \"😎\",\n  \"nerd_face\": \"🤓\",\n  \"monocle_face\": \"🧐\",\n  \"confused\": \"😕\",\n  \"worried\": \"😟\",\n  \"slightly_frowning_face\": \"🙁\",\n  \"frowning_face\": \"☹️\",\n  \"open_mouth\": \"😮\",\n  \"hushed\": \"😯\",\n  \"astonished\": \"😲\",\n  \"flushed\": \"😳\",\n  \"pleading_face\": \"🥺\",\n  \"frowning\": \"😦\",\n  \"anguished\": \"😧\",\n  \"fearful\": \"😨\",\n  \"cold_sweat\": \"😰\",\n  \"disappointed_relieved\": \"😥\",\n  \"cry\": \"😢\",\n  \"sob\": \"😭\",\n  \"scream\": \"😱\",\n  \"confounded\": \"😖\",\n  \"persevere\": \"😣\",\n  \"disappointed\": \"😞\",\n  \"sweat\": \"😓\",\n  \"weary\": \"😩\",\n  \"tired_face\": \"😫\",\n  \"yawning_face\": \"🥱\",\n  \"triumph\": \"😤\",\n  \"rage\": \"😡\",\n  \"pout\": \"😡\",\n  \"angry\": \"😠\",\n  \"cursing_face\": \"🤬\",\n  \"smiling_imp\": \"😈\",\n  \"imp\": \"👿\",\n  \"skull\": \"💀\",\n  \"skull_and_crossbones\": \"☠️\",\n  \"hankey\": \"💩\",\n  \"poop\": \"💩\",\n  \"shit\": \"💩\",\n  \"clown_face\": \"🤡\",\n  \"japanese_ogre\": \"👹\",\n  \"japanese_goblin\": \"👺\",\n  \"ghost\": \"👻\",\n  \"alien\": \"👽\",\n  \"space_invader\": \"👾\",\n  \"robot\": \"🤖\",\n  \"smiley_cat\": \"😺\",\n  \"smile_cat\": \"😸\",\n  \"joy_cat\": \"😹\",\n  \"heart_eyes_cat\": \"😻\",\n  \"smirk_cat\": \"😼\",\n  \"kissing_cat\": \"😽\",\n  \"scream_cat\": \"🙀\",\n  \"crying_cat_face\": \"😿\",\n  \"pouting_cat\": \"😾\",\n  \"see_no_evil\": \"🙈\",\n  \"hear_no_evil\": \"🙉\",\n  \"speak_no_evil\": \"🙊\",\n  \"kiss\": \"💋\",\n  \"love_letter\": \"💌\",\n  \"cupid\": \"💘\",\n  \"gift_heart\": \"💝\",\n  \"sparkling_heart\": \"💖\",\n  \"heartpulse\": \"💗\",\n  \"heartbeat\": \"💓\",\n  \"revolving_hearts\": \"💞\",\n  \"two_hearts\": \"💕\",\n  \"heart_decoration\": \"💟\",\n  \"heavy_heart_exclamation\": \"❣️\",\n  \"broken_heart\": \"💔\",\n  \"heart\": \"❤️\",\n  \"orange_heart\": \"🧡\",\n  \"yellow_heart\": \"💛\",\n  \"green_heart\": \"💚\",\n  \"blue_heart\": \"💙\",\n  \"purple_heart\": \"💜\",\n  \"brown_heart\": \"🤎\",\n  \"black_heart\": \"🖤\",\n  \"white_heart\": \"🤍\",\n  \"anger\": \"💢\",\n  \"boom\": \"💥\",\n  \"collision\": \"💥\",\n  \"dizzy\": \"💫\",\n  \"sweat_drops\": \"💦\",\n  \"dash\": \"💨\",\n  \"hole\": \"🕳️\",\n  \"bomb\": \"💣\",\n  \"speech_balloon\": \"💬\",\n  \"eye_speech_bubble\": \"👁️‍🗨️\",\n  \"left_speech_bubble\": \"🗨️\",\n  \"right_anger_bubble\": \"🗯️\",\n  \"thought_balloon\": \"💭\",\n  \"zzz\": \"💤\",\n  \"wave\": \"👋\",\n  \"raised_back_of_hand\": \"🤚\",\n  \"raised_hand_with_fingers_splayed\": \"🖐️\",\n  \"hand\": \"✋\",\n  \"raised_hand\": \"✋\",\n  \"vulcan_salute\": \"🖖\",\n  \"ok_hand\": \"👌\",\n  \"pinched_fingers\": \"🤌\",\n  \"pinching_hand\": \"🤏\",\n  \"v\": \"✌️\",\n  \"crossed_fingers\": \"🤞\",\n  \"love_you_gesture\": \"🤟\",\n  \"metal\": \"🤘\",\n  \"call_me_hand\": \"🤙\",\n  \"point_left\": \"👈\",\n  \"point_right\": \"👉\",\n  \"point_up_2\": \"👆\",\n  \"middle_finger\": \"🖕\",\n  \"fu\": \"🖕\",\n  \"point_down\": \"👇\",\n  \"point_up\": \"☝️\",\n  \"+1\": \"👍\",\n  \"thumbsup\": \"👍\",\n  \"-1\": \"👎\",\n  \"thumbsdown\": \"👎\",\n  \"fist_raised\": \"✊\",\n  \"fist\": \"✊\",\n  \"fist_oncoming\": \"👊\",\n  \"facepunch\": \"👊\",\n  \"punch\": \"👊\",\n  \"fist_left\": \"🤛\",\n  \"fist_right\": \"🤜\",\n  \"clap\": \"👏\",\n  \"raised_hands\": \"🙌\",\n  \"open_hands\": \"👐\",\n  \"palms_up_together\": \"🤲\",\n  \"handshake\": \"🤝\",\n  \"pray\": \"🙏\",\n  \"writing_hand\": \"✍️\",\n  \"nail_care\": \"💅\",\n  \"selfie\": \"🤳\",\n  \"muscle\": \"💪\",\n  \"mechanical_arm\": \"🦾\",\n  \"mechanical_leg\": \"🦿\",\n  \"leg\": \"🦵\",\n  \"foot\": \"🦶\",\n  \"ear\": \"👂\",\n  \"ear_with_hearing_aid\": \"🦻\",\n  \"nose\": \"👃\",\n  \"brain\": \"🧠\",\n  \"anatomical_heart\": \"🫀\",\n  \"lungs\": \"🫁\",\n  \"tooth\": \"🦷\",\n  \"bone\": \"🦴\",\n  \"eyes\": \"👀\",\n  \"eye\": \"👁️\",\n  \"tongue\": \"👅\",\n  \"lips\": \"👄\",\n  \"baby\": \"👶\",\n  \"child\": \"🧒\",\n  \"boy\": \"👦\",\n  \"girl\": \"👧\",\n  \"adult\": \"🧑\",\n  \"blond_haired_person\": \"👱\",\n  \"man\": \"👨\",\n  \"bearded_person\": \"🧔\",\n  \"red_haired_man\": \"👨‍🦰\",\n  \"curly_haired_man\": \"👨‍🦱\",\n  \"white_haired_man\": \"👨‍🦳\",\n  \"bald_man\": \"👨‍🦲\",\n  \"woman\": \"👩\",\n  \"red_haired_woman\": \"👩‍🦰\",\n  \"person_red_hair\": \"🧑‍🦰\",\n  \"curly_haired_woman\": \"👩‍🦱\",\n  \"person_curly_hair\": \"🧑‍🦱\",\n  \"white_haired_woman\": \"👩‍🦳\",\n  \"person_white_hair\": \"🧑‍🦳\",\n  \"bald_woman\": \"👩‍🦲\",\n  \"person_bald\": \"🧑‍🦲\",\n  \"blond_haired_woman\": \"👱‍♀️\",\n  \"blonde_woman\": \"👱‍♀️\",\n  \"blond_haired_man\": \"👱‍♂️\",\n  \"older_adult\": \"🧓\",\n  \"older_man\": \"👴\",\n  \"older_woman\": \"👵\",\n  \"frowning_person\": \"🙍\",\n  \"frowning_man\": \"🙍‍♂️\",\n  \"frowning_woman\": \"🙍‍♀️\",\n  \"pouting_face\": \"🙎\",\n  \"pouting_man\": \"🙎‍♂️\",\n  \"pouting_woman\": \"🙎‍♀️\",\n  \"no_good\": \"🙅\",\n  \"no_good_man\": \"🙅‍♂️\",\n  \"ng_man\": \"🙅‍♂️\",\n  \"no_good_woman\": \"🙅‍♀️\",\n  \"ng_woman\": \"🙅‍♀️\",\n  \"ok_person\": \"🙆\",\n  \"ok_man\": \"🙆‍♂️\",\n  \"ok_woman\": \"🙆‍♀️\",\n  \"tipping_hand_person\": \"💁\",\n  \"information_desk_person\": \"💁\",\n  \"tipping_hand_man\": \"💁‍♂️\",\n  \"sassy_man\": \"💁‍♂️\",\n  \"tipping_hand_woman\": \"💁‍♀️\",\n  \"sassy_woman\": \"💁‍♀️\",\n  \"raising_hand\": \"🙋\",\n  \"raising_hand_man\": \"🙋‍♂️\",\n  \"raising_hand_woman\": \"🙋‍♀️\",\n  \"deaf_person\": \"🧏\",\n  \"deaf_man\": \"🧏‍♂️\",\n  \"deaf_woman\": \"🧏‍♀️\",\n  \"bow\": \"🙇\",\n  \"bowing_man\": \"🙇‍♂️\",\n  \"bowing_woman\": \"🙇‍♀️\",\n  \"facepalm\": \"🤦\",\n  \"man_facepalming\": \"🤦‍♂️\",\n  \"woman_facepalming\": \"🤦‍♀️\",\n  \"shrug\": \"🤷\",\n  \"man_shrugging\": \"🤷‍♂️\",\n  \"woman_shrugging\": \"🤷‍♀️\",\n  \"health_worker\": \"🧑‍⚕️\",\n  \"man_health_worker\": \"👨‍⚕️\",\n  \"woman_health_worker\": \"👩‍⚕️\",\n  \"student\": \"🧑‍🎓\",\n  \"man_student\": \"👨‍🎓\",\n  \"woman_student\": \"👩‍🎓\",\n  \"teacher\": \"🧑‍🏫\",\n  \"man_teacher\": \"👨‍🏫\",\n  \"woman_teacher\": \"👩‍🏫\",\n  \"judge\": \"🧑‍⚖️\",\n  \"man_judge\": \"👨‍⚖️\",\n  \"woman_judge\": \"👩‍⚖️\",\n  \"farmer\": \"🧑‍🌾\",\n  \"man_farmer\": \"👨‍🌾\",\n  \"woman_farmer\": \"👩‍🌾\",\n  \"cook\": \"🧑‍🍳\",\n  \"man_cook\": \"👨‍🍳\",\n  \"woman_cook\": \"👩‍🍳\",\n  \"mechanic\": \"🧑‍🔧\",\n  \"man_mechanic\": \"👨‍🔧\",\n  \"woman_mechanic\": \"👩‍🔧\",\n  \"factory_worker\": \"🧑‍🏭\",\n  \"man_factory_worker\": \"👨‍🏭\",\n  \"woman_factory_worker\": \"👩‍🏭\",\n  \"office_worker\": \"🧑‍💼\",\n  \"man_office_worker\": \"👨‍💼\",\n  \"woman_office_worker\": \"👩‍💼\",\n  \"scientist\": \"🧑‍🔬\",\n  \"man_scientist\": \"👨‍🔬\",\n  \"woman_scientist\": \"👩‍🔬\",\n  \"technologist\": \"🧑‍💻\",\n  \"man_technologist\": \"👨‍💻\",\n  \"woman_technologist\": \"👩‍💻\",\n  \"singer\": \"🧑‍🎤\",\n  \"man_singer\": \"👨‍🎤\",\n  \"woman_singer\": \"👩‍🎤\",\n  \"artist\": \"🧑‍🎨\",\n  \"man_artist\": \"👨‍🎨\",\n  \"woman_artist\": \"👩‍🎨\",\n  \"pilot\": \"🧑‍✈️\",\n  \"man_pilot\": \"👨‍✈️\",\n  \"woman_pilot\": \"👩‍✈️\",\n  \"astronaut\": \"🧑‍🚀\",\n  \"man_astronaut\": \"👨‍🚀\",\n  \"woman_astronaut\": \"👩‍🚀\",\n  \"firefighter\": \"🧑‍🚒\",\n  \"man_firefighter\": \"👨‍🚒\",\n  \"woman_firefighter\": \"👩‍🚒\",\n  \"police_officer\": \"👮\",\n  \"cop\": \"👮\",\n  \"policeman\": \"👮‍♂️\",\n  \"policewoman\": \"👮‍♀️\",\n  \"detective\": \"🕵️\",\n  \"male_detective\": \"🕵️‍♂️\",\n  \"female_detective\": \"🕵️‍♀️\",\n  \"guard\": \"💂\",\n  \"guardsman\": \"💂‍♂️\",\n  \"guardswoman\": \"💂‍♀️\",\n  \"ninja\": \"🥷\",\n  \"construction_worker\": \"👷\",\n  \"construction_worker_man\": \"👷‍♂️\",\n  \"construction_worker_woman\": \"👷‍♀️\",\n  \"prince\": \"🤴\",\n  \"princess\": \"👸\",\n  \"person_with_turban\": \"👳\",\n  \"man_with_turban\": \"👳‍♂️\",\n  \"woman_with_turban\": \"👳‍♀️\",\n  \"man_with_gua_pi_mao\": \"👲\",\n  \"woman_with_headscarf\": \"🧕\",\n  \"person_in_tuxedo\": \"🤵\",\n  \"man_in_tuxedo\": \"🤵‍♂️\",\n  \"woman_in_tuxedo\": \"🤵‍♀️\",\n  \"person_with_veil\": \"👰\",\n  \"man_with_veil\": \"👰‍♂️\",\n  \"woman_with_veil\": \"👰‍♀️\",\n  \"bride_with_veil\": \"👰‍♀️\",\n  \"pregnant_woman\": \"🤰\",\n  \"breast_feeding\": \"🤱\",\n  \"woman_feeding_baby\": \"👩‍🍼\",\n  \"man_feeding_baby\": \"👨‍🍼\",\n  \"person_feeding_baby\": \"🧑‍🍼\",\n  \"angel\": \"👼\",\n  \"santa\": \"🎅\",\n  \"mrs_claus\": \"🤶\",\n  \"mx_claus\": \"🧑‍🎄\",\n  \"superhero\": \"🦸\",\n  \"superhero_man\": \"🦸‍♂️\",\n  \"superhero_woman\": \"🦸‍♀️\",\n  \"supervillain\": \"🦹\",\n  \"supervillain_man\": \"🦹‍♂️\",\n  \"supervillain_woman\": \"🦹‍♀️\",\n  \"mage\": \"🧙\",\n  \"mage_man\": \"🧙‍♂️\",\n  \"mage_woman\": \"🧙‍♀️\",\n  \"fairy\": \"🧚\",\n  \"fairy_man\": \"🧚‍♂️\",\n  \"fairy_woman\": \"🧚‍♀️\",\n  \"vampire\": \"🧛\",\n  \"vampire_man\": \"🧛‍♂️\",\n  \"vampire_woman\": \"🧛‍♀️\",\n  \"merperson\": \"🧜\",\n  \"merman\": \"🧜‍♂️\",\n  \"mermaid\": \"🧜‍♀️\",\n  \"elf\": \"🧝\",\n  \"elf_man\": \"🧝‍♂️\",\n  \"elf_woman\": \"🧝‍♀️\",\n  \"genie\": \"🧞\",\n  \"genie_man\": \"🧞‍♂️\",\n  \"genie_woman\": \"🧞‍♀️\",\n  \"zombie\": \"🧟\",\n  \"zombie_man\": \"🧟‍♂️\",\n  \"zombie_woman\": \"🧟‍♀️\",\n  \"massage\": \"💆\",\n  \"massage_man\": \"💆‍♂️\",\n  \"massage_woman\": \"💆‍♀️\",\n  \"haircut\": \"💇\",\n  \"haircut_man\": \"💇‍♂️\",\n  \"haircut_woman\": \"💇‍♀️\",\n  \"walking\": \"🚶\",\n  \"walking_man\": \"🚶‍♂️\",\n  \"walking_woman\": \"🚶‍♀️\",\n  \"standing_person\": \"🧍\",\n  \"standing_man\": \"🧍‍♂️\",\n  \"standing_woman\": \"🧍‍♀️\",\n  \"kneeling_person\": \"🧎\",\n  \"kneeling_man\": \"🧎‍♂️\",\n  \"kneeling_woman\": \"🧎‍♀️\",\n  \"person_with_probing_cane\": \"🧑‍🦯\",\n  \"man_with_probing_cane\": \"👨‍🦯\",\n  \"woman_with_probing_cane\": \"👩‍🦯\",\n  \"person_in_motorized_wheelchair\": \"🧑‍🦼\",\n  \"man_in_motorized_wheelchair\": \"👨‍🦼\",\n  \"woman_in_motorized_wheelchair\": \"👩‍🦼\",\n  \"person_in_manual_wheelchair\": \"🧑‍🦽\",\n  \"man_in_manual_wheelchair\": \"👨‍🦽\",\n  \"woman_in_manual_wheelchair\": \"👩‍🦽\",\n  \"runner\": \"🏃\",\n  \"running\": \"🏃\",\n  \"running_man\": \"🏃‍♂️\",\n  \"running_woman\": \"🏃‍♀️\",\n  \"woman_dancing\": \"💃\",\n  \"dancer\": \"💃\",\n  \"man_dancing\": \"🕺\",\n  \"business_suit_levitating\": \"🕴️\",\n  \"dancers\": \"👯\",\n  \"dancing_men\": \"👯‍♂️\",\n  \"dancing_women\": \"👯‍♀️\",\n  \"sauna_person\": \"🧖\",\n  \"sauna_man\": \"🧖‍♂️\",\n  \"sauna_woman\": \"🧖‍♀️\",\n  \"climbing\": \"🧗\",\n  \"climbing_man\": \"🧗‍♂️\",\n  \"climbing_woman\": \"🧗‍♀️\",\n  \"person_fencing\": \"🤺\",\n  \"horse_racing\": \"🏇\",\n  \"skier\": \"⛷️\",\n  \"snowboarder\": \"🏂\",\n  \"golfing\": \"🏌️\",\n  \"golfing_man\": \"🏌️‍♂️\",\n  \"golfing_woman\": \"🏌️‍♀️\",\n  \"surfer\": \"🏄\",\n  \"surfing_man\": \"🏄‍♂️\",\n  \"surfing_woman\": \"🏄‍♀️\",\n  \"rowboat\": \"🚣\",\n  \"rowing_man\": \"🚣‍♂️\",\n  \"rowing_woman\": \"🚣‍♀️\",\n  \"swimmer\": \"🏊\",\n  \"swimming_man\": \"🏊‍♂️\",\n  \"swimming_woman\": \"🏊‍♀️\",\n  \"bouncing_ball_person\": \"⛹️\",\n  \"bouncing_ball_man\": \"⛹️‍♂️\",\n  \"basketball_man\": \"⛹️‍♂️\",\n  \"bouncing_ball_woman\": \"⛹️‍♀️\",\n  \"basketball_woman\": \"⛹️‍♀️\",\n  \"weight_lifting\": \"🏋️\",\n  \"weight_lifting_man\": \"🏋️‍♂️\",\n  \"weight_lifting_woman\": \"🏋️‍♀️\",\n  \"bicyclist\": \"🚴\",\n  \"biking_man\": \"🚴‍♂️\",\n  \"biking_woman\": \"🚴‍♀️\",\n  \"mountain_bicyclist\": \"🚵\",\n  \"mountain_biking_man\": \"🚵‍♂️\",\n  \"mountain_biking_woman\": \"🚵‍♀️\",\n  \"cartwheeling\": \"🤸\",\n  \"man_cartwheeling\": \"🤸‍♂️\",\n  \"woman_cartwheeling\": \"🤸‍♀️\",\n  \"wrestling\": \"🤼\",\n  \"men_wrestling\": \"🤼‍♂️\",\n  \"women_wrestling\": \"🤼‍♀️\",\n  \"water_polo\": \"🤽\",\n  \"man_playing_water_polo\": \"🤽‍♂️\",\n  \"woman_playing_water_polo\": \"🤽‍♀️\",\n  \"handball_person\": \"🤾\",\n  \"man_playing_handball\": \"🤾‍♂️\",\n  \"woman_playing_handball\": \"🤾‍♀️\",\n  \"juggling_person\": \"🤹\",\n  \"man_juggling\": \"🤹‍♂️\",\n  \"woman_juggling\": \"🤹‍♀️\",\n  \"lotus_position\": \"🧘\",\n  \"lotus_position_man\": \"🧘‍♂️\",\n  \"lotus_position_woman\": \"🧘‍♀️\",\n  \"bath\": \"🛀\",\n  \"sleeping_bed\": \"🛌\",\n  \"people_holding_hands\": \"🧑‍🤝‍🧑\",\n  \"two_women_holding_hands\": \"👭\",\n  \"couple\": \"👫\",\n  \"two_men_holding_hands\": \"👬\",\n  \"couplekiss\": \"💏\",\n  \"couplekiss_man_woman\": \"👩‍❤️‍💋‍👨\",\n  \"couplekiss_man_man\": \"👨‍❤️‍💋‍👨\",\n  \"couplekiss_woman_woman\": \"👩‍❤️‍💋‍👩\",\n  \"couple_with_heart\": \"💑\",\n  \"couple_with_heart_woman_man\": \"👩‍❤️‍👨\",\n  \"couple_with_heart_man_man\": \"👨‍❤️‍👨\",\n  \"couple_with_heart_woman_woman\": \"👩‍❤️‍👩\",\n  \"family\": \"👪\",\n  \"family_man_woman_boy\": \"👨‍👩‍👦\",\n  \"family_man_woman_girl\": \"👨‍👩‍👧\",\n  \"family_man_woman_girl_boy\": \"👨‍👩‍👧‍👦\",\n  \"family_man_woman_boy_boy\": \"👨‍👩‍👦‍👦\",\n  \"family_man_woman_girl_girl\": \"👨‍👩‍👧‍👧\",\n  \"family_man_man_boy\": \"👨‍👨‍👦\",\n  \"family_man_man_girl\": \"👨‍👨‍👧\",\n  \"family_man_man_girl_boy\": \"👨‍👨‍👧‍👦\",\n  \"family_man_man_boy_boy\": \"👨‍👨‍👦‍👦\",\n  \"family_man_man_girl_girl\": \"👨‍👨‍👧‍👧\",\n  \"family_woman_woman_boy\": \"👩‍👩‍👦\",\n  \"family_woman_woman_girl\": \"👩‍👩‍👧\",\n  \"family_woman_woman_girl_boy\": \"👩‍👩‍👧‍👦\",\n  \"family_woman_woman_boy_boy\": \"👩‍👩‍👦‍👦\",\n  \"family_woman_woman_girl_girl\": \"👩‍👩‍👧‍👧\",\n  \"family_man_boy\": \"👨‍👦\",\n  \"family_man_boy_boy\": \"👨‍👦‍👦\",\n  \"family_man_girl\": \"👨‍👧\",\n  \"family_man_girl_boy\": \"👨‍👧‍👦\",\n  \"family_man_girl_girl\": \"👨‍👧‍👧\",\n  \"family_woman_boy\": \"👩‍👦\",\n  \"family_woman_boy_boy\": \"👩‍👦‍👦\",\n  \"family_woman_girl\": \"👩‍👧\",\n  \"family_woman_girl_boy\": \"👩‍👧‍👦\",\n  \"family_woman_girl_girl\": \"👩‍👧‍👧\",\n  \"speaking_head\": \"🗣️\",\n  \"bust_in_silhouette\": \"👤\",\n  \"busts_in_silhouette\": \"👥\",\n  \"people_hugging\": \"🫂\",\n  \"footprints\": \"👣\",\n  \"monkey_face\": \"🐵\",\n  \"monkey\": \"🐒\",\n  \"gorilla\": \"🦍\",\n  \"orangutan\": \"🦧\",\n  \"dog\": \"🐶\",\n  \"dog2\": \"🐕\",\n  \"guide_dog\": \"🦮\",\n  \"service_dog\": \"🐕‍🦺\",\n  \"poodle\": \"🐩\",\n  \"wolf\": \"🐺\",\n  \"fox_face\": \"🦊\",\n  \"raccoon\": \"🦝\",\n  \"cat\": \"🐱\",\n  \"cat2\": \"🐈\",\n  \"black_cat\": \"🐈‍⬛\",\n  \"lion\": \"🦁\",\n  \"tiger\": \"🐯\",\n  \"tiger2\": \"🐅\",\n  \"leopard\": \"🐆\",\n  \"horse\": \"🐴\",\n  \"racehorse\": \"🐎\",\n  \"unicorn\": \"🦄\",\n  \"zebra\": \"🦓\",\n  \"deer\": \"🦌\",\n  \"bison\": \"🦬\",\n  \"cow\": \"🐮\",\n  \"ox\": \"🐂\",\n  \"water_buffalo\": \"🐃\",\n  \"cow2\": \"🐄\",\n  \"pig\": \"🐷\",\n  \"pig2\": \"🐖\",\n  \"boar\": \"🐗\",\n  \"pig_nose\": \"🐽\",\n  \"ram\": \"🐏\",\n  \"sheep\": \"🐑\",\n  \"goat\": \"🐐\",\n  \"dromedary_camel\": \"🐪\",\n  \"camel\": \"🐫\",\n  \"llama\": \"🦙\",\n  \"giraffe\": \"🦒\",\n  \"elephant\": \"🐘\",\n  \"mammoth\": \"🦣\",\n  \"rhinoceros\": \"🦏\",\n  \"hippopotamus\": \"🦛\",\n  \"mouse\": \"🐭\",\n  \"mouse2\": \"🐁\",\n  \"rat\": \"🐀\",\n  \"hamster\": \"🐹\",\n  \"rabbit\": \"🐰\",\n  \"rabbit2\": \"🐇\",\n  \"chipmunk\": \"🐿️\",\n  \"beaver\": \"🦫\",\n  \"hedgehog\": \"🦔\",\n  \"bat\": \"🦇\",\n  \"bear\": \"🐻\",\n  \"polar_bear\": \"🐻‍❄️\",\n  \"koala\": \"🐨\",\n  \"panda_face\": \"🐼\",\n  \"sloth\": \"🦥\",\n  \"otter\": \"🦦\",\n  \"skunk\": \"🦨\",\n  \"kangaroo\": \"🦘\",\n  \"badger\": \"🦡\",\n  \"feet\": \"🐾\",\n  \"paw_prints\": \"🐾\",\n  \"turkey\": \"🦃\",\n  \"chicken\": \"🐔\",\n  \"rooster\": \"🐓\",\n  \"hatching_chick\": \"🐣\",\n  \"baby_chick\": \"🐤\",\n  \"hatched_chick\": \"🐥\",\n  \"bird\": \"🐦\",\n  \"penguin\": \"🐧\",\n  \"dove\": \"🕊️\",\n  \"eagle\": \"🦅\",\n  \"duck\": \"🦆\",\n  \"swan\": \"🦢\",\n  \"owl\": \"🦉\",\n  \"dodo\": \"🦤\",\n  \"feather\": \"🪶\",\n  \"flamingo\": \"🦩\",\n  \"peacock\": \"🦚\",\n  \"parrot\": \"🦜\",\n  \"frog\": \"🐸\",\n  \"crocodile\": \"🐊\",\n  \"turtle\": \"🐢\",\n  \"lizard\": \"🦎\",\n  \"snake\": \"🐍\",\n  \"dragon_face\": \"🐲\",\n  \"dragon\": \"🐉\",\n  \"sauropod\": \"🦕\",\n  \"t-rex\": \"🦖\",\n  \"whale\": \"🐳\",\n  \"whale2\": \"🐋\",\n  \"dolphin\": \"🐬\",\n  \"flipper\": \"🐬\",\n  \"seal\": \"🦭\",\n  \"fish\": \"🐟\",\n  \"tropical_fish\": \"🐠\",\n  \"blowfish\": \"🐡\",\n  \"shark\": \"🦈\",\n  \"octopus\": \"🐙\",\n  \"shell\": \"🐚\",\n  \"snail\": \"🐌\",\n  \"butterfly\": \"🦋\",\n  \"bug\": \"🐛\",\n  \"ant\": \"🐜\",\n  \"bee\": \"🐝\",\n  \"honeybee\": \"🐝\",\n  \"beetle\": \"🪲\",\n  \"lady_beetle\": \"🐞\",\n  \"cricket\": \"🦗\",\n  \"cockroach\": \"🪳\",\n  \"spider\": \"🕷️\",\n  \"spider_web\": \"🕸️\",\n  \"scorpion\": \"🦂\",\n  \"mosquito\": \"🦟\",\n  \"fly\": \"🪰\",\n  \"worm\": \"🪱\",\n  \"microbe\": \"🦠\",\n  \"bouquet\": \"💐\",\n  \"cherry_blossom\": \"🌸\",\n  \"white_flower\": \"💮\",\n  \"rosette\": \"🏵️\",\n  \"rose\": \"🌹\",\n  \"wilted_flower\": \"🥀\",\n  \"hibiscus\": \"🌺\",\n  \"sunflower\": \"🌻\",\n  \"blossom\": \"🌼\",\n  \"tulip\": \"🌷\",\n  \"seedling\": \"🌱\",\n  \"potted_plant\": \"🪴\",\n  \"evergreen_tree\": \"🌲\",\n  \"deciduous_tree\": \"🌳\",\n  \"palm_tree\": \"🌴\",\n  \"cactus\": \"🌵\",\n  \"ear_of_rice\": \"🌾\",\n  \"herb\": \"🌿\",\n  \"shamrock\": \"☘️\",\n  \"four_leaf_clover\": \"🍀\",\n  \"maple_leaf\": \"🍁\",\n  \"fallen_leaf\": \"🍂\",\n  \"leaves\": \"🍃\",\n  \"grapes\": \"🍇\",\n  \"melon\": \"🍈\",\n  \"watermelon\": \"🍉\",\n  \"tangerine\": \"🍊\",\n  \"orange\": \"🍊\",\n  \"mandarin\": \"🍊\",\n  \"lemon\": \"🍋\",\n  \"banana\": \"🍌\",\n  \"pineapple\": \"🍍\",\n  \"mango\": \"🥭\",\n  \"apple\": \"🍎\",\n  \"green_apple\": \"🍏\",\n  \"pear\": \"🍐\",\n  \"peach\": \"🍑\",\n  \"cherries\": \"🍒\",\n  \"strawberry\": \"🍓\",\n  \"blueberries\": \"🫐\",\n  \"kiwi_fruit\": \"🥝\",\n  \"tomato\": \"🍅\",\n  \"olive\": \"🫒\",\n  \"coconut\": \"🥥\",\n  \"avocado\": \"🥑\",\n  \"eggplant\": \"🍆\",\n  \"potato\": \"🥔\",\n  \"carrot\": \"🥕\",\n  \"corn\": \"🌽\",\n  \"hot_pepper\": \"🌶️\",\n  \"bell_pepper\": \"🫑\",\n  \"cucumber\": \"🥒\",\n  \"leafy_green\": \"🥬\",\n  \"broccoli\": \"🥦\",\n  \"garlic\": \"🧄\",\n  \"onion\": \"🧅\",\n  \"mushroom\": \"🍄\",\n  \"peanuts\": \"🥜\",\n  \"chestnut\": \"🌰\",\n  \"bread\": \"🍞\",\n  \"croissant\": \"🥐\",\n  \"baguette_bread\": \"🥖\",\n  \"flatbread\": \"🫓\",\n  \"pretzel\": \"🥨\",\n  \"bagel\": \"🥯\",\n  \"pancakes\": \"🥞\",\n  \"waffle\": \"🧇\",\n  \"cheese\": \"🧀\",\n  \"meat_on_bone\": \"🍖\",\n  \"poultry_leg\": \"🍗\",\n  \"cut_of_meat\": \"🥩\",\n  \"bacon\": \"🥓\",\n  \"hamburger\": \"🍔\",\n  \"fries\": \"🍟\",\n  \"pizza\": \"🍕\",\n  \"hotdog\": \"🌭\",\n  \"sandwich\": \"🥪\",\n  \"taco\": \"🌮\",\n  \"burrito\": \"🌯\",\n  \"tamale\": \"🫔\",\n  \"stuffed_flatbread\": \"🥙\",\n  \"falafel\": \"🧆\",\n  \"egg\": \"🥚\",\n  \"fried_egg\": \"🍳\",\n  \"shallow_pan_of_food\": \"🥘\",\n  \"stew\": \"🍲\",\n  \"fondue\": \"🫕\",\n  \"bowl_with_spoon\": \"🥣\",\n  \"green_salad\": \"🥗\",\n  \"popcorn\": \"🍿\",\n  \"butter\": \"🧈\",\n  \"salt\": \"🧂\",\n  \"canned_food\": \"🥫\",\n  \"bento\": \"🍱\",\n  \"rice_cracker\": \"🍘\",\n  \"rice_ball\": \"🍙\",\n  \"rice\": \"🍚\",\n  \"curry\": \"🍛\",\n  \"ramen\": \"🍜\",\n  \"spaghetti\": \"🍝\",\n  \"sweet_potato\": \"🍠\",\n  \"oden\": \"🍢\",\n  \"sushi\": \"🍣\",\n  \"fried_shrimp\": \"🍤\",\n  \"fish_cake\": \"🍥\",\n  \"moon_cake\": \"🥮\",\n  \"dango\": \"🍡\",\n  \"dumpling\": \"🥟\",\n  \"fortune_cookie\": \"🥠\",\n  \"takeout_box\": \"🥡\",\n  \"crab\": \"🦀\",\n  \"lobster\": \"🦞\",\n  \"shrimp\": \"🦐\",\n  \"squid\": \"🦑\",\n  \"oyster\": \"🦪\",\n  \"icecream\": \"🍦\",\n  \"shaved_ice\": \"🍧\",\n  \"ice_cream\": \"🍨\",\n  \"doughnut\": \"🍩\",\n  \"cookie\": \"🍪\",\n  \"birthday\": \"🎂\",\n  \"cake\": \"🍰\",\n  \"cupcake\": \"🧁\",\n  \"pie\": \"🥧\",\n  \"chocolate_bar\": \"🍫\",\n  \"candy\": \"🍬\",\n  \"lollipop\": \"🍭\",\n  \"custard\": \"🍮\",\n  \"honey_pot\": \"🍯\",\n  \"baby_bottle\": \"🍼\",\n  \"milk_glass\": \"🥛\",\n  \"coffee\": \"☕\",\n  \"teapot\": \"🫖\",\n  \"tea\": \"🍵\",\n  \"sake\": \"🍶\",\n  \"champagne\": \"🍾\",\n  \"wine_glass\": \"🍷\",\n  \"cocktail\": \"🍸\",\n  \"tropical_drink\": \"🍹\",\n  \"beer\": \"🍺\",\n  \"beers\": \"🍻\",\n  \"clinking_glasses\": \"🥂\",\n  \"tumbler_glass\": \"🥃\",\n  \"cup_with_straw\": \"🥤\",\n  \"bubble_tea\": \"🧋\",\n  \"beverage_box\": \"🧃\",\n  \"mate\": \"🧉\",\n  \"ice_cube\": \"🧊\",\n  \"chopsticks\": \"🥢\",\n  \"plate_with_cutlery\": \"🍽️\",\n  \"fork_and_knife\": \"🍴\",\n  \"spoon\": \"🥄\",\n  \"hocho\": \"🔪\",\n  \"knife\": \"🔪\",\n  \"amphora\": \"🏺\",\n  \"earth_africa\": \"🌍\",\n  \"earth_americas\": \"🌎\",\n  \"earth_asia\": \"🌏\",\n  \"globe_with_meridians\": \"🌐\",\n  \"world_map\": \"🗺️\",\n  \"japan\": \"🗾\",\n  \"compass\": \"🧭\",\n  \"mountain_snow\": \"🏔️\",\n  \"mountain\": \"⛰️\",\n  \"volcano\": \"🌋\",\n  \"mount_fuji\": \"🗻\",\n  \"camping\": \"🏕️\",\n  \"beach_umbrella\": \"🏖️\",\n  \"desert\": \"🏜️\",\n  \"desert_island\": \"🏝️\",\n  \"national_park\": \"🏞️\",\n  \"stadium\": \"🏟️\",\n  \"classical_building\": \"🏛️\",\n  \"building_construction\": \"🏗️\",\n  \"bricks\": \"🧱\",\n  \"rock\": \"🪨\",\n  \"wood\": \"🪵\",\n  \"hut\": \"🛖\",\n  \"houses\": \"🏘️\",\n  \"derelict_house\": \"🏚️\",\n  \"house\": \"🏠\",\n  \"house_with_garden\": \"🏡\",\n  \"office\": \"🏢\",\n  \"post_office\": \"🏣\",\n  \"european_post_office\": \"🏤\",\n  \"hospital\": \"🏥\",\n  \"bank\": \"🏦\",\n  \"hotel\": \"🏨\",\n  \"love_hotel\": \"🏩\",\n  \"convenience_store\": \"🏪\",\n  \"school\": \"🏫\",\n  \"department_store\": \"🏬\",\n  \"factory\": \"🏭\",\n  \"japanese_castle\": \"🏯\",\n  \"european_castle\": \"🏰\",\n  \"wedding\": \"💒\",\n  \"tokyo_tower\": \"🗼\",\n  \"statue_of_liberty\": \"🗽\",\n  \"church\": \"⛪\",\n  \"mosque\": \"🕌\",\n  \"hindu_temple\": \"🛕\",\n  \"synagogue\": \"🕍\",\n  \"shinto_shrine\": \"⛩️\",\n  \"kaaba\": \"🕋\",\n  \"fountain\": \"⛲\",\n  \"tent\": \"⛺\",\n  \"foggy\": \"🌁\",\n  \"night_with_stars\": \"🌃\",\n  \"cityscape\": \"🏙️\",\n  \"sunrise_over_mountains\": \"🌄\",\n  \"sunrise\": \"🌅\",\n  \"city_sunset\": \"🌆\",\n  \"city_sunrise\": \"🌇\",\n  \"bridge_at_night\": \"🌉\",\n  \"hotsprings\": \"♨️\",\n  \"carousel_horse\": \"🎠\",\n  \"ferris_wheel\": \"🎡\",\n  \"roller_coaster\": \"🎢\",\n  \"barber\": \"💈\",\n  \"circus_tent\": \"🎪\",\n  \"steam_locomotive\": \"🚂\",\n  \"railway_car\": \"🚃\",\n  \"bullettrain_side\": \"🚄\",\n  \"bullettrain_front\": \"🚅\",\n  \"train2\": \"🚆\",\n  \"metro\": \"🚇\",\n  \"light_rail\": \"🚈\",\n  \"station\": \"🚉\",\n  \"tram\": \"🚊\",\n  \"monorail\": \"🚝\",\n  \"mountain_railway\": \"🚞\",\n  \"train\": \"🚋\",\n  \"bus\": \"🚌\",\n  \"oncoming_bus\": \"🚍\",\n  \"trolleybus\": \"🚎\",\n  \"minibus\": \"🚐\",\n  \"ambulance\": \"🚑\",\n  \"fire_engine\": \"🚒\",\n  \"police_car\": \"🚓\",\n  \"oncoming_police_car\": \"🚔\",\n  \"taxi\": \"🚕\",\n  \"oncoming_taxi\": \"🚖\",\n  \"car\": \"🚗\",\n  \"red_car\": \"🚗\",\n  \"oncoming_automobile\": \"🚘\",\n  \"blue_car\": \"🚙\",\n  \"pickup_truck\": \"🛻\",\n  \"truck\": \"🚚\",\n  \"articulated_lorry\": \"🚛\",\n  \"tractor\": \"🚜\",\n  \"racing_car\": \"🏎️\",\n  \"motorcycle\": \"🏍️\",\n  \"motor_scooter\": \"🛵\",\n  \"manual_wheelchair\": \"🦽\",\n  \"motorized_wheelchair\": \"🦼\",\n  \"auto_rickshaw\": \"🛺\",\n  \"bike\": \"🚲\",\n  \"kick_scooter\": \"🛴\",\n  \"skateboard\": \"🛹\",\n  \"roller_skate\": \"🛼\",\n  \"busstop\": \"🚏\",\n  \"motorway\": \"🛣️\",\n  \"railway_track\": \"🛤️\",\n  \"oil_drum\": \"🛢️\",\n  \"fuelpump\": \"⛽\",\n  \"rotating_light\": \"🚨\",\n  \"traffic_light\": \"🚥\",\n  \"vertical_traffic_light\": \"🚦\",\n  \"stop_sign\": \"🛑\",\n  \"construction\": \"🚧\",\n  \"anchor\": \"⚓\",\n  \"boat\": \"⛵\",\n  \"sailboat\": \"⛵\",\n  \"canoe\": \"🛶\",\n  \"speedboat\": \"🚤\",\n  \"passenger_ship\": \"🛳️\",\n  \"ferry\": \"⛴️\",\n  \"motor_boat\": \"🛥️\",\n  \"ship\": \"🚢\",\n  \"airplane\": \"✈️\",\n  \"small_airplane\": \"🛩️\",\n  \"flight_departure\": \"🛫\",\n  \"flight_arrival\": \"🛬\",\n  \"parachute\": \"🪂\",\n  \"seat\": \"💺\",\n  \"helicopter\": \"🚁\",\n  \"suspension_railway\": \"🚟\",\n  \"mountain_cableway\": \"🚠\",\n  \"aerial_tramway\": \"🚡\",\n  \"artificial_satellite\": \"🛰️\",\n  \"rocket\": \"🚀\",\n  \"flying_saucer\": \"🛸\",\n  \"bellhop_bell\": \"🛎️\",\n  \"luggage\": \"🧳\",\n  \"hourglass\": \"⌛\",\n  \"hourglass_flowing_sand\": \"⏳\",\n  \"watch\": \"⌚\",\n  \"alarm_clock\": \"⏰\",\n  \"stopwatch\": \"⏱️\",\n  \"timer_clock\": \"⏲️\",\n  \"mantelpiece_clock\": \"🕰️\",\n  \"clock12\": \"🕛\",\n  \"clock1230\": \"🕧\",\n  \"clock1\": \"🕐\",\n  \"clock130\": \"🕜\",\n  \"clock2\": \"🕑\",\n  \"clock230\": \"🕝\",\n  \"clock3\": \"🕒\",\n  \"clock330\": \"🕞\",\n  \"clock4\": \"🕓\",\n  \"clock430\": \"🕟\",\n  \"clock5\": \"🕔\",\n  \"clock530\": \"🕠\",\n  \"clock6\": \"🕕\",\n  \"clock630\": \"🕡\",\n  \"clock7\": \"🕖\",\n  \"clock730\": \"🕢\",\n  \"clock8\": \"🕗\",\n  \"clock830\": \"🕣\",\n  \"clock9\": \"🕘\",\n  \"clock930\": \"🕤\",\n  \"clock10\": \"🕙\",\n  \"clock1030\": \"🕥\",\n  \"clock11\": \"🕚\",\n  \"clock1130\": \"🕦\",\n  \"new_moon\": \"🌑\",\n  \"waxing_crescent_moon\": \"🌒\",\n  \"first_quarter_moon\": \"🌓\",\n  \"moon\": \"🌔\",\n  \"waxing_gibbous_moon\": \"🌔\",\n  \"full_moon\": \"🌕\",\n  \"waning_gibbous_moon\": \"🌖\",\n  \"last_quarter_moon\": \"🌗\",\n  \"waning_crescent_moon\": \"🌘\",\n  \"crescent_moon\": \"🌙\",\n  \"new_moon_with_face\": \"🌚\",\n  \"first_quarter_moon_with_face\": \"🌛\",\n  \"last_quarter_moon_with_face\": \"🌜\",\n  \"thermometer\": \"🌡️\",\n  \"sunny\": \"☀️\",\n  \"full_moon_with_face\": \"🌝\",\n  \"sun_with_face\": \"🌞\",\n  \"ringed_planet\": \"🪐\",\n  \"star\": \"⭐\",\n  \"star2\": \"🌟\",\n  \"stars\": \"🌠\",\n  \"milky_way\": \"🌌\",\n  \"cloud\": \"☁️\",\n  \"partly_sunny\": \"⛅\",\n  \"cloud_with_lightning_and_rain\": \"⛈️\",\n  \"sun_behind_small_cloud\": \"🌤️\",\n  \"sun_behind_large_cloud\": \"🌥️\",\n  \"sun_behind_rain_cloud\": \"🌦️\",\n  \"cloud_with_rain\": \"🌧️\",\n  \"cloud_with_snow\": \"🌨️\",\n  \"cloud_with_lightning\": \"🌩️\",\n  \"tornado\": \"🌪️\",\n  \"fog\": \"🌫️\",\n  \"wind_face\": \"🌬️\",\n  \"cyclone\": \"🌀\",\n  \"rainbow\": \"🌈\",\n  \"closed_umbrella\": \"🌂\",\n  \"open_umbrella\": \"☂️\",\n  \"umbrella\": \"☔\",\n  \"parasol_on_ground\": \"⛱️\",\n  \"zap\": \"⚡\",\n  \"snowflake\": \"❄️\",\n  \"snowman_with_snow\": \"☃️\",\n  \"snowman\": \"⛄\",\n  \"comet\": \"☄️\",\n  \"fire\": \"🔥\",\n  \"droplet\": \"💧\",\n  \"ocean\": \"🌊\",\n  \"jack_o_lantern\": \"🎃\",\n  \"christmas_tree\": \"🎄\",\n  \"fireworks\": \"🎆\",\n  \"sparkler\": \"🎇\",\n  \"firecracker\": \"🧨\",\n  \"sparkles\": \"✨\",\n  \"balloon\": \"🎈\",\n  \"tada\": \"🎉\",\n  \"confetti_ball\": \"🎊\",\n  \"tanabata_tree\": \"🎋\",\n  \"bamboo\": \"🎍\",\n  \"dolls\": \"🎎\",\n  \"flags\": \"🎏\",\n  \"wind_chime\": \"🎐\",\n  \"rice_scene\": \"🎑\",\n  \"red_envelope\": \"🧧\",\n  \"ribbon\": \"🎀\",\n  \"gift\": \"🎁\",\n  \"reminder_ribbon\": \"🎗️\",\n  \"tickets\": \"🎟️\",\n  \"ticket\": \"🎫\",\n  \"medal_military\": \"🎖️\",\n  \"trophy\": \"🏆\",\n  \"medal_sports\": \"🏅\",\n  \"1st_place_medal\": \"🥇\",\n  \"2nd_place_medal\": \"🥈\",\n  \"3rd_place_medal\": \"🥉\",\n  \"soccer\": \"⚽\",\n  \"baseball\": \"⚾\",\n  \"softball\": \"🥎\",\n  \"basketball\": \"🏀\",\n  \"volleyball\": \"🏐\",\n  \"football\": \"🏈\",\n  \"rugby_football\": \"🏉\",\n  \"tennis\": \"🎾\",\n  \"flying_disc\": \"🥏\",\n  \"bowling\": \"🎳\",\n  \"cricket_game\": \"🏏\",\n  \"field_hockey\": \"🏑\",\n  \"ice_hockey\": \"🏒\",\n  \"lacrosse\": \"🥍\",\n  \"ping_pong\": \"🏓\",\n  \"badminton\": \"🏸\",\n  \"boxing_glove\": \"🥊\",\n  \"martial_arts_uniform\": \"🥋\",\n  \"goal_net\": \"🥅\",\n  \"golf\": \"⛳\",\n  \"ice_skate\": \"⛸️\",\n  \"fishing_pole_and_fish\": \"🎣\",\n  \"diving_mask\": \"🤿\",\n  \"running_shirt_with_sash\": \"🎽\",\n  \"ski\": \"🎿\",\n  \"sled\": \"🛷\",\n  \"curling_stone\": \"🥌\",\n  \"dart\": \"🎯\",\n  \"yo_yo\": \"🪀\",\n  \"kite\": \"🪁\",\n  \"8ball\": \"🎱\",\n  \"crystal_ball\": \"🔮\",\n  \"magic_wand\": \"🪄\",\n  \"nazar_amulet\": \"🧿\",\n  \"video_game\": \"🎮\",\n  \"joystick\": \"🕹️\",\n  \"slot_machine\": \"🎰\",\n  \"game_die\": \"🎲\",\n  \"jigsaw\": \"🧩\",\n  \"teddy_bear\": \"🧸\",\n  \"pinata\": \"🪅\",\n  \"nesting_dolls\": \"🪆\",\n  \"spades\": \"♠️\",\n  \"hearts\": \"♥️\",\n  \"diamonds\": \"♦️\",\n  \"clubs\": \"♣️\",\n  \"chess_pawn\": \"♟️\",\n  \"black_joker\": \"🃏\",\n  \"mahjong\": \"🀄\",\n  \"flower_playing_cards\": \"🎴\",\n  \"performing_arts\": \"🎭\",\n  \"framed_picture\": \"🖼️\",\n  \"art\": \"🎨\",\n  \"thread\": \"🧵\",\n  \"sewing_needle\": \"🪡\",\n  \"yarn\": \"🧶\",\n  \"knot\": \"🪢\",\n  \"eyeglasses\": \"👓\",\n  \"dark_sunglasses\": \"🕶️\",\n  \"goggles\": \"🥽\",\n  \"lab_coat\": \"🥼\",\n  \"safety_vest\": \"🦺\",\n  \"necktie\": \"👔\",\n  \"shirt\": \"👕\",\n  \"tshirt\": \"👕\",\n  \"jeans\": \"👖\",\n  \"scarf\": \"🧣\",\n  \"gloves\": \"🧤\",\n  \"coat\": \"🧥\",\n  \"socks\": \"🧦\",\n  \"dress\": \"👗\",\n  \"kimono\": \"👘\",\n  \"sari\": \"🥻\",\n  \"one_piece_swimsuit\": \"🩱\",\n  \"swim_brief\": \"🩲\",\n  \"shorts\": \"🩳\",\n  \"bikini\": \"👙\",\n  \"womans_clothes\": \"👚\",\n  \"purse\": \"👛\",\n  \"handbag\": \"👜\",\n  \"pouch\": \"👝\",\n  \"shopping\": \"🛍️\",\n  \"school_satchel\": \"🎒\",\n  \"thong_sandal\": \"🩴\",\n  \"mans_shoe\": \"👞\",\n  \"shoe\": \"👞\",\n  \"athletic_shoe\": \"👟\",\n  \"hiking_boot\": \"🥾\",\n  \"flat_shoe\": \"🥿\",\n  \"high_heel\": \"👠\",\n  \"sandal\": \"👡\",\n  \"ballet_shoes\": \"🩰\",\n  \"boot\": \"👢\",\n  \"crown\": \"👑\",\n  \"womans_hat\": \"👒\",\n  \"tophat\": \"🎩\",\n  \"mortar_board\": \"🎓\",\n  \"billed_cap\": \"🧢\",\n  \"military_helmet\": \"🪖\",\n  \"rescue_worker_helmet\": \"⛑️\",\n  \"prayer_beads\": \"📿\",\n  \"lipstick\": \"💄\",\n  \"ring\": \"💍\",\n  \"gem\": \"💎\",\n  \"mute\": \"🔇\",\n  \"speaker\": \"🔈\",\n  \"sound\": \"🔉\",\n  \"loud_sound\": \"🔊\",\n  \"loudspeaker\": \"📢\",\n  \"mega\": \"📣\",\n  \"postal_horn\": \"📯\",\n  \"bell\": \"🔔\",\n  \"no_bell\": \"🔕\",\n  \"musical_score\": \"🎼\",\n  \"musical_note\": \"🎵\",\n  \"notes\": \"🎶\",\n  \"studio_microphone\": \"🎙️\",\n  \"level_slider\": \"🎚️\",\n  \"control_knobs\": \"🎛️\",\n  \"microphone\": \"🎤\",\n  \"headphones\": \"🎧\",\n  \"radio\": \"📻\",\n  \"saxophone\": \"🎷\",\n  \"accordion\": \"🪗\",\n  \"guitar\": \"🎸\",\n  \"musical_keyboard\": \"🎹\",\n  \"trumpet\": \"🎺\",\n  \"violin\": \"🎻\",\n  \"banjo\": \"🪕\",\n  \"drum\": \"🥁\",\n  \"long_drum\": \"🪘\",\n  \"iphone\": \"📱\",\n  \"calling\": \"📲\",\n  \"phone\": \"☎️\",\n  \"telephone\": \"☎️\",\n  \"telephone_receiver\": \"📞\",\n  \"pager\": \"📟\",\n  \"fax\": \"📠\",\n  \"battery\": \"🔋\",\n  \"electric_plug\": \"🔌\",\n  \"computer\": \"💻\",\n  \"desktop_computer\": \"🖥️\",\n  \"printer\": \"🖨️\",\n  \"keyboard\": \"⌨️\",\n  \"computer_mouse\": \"🖱️\",\n  \"trackball\": \"🖲️\",\n  \"minidisc\": \"💽\",\n  \"floppy_disk\": \"💾\",\n  \"cd\": \"💿\",\n  \"dvd\": \"📀\",\n  \"abacus\": \"🧮\",\n  \"movie_camera\": \"🎥\",\n  \"film_strip\": \"🎞️\",\n  \"film_projector\": \"📽️\",\n  \"clapper\": \"🎬\",\n  \"tv\": \"📺\",\n  \"camera\": \"📷\",\n  \"camera_flash\": \"📸\",\n  \"video_camera\": \"📹\",\n  \"vhs\": \"📼\",\n  \"mag\": \"🔍\",\n  \"mag_right\": \"🔎\",\n  \"candle\": \"🕯️\",\n  \"bulb\": \"💡\",\n  \"flashlight\": \"🔦\",\n  \"izakaya_lantern\": \"🏮\",\n  \"lantern\": \"🏮\",\n  \"diya_lamp\": \"🪔\",\n  \"notebook_with_decorative_cover\": \"📔\",\n  \"closed_book\": \"📕\",\n  \"book\": \"📖\",\n  \"open_book\": \"📖\",\n  \"green_book\": \"📗\",\n  \"blue_book\": \"📘\",\n  \"orange_book\": \"📙\",\n  \"books\": \"📚\",\n  \"notebook\": \"📓\",\n  \"ledger\": \"📒\",\n  \"page_with_curl\": \"📃\",\n  \"scroll\": \"📜\",\n  \"page_facing_up\": \"📄\",\n  \"newspaper\": \"📰\",\n  \"newspaper_roll\": \"🗞️\",\n  \"bookmark_tabs\": \"📑\",\n  \"bookmark\": \"🔖\",\n  \"label\": \"🏷️\",\n  \"moneybag\": \"💰\",\n  \"coin\": \"🪙\",\n  \"yen\": \"💴\",\n  \"dollar\": \"💵\",\n  \"euro\": \"💶\",\n  \"pound\": \"💷\",\n  \"money_with_wings\": \"💸\",\n  \"credit_card\": \"💳\",\n  \"receipt\": \"🧾\",\n  \"chart\": \"💹\",\n  \"envelope\": \"✉️\",\n  \"email\": \"📧\",\n  \"e-mail\": \"📧\",\n  \"incoming_envelope\": \"📨\",\n  \"envelope_with_arrow\": \"📩\",\n  \"outbox_tray\": \"📤\",\n  \"inbox_tray\": \"📥\",\n  \"package\": \"📦\",\n  \"mailbox\": \"📫\",\n  \"mailbox_closed\": \"📪\",\n  \"mailbox_with_mail\": \"📬\",\n  \"mailbox_with_no_mail\": \"📭\",\n  \"postbox\": \"📮\",\n  \"ballot_box\": \"🗳️\",\n  \"pencil2\": \"✏️\",\n  \"black_nib\": \"✒️\",\n  \"fountain_pen\": \"🖋️\",\n  \"pen\": \"🖊️\",\n  \"paintbrush\": \"🖌️\",\n  \"crayon\": \"🖍️\",\n  \"memo\": \"📝\",\n  \"pencil\": \"📝\",\n  \"briefcase\": \"💼\",\n  \"file_folder\": \"📁\",\n  \"open_file_folder\": \"📂\",\n  \"card_index_dividers\": \"🗂️\",\n  \"date\": \"📅\",\n  \"calendar\": \"📆\",\n  \"spiral_notepad\": \"🗒️\",\n  \"spiral_calendar\": \"🗓️\",\n  \"card_index\": \"📇\",\n  \"chart_with_upwards_trend\": \"📈\",\n  \"chart_with_downwards_trend\": \"📉\",\n  \"bar_chart\": \"📊\",\n  \"clipboard\": \"📋\",\n  \"pushpin\": \"📌\",\n  \"round_pushpin\": \"📍\",\n  \"paperclip\": \"📎\",\n  \"paperclips\": \"🖇️\",\n  \"straight_ruler\": \"📏\",\n  \"triangular_ruler\": \"📐\",\n  \"scissors\": \"✂️\",\n  \"card_file_box\": \"🗃️\",\n  \"file_cabinet\": \"🗄️\",\n  \"wastebasket\": \"🗑️\",\n  \"lock\": \"🔒\",\n  \"unlock\": \"🔓\",\n  \"lock_with_ink_pen\": \"🔏\",\n  \"closed_lock_with_key\": \"🔐\",\n  \"key\": \"🔑\",\n  \"old_key\": \"🗝️\",\n  \"hammer\": \"🔨\",\n  \"axe\": \"🪓\",\n  \"pick\": \"⛏️\",\n  \"hammer_and_pick\": \"⚒️\",\n  \"hammer_and_wrench\": \"🛠️\",\n  \"dagger\": \"🗡️\",\n  \"crossed_swords\": \"⚔️\",\n  \"gun\": \"🔫\",\n  \"boomerang\": \"🪃\",\n  \"bow_and_arrow\": \"🏹\",\n  \"shield\": \"🛡️\",\n  \"carpentry_saw\": \"🪚\",\n  \"wrench\": \"🔧\",\n  \"screwdriver\": \"🪛\",\n  \"nut_and_bolt\": \"🔩\",\n  \"gear\": \"⚙️\",\n  \"clamp\": \"🗜️\",\n  \"balance_scale\": \"⚖️\",\n  \"probing_cane\": \"🦯\",\n  \"link\": \"🔗\",\n  \"chains\": \"⛓️\",\n  \"hook\": \"🪝\",\n  \"toolbox\": \"🧰\",\n  \"magnet\": \"🧲\",\n  \"ladder\": \"🪜\",\n  \"alembic\": \"⚗️\",\n  \"test_tube\": \"🧪\",\n  \"petri_dish\": \"🧫\",\n  \"dna\": \"🧬\",\n  \"microscope\": \"🔬\",\n  \"telescope\": \"🔭\",\n  \"satellite\": \"📡\",\n  \"syringe\": \"💉\",\n  \"drop_of_blood\": \"🩸\",\n  \"pill\": \"💊\",\n  \"adhesive_bandage\": \"🩹\",\n  \"stethoscope\": \"🩺\",\n  \"door\": \"🚪\",\n  \"elevator\": \"🛗\",\n  \"mirror\": \"🪞\",\n  \"window\": \"🪟\",\n  \"bed\": \"🛏️\",\n  \"couch_and_lamp\": \"🛋️\",\n  \"chair\": \"🪑\",\n  \"toilet\": \"🚽\",\n  \"plunger\": \"🪠\",\n  \"shower\": \"🚿\",\n  \"bathtub\": \"🛁\",\n  \"mouse_trap\": \"🪤\",\n  \"razor\": \"🪒\",\n  \"lotion_bottle\": \"🧴\",\n  \"safety_pin\": \"🧷\",\n  \"broom\": \"🧹\",\n  \"basket\": \"🧺\",\n  \"roll_of_paper\": \"🧻\",\n  \"bucket\": \"🪣\",\n  \"soap\": \"🧼\",\n  \"toothbrush\": \"🪥\",\n  \"sponge\": \"🧽\",\n  \"fire_extinguisher\": \"🧯\",\n  \"shopping_cart\": \"🛒\",\n  \"smoking\": \"🚬\",\n  \"coffin\": \"⚰️\",\n  \"headstone\": \"🪦\",\n  \"funeral_urn\": \"⚱️\",\n  \"moyai\": \"🗿\",\n  \"placard\": \"🪧\",\n  \"atm\": \"🏧\",\n  \"put_litter_in_its_place\": \"🚮\",\n  \"potable_water\": \"🚰\",\n  \"wheelchair\": \"♿\",\n  \"mens\": \"🚹\",\n  \"womens\": \"🚺\",\n  \"restroom\": \"🚻\",\n  \"baby_symbol\": \"🚼\",\n  \"wc\": \"🚾\",\n  \"passport_control\": \"🛂\",\n  \"customs\": \"🛃\",\n  \"baggage_claim\": \"🛄\",\n  \"left_luggage\": \"🛅\",\n  \"warning\": \"⚠️\",\n  \"children_crossing\": \"🚸\",\n  \"no_entry\": \"⛔\",\n  \"no_entry_sign\": \"🚫\",\n  \"no_bicycles\": \"🚳\",\n  \"no_smoking\": \"🚭\",\n  \"do_not_litter\": \"🚯\",\n  \"non-potable_water\": \"🚱\",\n  \"no_pedestrians\": \"🚷\",\n  \"no_mobile_phones\": \"📵\",\n  \"underage\": \"🔞\",\n  \"radioactive\": \"☢️\",\n  \"biohazard\": \"☣️\",\n  \"arrow_up\": \"⬆️\",\n  \"arrow_upper_right\": \"↗️\",\n  \"arrow_right\": \"➡️\",\n  \"arrow_lower_right\": \"↘️\",\n  \"arrow_down\": \"⬇️\",\n  \"arrow_lower_left\": \"↙️\",\n  \"arrow_left\": \"⬅️\",\n  \"arrow_upper_left\": \"↖️\",\n  \"arrow_up_down\": \"↕️\",\n  \"left_right_arrow\": \"↔️\",\n  \"leftwards_arrow_with_hook\": \"↩️\",\n  \"arrow_right_hook\": \"↪️\",\n  \"arrow_heading_up\": \"⤴️\",\n  \"arrow_heading_down\": \"⤵️\",\n  \"arrows_clockwise\": \"🔃\",\n  \"arrows_counterclockwise\": \"🔄\",\n  \"back\": \"🔙\",\n  \"end\": \"🔚\",\n  \"on\": \"🔛\",\n  \"soon\": \"🔜\",\n  \"top\": \"🔝\",\n  \"place_of_worship\": \"🛐\",\n  \"atom_symbol\": \"⚛️\",\n  \"om\": \"🕉️\",\n  \"star_of_david\": \"✡️\",\n  \"wheel_of_dharma\": \"☸️\",\n  \"yin_yang\": \"☯️\",\n  \"latin_cross\": \"✝️\",\n  \"orthodox_cross\": \"☦️\",\n  \"star_and_crescent\": \"☪️\",\n  \"peace_symbol\": \"☮️\",\n  \"menorah\": \"🕎\",\n  \"six_pointed_star\": \"🔯\",\n  \"aries\": \"♈\",\n  \"taurus\": \"♉\",\n  \"gemini\": \"♊\",\n  \"cancer\": \"♋\",\n  \"leo\": \"♌\",\n  \"virgo\": \"♍\",\n  \"libra\": \"♎\",\n  \"scorpius\": \"♏\",\n  \"sagittarius\": \"♐\",\n  \"capricorn\": \"♑\",\n  \"aquarius\": \"♒\",\n  \"pisces\": \"♓\",\n  \"ophiuchus\": \"⛎\",\n  \"twisted_rightwards_arrows\": \"🔀\",\n  \"repeat\": \"🔁\",\n  \"repeat_one\": \"🔂\",\n  \"arrow_forward\": \"▶️\",\n  \"fast_forward\": \"⏩\",\n  \"next_track_button\": \"⏭️\",\n  \"play_or_pause_button\": \"⏯️\",\n  \"arrow_backward\": \"◀️\",\n  \"rewind\": \"⏪\",\n  \"previous_track_button\": \"⏮️\",\n  \"arrow_up_small\": \"🔼\",\n  \"arrow_double_up\": \"⏫\",\n  \"arrow_down_small\": \"🔽\",\n  \"arrow_double_down\": \"⏬\",\n  \"pause_button\": \"⏸️\",\n  \"stop_button\": \"⏹️\",\n  \"record_button\": \"⏺️\",\n  \"eject_button\": \"⏏️\",\n  \"cinema\": \"🎦\",\n  \"low_brightness\": \"🔅\",\n  \"high_brightness\": \"🔆\",\n  \"signal_strength\": \"📶\",\n  \"vibration_mode\": \"📳\",\n  \"mobile_phone_off\": \"📴\",\n  \"female_sign\": \"♀️\",\n  \"male_sign\": \"♂️\",\n  \"transgender_symbol\": \"⚧️\",\n  \"heavy_multiplication_x\": \"✖️\",\n  \"heavy_plus_sign\": \"➕\",\n  \"heavy_minus_sign\": \"➖\",\n  \"heavy_division_sign\": \"➗\",\n  \"infinity\": \"♾️\",\n  \"bangbang\": \"‼️\",\n  \"interrobang\": \"⁉️\",\n  \"question\": \"❓\",\n  \"grey_question\": \"❔\",\n  \"grey_exclamation\": \"❕\",\n  \"exclamation\": \"❗\",\n  \"heavy_exclamation_mark\": \"❗\",\n  \"wavy_dash\": \"〰️\",\n  \"currency_exchange\": \"💱\",\n  \"heavy_dollar_sign\": \"💲\",\n  \"medical_symbol\": \"⚕️\",\n  \"recycle\": \"♻️\",\n  \"fleur_de_lis\": \"⚜️\",\n  \"trident\": \"🔱\",\n  \"name_badge\": \"📛\",\n  \"beginner\": \"🔰\",\n  \"o\": \"⭕\",\n  \"white_check_mark\": \"✅\",\n  \"ballot_box_with_check\": \"☑️\",\n  \"heavy_check_mark\": \"✔️\",\n  \"x\": \"❌\",\n  \"negative_squared_cross_mark\": \"❎\",\n  \"curly_loop\": \"➰\",\n  \"loop\": \"➿\",\n  \"part_alternation_mark\": \"〽️\",\n  \"eight_spoked_asterisk\": \"✳️\",\n  \"eight_pointed_black_star\": \"✴️\",\n  \"sparkle\": \"❇️\",\n  \"copyright\": \"©️\",\n  \"registered\": \"®️\",\n  \"tm\": \"™️\",\n  \"hash\": \"#️⃣\",\n  \"asterisk\": \"*️⃣\",\n  \"zero\": \"0️⃣\",\n  \"one\": \"1️⃣\",\n  \"two\": \"2️⃣\",\n  \"three\": \"3️⃣\",\n  \"four\": \"4️⃣\",\n  \"five\": \"5️⃣\",\n  \"six\": \"6️⃣\",\n  \"seven\": \"7️⃣\",\n  \"eight\": \"8️⃣\",\n  \"nine\": \"9️⃣\",\n  \"keycap_ten\": \"🔟\",\n  \"capital_abcd\": \"🔠\",\n  \"abcd\": \"🔡\",\n  \"symbols\": \"🔣\",\n  \"abc\": \"🔤\",\n  \"a\": \"🅰️\",\n  \"ab\": \"🆎\",\n  \"b\": \"🅱️\",\n  \"cl\": \"🆑\",\n  \"cool\": \"🆒\",\n  \"free\": \"🆓\",\n  \"information_source\": \"ℹ️\",\n  \"id\": \"🆔\",\n  \"m\": \"Ⓜ️\",\n  \"new\": \"🆕\",\n  \"ng\": \"🆖\",\n  \"o2\": \"🅾️\",\n  \"ok\": \"🆗\",\n  \"parking\": \"🅿️\",\n  \"sos\": \"🆘\",\n  \"up\": \"🆙\",\n  \"vs\": \"🆚\",\n  \"koko\": \"🈁\",\n  \"sa\": \"🈂️\",\n  \"ideograph_advantage\": \"🉐\",\n  \"accept\": \"🉑\",\n  \"congratulations\": \"㊗️\",\n  \"secret\": \"㊙️\",\n  \"u6e80\": \"🈵\",\n  \"red_circle\": \"🔴\",\n  \"orange_circle\": \"🟠\",\n  \"yellow_circle\": \"🟡\",\n  \"green_circle\": \"🟢\",\n  \"large_blue_circle\": \"🔵\",\n  \"purple_circle\": \"🟣\",\n  \"brown_circle\": \"🟤\",\n  \"black_circle\": \"⚫\",\n  \"white_circle\": \"⚪\",\n  \"red_square\": \"🟥\",\n  \"orange_square\": \"🟧\",\n  \"yellow_square\": \"🟨\",\n  \"green_square\": \"🟩\",\n  \"blue_square\": \"🟦\",\n  \"purple_square\": \"🟪\",\n  \"brown_square\": \"🟫\",\n  \"black_large_square\": \"⬛\",\n  \"white_large_square\": \"⬜\",\n  \"black_medium_square\": \"◼️\",\n  \"white_medium_square\": \"◻️\",\n  \"black_medium_small_square\": \"◾\",\n  \"white_medium_small_square\": \"◽\",\n  \"black_small_square\": \"▪️\",\n  \"white_small_square\": \"▫️\",\n  \"large_orange_diamond\": \"🔶\",\n  \"large_blue_diamond\": \"🔷\",\n  \"small_orange_diamond\": \"🔸\",\n  \"small_blue_diamond\": \"🔹\",\n  \"small_red_triangle\": \"🔺\",\n  \"small_red_triangle_down\": \"🔻\",\n  \"diamond_shape_with_a_dot_inside\": \"💠\",\n  \"radio_button\": \"🔘\",\n  \"white_square_button\": \"🔳\",\n  \"black_square_button\": \"🔲\",\n  \"checkered_flag\": \"🏁\",\n  \"triangular_flag_on_post\": \"🚩\",\n  \"crossed_flags\": \"🎌\",\n  \"black_flag\": \"🏴\",\n  \"white_flag\": \"🏳️\",\n  \"rainbow_flag\": \"🏳️‍🌈\",\n  \"transgender_flag\": \"🏳️‍⚧️\",\n  \"pirate_flag\": \"🏴‍☠️\",\n  \"ascension_island\": \"🇦🇨\",\n  \"andorra\": \"🇦🇩\",\n  \"united_arab_emirates\": \"🇦🇪\",\n  \"afghanistan\": \"🇦🇫\",\n  \"antigua_barbuda\": \"🇦🇬\",\n  \"anguilla\": \"🇦🇮\",\n  \"albania\": \"🇦🇱\",\n  \"armenia\": \"🇦🇲\",\n  \"angola\": \"🇦🇴\",\n  \"antarctica\": \"🇦🇶\",\n  \"argentina\": \"🇦🇷\",\n  \"american_samoa\": \"🇦🇸\",\n  \"austria\": \"🇦🇹\",\n  \"australia\": \"🇦🇺\",\n  \"aruba\": \"🇦🇼\",\n  \"aland_islands\": \"🇦🇽\",\n  \"azerbaijan\": \"🇦🇿\",\n  \"bosnia_herzegovina\": \"🇧🇦\",\n  \"barbados\": \"🇧🇧\",\n  \"bangladesh\": \"🇧🇩\",\n  \"belgium\": \"🇧🇪\",\n  \"burkina_faso\": \"🇧🇫\",\n  \"bulgaria\": \"🇧🇬\",\n  \"bahrain\": \"🇧🇭\",\n  \"burundi\": \"🇧🇮\",\n  \"benin\": \"🇧🇯\",\n  \"st_barthelemy\": \"🇧🇱\",\n  \"bermuda\": \"🇧🇲\",\n  \"brunei\": \"🇧🇳\",\n  \"bolivia\": \"🇧🇴\",\n  \"caribbean_netherlands\": \"🇧🇶\",\n  \"brazil\": \"🇧🇷\",\n  \"bahamas\": \"🇧🇸\",\n  \"bhutan\": \"🇧🇹\",\n  \"bouvet_island\": \"🇧🇻\",\n  \"botswana\": \"🇧🇼\",\n  \"belarus\": \"🇧🇾\",\n  \"belize\": \"🇧🇿\",\n  \"canada\": \"🇨🇦\",\n  \"cocos_islands\": \"🇨🇨\",\n  \"congo_kinshasa\": \"🇨🇩\",\n  \"central_african_republic\": \"🇨🇫\",\n  \"congo_brazzaville\": \"🇨🇬\",\n  \"switzerland\": \"🇨🇭\",\n  \"cote_divoire\": \"🇨🇮\",\n  \"cook_islands\": \"🇨🇰\",\n  \"chile\": \"🇨🇱\",\n  \"cameroon\": \"🇨🇲\",\n  \"cn\": \"🇨🇳\",\n  \"colombia\": \"🇨🇴\",\n  \"clipperton_island\": \"🇨🇵\",\n  \"costa_rica\": \"🇨🇷\",\n  \"cuba\": \"🇨🇺\",\n  \"cape_verde\": \"🇨🇻\",\n  \"curacao\": \"🇨🇼\",\n  \"christmas_island\": \"🇨🇽\",\n  \"cyprus\": \"🇨🇾\",\n  \"czech_republic\": \"🇨🇿\",\n  \"de\": \"🇩🇪\",\n  \"diego_garcia\": \"🇩🇬\",\n  \"djibouti\": \"🇩🇯\",\n  \"denmark\": \"🇩🇰\",\n  \"dominica\": \"🇩🇲\",\n  \"dominican_republic\": \"🇩🇴\",\n  \"algeria\": \"🇩🇿\",\n  \"ceuta_melilla\": \"🇪🇦\",\n  \"ecuador\": \"🇪🇨\",\n  \"estonia\": \"🇪🇪\",\n  \"egypt\": \"🇪🇬\",\n  \"western_sahara\": \"🇪🇭\",\n  \"eritrea\": \"🇪🇷\",\n  \"es\": \"🇪🇸\",\n  \"ethiopia\": \"🇪🇹\",\n  \"eu\": \"🇪🇺\",\n  \"european_union\": \"🇪🇺\",\n  \"finland\": \"🇫🇮\",\n  \"fiji\": \"🇫🇯\",\n  \"falkland_islands\": \"🇫🇰\",\n  \"micronesia\": \"🇫🇲\",\n  \"faroe_islands\": \"🇫🇴\",\n  \"fr\": \"🇫🇷\",\n  \"gabon\": \"🇬🇦\",\n  \"gb\": \"🇬🇧\",\n  \"uk\": \"🇬🇧\",\n  \"grenada\": \"🇬🇩\",\n  \"georgia\": \"🇬🇪\",\n  \"french_guiana\": \"🇬🇫\",\n  \"guernsey\": \"🇬🇬\",\n  \"ghana\": \"🇬🇭\",\n  \"gibraltar\": \"🇬🇮\",\n  \"greenland\": \"🇬🇱\",\n  \"gambia\": \"🇬🇲\",\n  \"guinea\": \"🇬🇳\",\n  \"guadeloupe\": \"🇬🇵\",\n  \"equatorial_guinea\": \"🇬🇶\",\n  \"greece\": \"🇬🇷\",\n  \"south_georgia_south_sandwich_islands\": \"🇬🇸\",\n  \"guatemala\": \"🇬🇹\",\n  \"guam\": \"🇬🇺\",\n  \"guinea_bissau\": \"🇬🇼\",\n  \"guyana\": \"🇬🇾\",\n  \"hong_kong\": \"🇭🇰\",\n  \"heard_mcdonald_islands\": \"🇭🇲\",\n  \"honduras\": \"🇭🇳\",\n  \"croatia\": \"🇭🇷\",\n  \"haiti\": \"🇭🇹\",\n  \"hungary\": \"🇭🇺\",\n  \"canary_islands\": \"🇮🇨\",\n  \"indonesia\": \"🇮🇩\",\n  \"ireland\": \"🇮🇪\",\n  \"israel\": \"🇮🇱\",\n  \"isle_of_man\": \"🇮🇲\",\n  \"india\": \"🇮🇳\",\n  \"british_indian_ocean_territory\": \"🇮🇴\",\n  \"iraq\": \"🇮🇶\",\n  \"iran\": \"🇮🇷\",\n  \"iceland\": \"🇮🇸\",\n  \"it\": \"🇮🇹\",\n  \"jersey\": \"🇯🇪\",\n  \"jamaica\": \"🇯🇲\",\n  \"jordan\": \"🇯🇴\",\n  \"jp\": \"🇯🇵\",\n  \"kenya\": \"🇰🇪\",\n  \"kyrgyzstan\": \"🇰🇬\",\n  \"cambodia\": \"🇰🇭\",\n  \"kiribati\": \"🇰🇮\",\n  \"comoros\": \"🇰🇲\",\n  \"st_kitts_nevis\": \"🇰🇳\",\n  \"north_korea\": \"🇰🇵\",\n  \"kr\": \"🇰🇷\",\n  \"kuwait\": \"🇰🇼\",\n  \"cayman_islands\": \"🇰🇾\",\n  \"kazakhstan\": \"🇰🇿\",\n  \"laos\": \"🇱🇦\",\n  \"lebanon\": \"🇱🇧\",\n  \"st_lucia\": \"🇱🇨\",\n  \"liechtenstein\": \"🇱🇮\",\n  \"sri_lanka\": \"🇱🇰\",\n  \"liberia\": \"🇱🇷\",\n  \"lesotho\": \"🇱🇸\",\n  \"lithuania\": \"🇱🇹\",\n  \"luxembourg\": \"🇱🇺\",\n  \"latvia\": \"🇱🇻\",\n  \"libya\": \"🇱🇾\",\n  \"morocco\": \"🇲🇦\",\n  \"monaco\": \"🇲🇨\",\n  \"moldova\": \"🇲🇩\",\n  \"montenegro\": \"🇲🇪\",\n  \"st_martin\": \"🇲🇫\",\n  \"madagascar\": \"🇲🇬\",\n  \"marshall_islands\": \"🇲🇭\",\n  \"macedonia\": \"🇲🇰\",\n  \"mali\": \"🇲🇱\",\n  \"myanmar\": \"🇲🇲\",\n  \"mongolia\": \"🇲🇳\",\n  \"macau\": \"🇲🇴\",\n  \"northern_mariana_islands\": \"🇲🇵\",\n  \"martinique\": \"🇲🇶\",\n  \"mauritania\": \"🇲🇷\",\n  \"montserrat\": \"🇲🇸\",\n  \"malta\": \"🇲🇹\",\n  \"mauritius\": \"🇲🇺\",\n  \"maldives\": \"🇲🇻\",\n  \"malawi\": \"🇲🇼\",\n  \"mexico\": \"🇲🇽\",\n  \"malaysia\": \"🇲🇾\",\n  \"mozambique\": \"🇲🇿\",\n  \"namibia\": \"🇳🇦\",\n  \"new_caledonia\": \"🇳🇨\",\n  \"niger\": \"🇳🇪\",\n  \"norfolk_island\": \"🇳🇫\",\n  \"nigeria\": \"🇳🇬\",\n  \"nicaragua\": \"🇳🇮\",\n  \"netherlands\": \"🇳🇱\",\n  \"norway\": \"🇳🇴\",\n  \"nepal\": \"🇳🇵\",\n  \"nauru\": \"🇳🇷\",\n  \"niue\": \"🇳🇺\",\n  \"new_zealand\": \"🇳🇿\",\n  \"oman\": \"🇴🇲\",\n  \"panama\": \"🇵🇦\",\n  \"peru\": \"🇵🇪\",\n  \"french_polynesia\": \"🇵🇫\",\n  \"papua_new_guinea\": \"🇵🇬\",\n  \"philippines\": \"🇵🇭\",\n  \"pakistan\": \"🇵🇰\",\n  \"poland\": \"🇵🇱\",\n  \"st_pierre_miquelon\": \"🇵🇲\",\n  \"pitcairn_islands\": \"🇵🇳\",\n  \"puerto_rico\": \"🇵🇷\",\n  \"palestinian_territories\": \"🇵🇸\",\n  \"portugal\": \"🇵🇹\",\n  \"palau\": \"🇵🇼\",\n  \"paraguay\": \"🇵🇾\",\n  \"qatar\": \"🇶🇦\",\n  \"reunion\": \"🇷🇪\",\n  \"romania\": \"🇷🇴\",\n  \"serbia\": \"🇷🇸\",\n  \"ru\": \"🇷🇺\",\n  \"rwanda\": \"🇷🇼\",\n  \"saudi_arabia\": \"🇸🇦\",\n  \"solomon_islands\": \"🇸🇧\",\n  \"seychelles\": \"🇸🇨\",\n  \"sudan\": \"🇸🇩\",\n  \"sweden\": \"🇸🇪\",\n  \"singapore\": \"🇸🇬\",\n  \"st_helena\": \"🇸🇭\",\n  \"slovenia\": \"🇸🇮\",\n  \"svalbard_jan_mayen\": \"🇸🇯\",\n  \"slovakia\": \"🇸🇰\",\n  \"sierra_leone\": \"🇸🇱\",\n  \"san_marino\": \"🇸🇲\",\n  \"senegal\": \"🇸🇳\",\n  \"somalia\": \"🇸🇴\",\n  \"suriname\": \"🇸🇷\",\n  \"south_sudan\": \"🇸🇸\",\n  \"sao_tome_principe\": \"🇸🇹\",\n  \"el_salvador\": \"🇸🇻\",\n  \"sint_maarten\": \"🇸🇽\",\n  \"syria\": \"🇸🇾\",\n  \"swaziland\": \"🇸🇿\",\n  \"tristan_da_cunha\": \"🇹🇦\",\n  \"turks_caicos_islands\": \"🇹🇨\",\n  \"chad\": \"🇹🇩\",\n  \"french_southern_territories\": \"🇹🇫\",\n  \"togo\": \"🇹🇬\",\n  \"thailand\": \"🇹🇭\",\n  \"tajikistan\": \"🇹🇯\",\n  \"tokelau\": \"🇹🇰\",\n  \"timor_leste\": \"🇹🇱\",\n  \"turkmenistan\": \"🇹🇲\",\n  \"tunisia\": \"🇹🇳\",\n  \"tonga\": \"🇹🇴\",\n  \"tr\": \"🇹🇷\",\n  \"trinidad_tobago\": \"🇹🇹\",\n  \"tuvalu\": \"🇹🇻\",\n  \"taiwan\": \"🇹🇼\",\n  \"tanzania\": \"🇹🇿\",\n  \"ukraine\": \"🇺🇦\",\n  \"uganda\": \"🇺🇬\",\n  \"us_outlying_islands\": \"🇺🇲\",\n  \"united_nations\": \"🇺🇳\",\n  \"us\": \"🇺🇸\",\n  \"uruguay\": \"🇺🇾\",\n  \"uzbekistan\": \"🇺🇿\",\n  \"vatican_city\": \"🇻🇦\",\n  \"st_vincent_grenadines\": \"🇻🇨\",\n  \"venezuela\": \"🇻🇪\",\n  \"british_virgin_islands\": \"🇻🇬\",\n  \"us_virgin_islands\": \"🇻🇮\",\n  \"vietnam\": \"🇻🇳\",\n  \"vanuatu\": \"🇻🇺\",\n  \"wallis_futuna\": \"🇼🇫\",\n  \"samoa\": \"🇼🇸\",\n  \"kosovo\": \"🇽🇰\",\n  \"yemen\": \"🇾🇪\",\n  \"mayotte\": \"🇾🇹\",\n  \"south_africa\": \"🇿🇦\",\n  \"zambia\": \"🇿🇲\",\n  \"zimbabwe\": \"🇿🇼\",\n  \"england\": \"🏴󠁧󠁢󠁥󠁮󠁧󠁿\",\n  \"scotland\": \"🏴󠁧󠁢󠁳󠁣󠁴󠁿\",\n  \"wales\": \"🏴󠁧󠁢󠁷󠁬󠁳󠁿\"\n}`);\n\n\nexport function withEmoji(str: string): string {\n  return str.replace(/:(\\w+(_\\w+))*:/gi, (m, g1) => {\n    return _emojiMap[g1] ?? m;\n  });\n}\n", "/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\n\nimport { h } from 'preact';\n\nexport const IssueOpenIcon = () => (\n\t<svg\n\t\tclassName=\"octicon octicon-issue-opened open\"\n\t\tviewBox=\"0 0 14 16\"\n\t\tversion=\"1.1\"\n\t\twidth=\"16\"\n\t\theight=\"16\"\n\t\taria-hidden=\"true\"\n\t>\n\t\t<path\n\t\t\tfill-rule=\"evenodd\"\n\t\t\td=\"M7 2.3c3.14 0 5.7 2.56 5.7 5.7s-2.56 5.7-5.7 5.7A5.71 5.71 0 011.3 8c0-3.14 2.56-5.7 5.7-5.7zM7 1C3.14 1 0 4.14 0 8s3.14 7 7 7 7-3.14 7-7-3.14-7-7-7zm1 3H6v5h2V4zm0 6H6v2h2v-2z\"\n\t\t></path>\n\t</svg>\n);\n\n\nexport const IssueClosedIcon = () => (\n\t<svg\n\t\tclassName=\"octicon octicon-issue-closed closed\"\n\t\tviewBox=\"0 0 16 16\"\n\t\tversion=\"1.1\"\n\t\twidth=\"16\"\n\t\theight=\"16\"\n\t\taria-hidden=\"true\"\n\t>\n\t\t<path\n\t\t\tfill-rule=\"evenodd\"\n\t\t\td=\"M7 10h2v2H7v-2zm2-6H7v5h2V4zm1.5 1.5l-1 1L12 9l4-4.5-1-1L12 7l-1.5-1.5zM8 13.7A5.71 5.71 0 012.3 8c0-3.14 2.56-5.7 5.7-5.7 1.83 0 3.45.88 4.5 2.2l.92-.92A6.947 6.947 0 008 1C4.14 1 1 4.14 1 8s3.14 7 7 7 7-3.14 7-7l-1.52 1.52c-.66 2.41-2.86 4.19-5.48 4.19v-.01z\"\n\t\t></path>\n\t</svg>\n);\n\n\nexport const PRIcon = () => (\n\t<svg\n\t\tclassName=\"octicon octicon-git-merge merged\"\n\t\tviewBox=\"0 0 12 16\"\n\t\tversion=\"1.1\"\n\t\twidth=\"16\"\n\t\theight=\"16\"\n\t\taria-hidden=\"true\"\n\t>\n\t\t<path\n\t\t\tfill-rule=\"evenodd\"\n\t\t\td=\"M10 7c-.73 0-1.38.41-1.73 1.02V8C7.22 7.98 6 7.64 5.14 6.98c-.75-.58-1.5-1.61-1.89-2.44A1.993 1.993 0 002 .99C.89.99 0 1.89 0 3a2 2 0 001 1.72v6.56c-.59.35-1 .99-1 1.72 0 1.11.89 2 2 2a1.993 1.993 0 001-3.72V7.67c.67.7 1.44 1.27 2.3 1.69.86.42 2.03.63 2.97.64v-.02c.36.61 1 1.02 1.73 1.02 1.11 0 2-.89 2-2 0-1.11-.89-2-2-2zm-6.8 6c0 .66-.55 1.2-1.2 1.2-.65 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2zM2 4.2C1.34 4.2.8 3.65.8 3c0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2zm8 6c-.66 0-1.2-.55-1.2-1.2 0-.65.55-1.2 1.2-1.2.65 0 1.2.55 1.2 1.2 0 .65-.55 1.2-1.2 1.2z\"\n\t\t></path>\n\t</svg>\n);\n", "/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\n\nimport { FunctionComponent, h } from 'preact';\nimport { useEffect, useMemo, useState } from 'preact/hooks';\nimport { withEmoji } from '../common/emoji';\nimport { SearchIssuesAndPullRequestsResponseItemsItem, SearchIssuesAndPullRequestsResponseItemsItemLabelsItem, SearchIssuesAndPullRequestsResponseItemsItemUser } from '../common/types';\nimport { IssueClosedIcon, IssueOpenIcon, PRIcon } from './icons';\n\nconst defaultMaxCount = 13;\n\nexport const AllItems: FunctionComponent<{ items: ReadonlyArray<SearchIssuesAndPullRequestsResponseItemsItem>; }> = ({ items: rawItems }) => {\n\tconst [hidden, setHidden] = useState<number[]>([]);\n\tconst items = useMemo(() => {\n\t\tconst seen = new Set<string>();\n\t\treturn rawItems.filter(item => {\n\t\t\tif (hidden.includes(item.id)) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tif (seen.has(item.url)) {\n\t\t\t\treturn false;\n\t\t\t}\n\n\t\t\tseen.add(item.url);\n\t\t\treturn true;\n\t\t});\n\t}, [rawItems, hidden]);\n\n\t// reset hidden items running a new or different query\n\tuseEffect(() => setHidden([]), [rawItems]);\n\n\tconst hasManyRepos = items.some(item => item.repository_url !== items[0].repository_url);\n\tconst renderItem = (item: SearchIssuesAndPullRequestsResponseItemsItem) =>\n\t\t<Item key={item.id} item={item} hide={() => setHidden([...hidden, item.id])} showRepo={hasManyRepos} />;\n\n\tif (items.length <= defaultMaxCount) {\n\t\treturn <div>{items.map(renderItem)}</div>;\n\t}\n\n\tconst [collapsed, setCollapsed] = useState(true);\n\tconst di = collapsed ? items.slice(0, defaultMaxCount) : items;\n\n\treturn <div className='large'>\n\t\t{di.map(renderItem)}\n\t\t<div className=\"collapse\">\n\t\t\t<CollapseButton n={items.length} setCollapsed={setCollapsed} collapsed={collapsed} />\n\t\t</div>\n\t</div>;\n};\n\n\nconst Item: FunctionComponent<{\n\titem: SearchIssuesAndPullRequestsResponseItemsItem;\n\thide(): void;\n\tshowRepo: boolean;\n}> = ({ item, showRepo, hide }) =>\n\t\t<div className='item-row'>\n\t\t\t<div className='item-main'>\n\t\t\t\t<div className=\"item-state\">{item.pull_request ? <PRIcon /> : item.closed_at ? <IssueClosedIcon /> : <IssueOpenIcon />}</div>\n\t\t\t\t<div style={{ flex: 'auto', flexBasis: 0 }}>\n\t\t\t\t\t{showRepo && <RepoLabel url={item.repository_url} />}\n\t\t\t\t\t<a href={item.html_url} className=\"title\">{item.title}</a>\n\t\t\t\t\t{item.labels.map(label => <Label label={label} key={label.id} />)}\n\t\t\t\t</div>\n\t\t\t\t<div className=\"user\">\n\t\t\t\t\t{item.assignees?.map(user => <Avatar user={user} key={user.id} />)}\n\t\t\t\t</div>\n\n\t\t\t</div>\n\n\t\t\t<div className=\"status\">\n\t\t\t\t<span>#{item.number} opened {new Date(item.created_at).toLocaleDateString()} by {item.user.login}</span>\n\t\t\t\t<span style={{ flex: 1 }} />\n\t\t\t\t<ul className='actions'>\n\t\t\t\t\t<li><a role='button' onClick={hide}>Hide</a></li>\n\t\t\t\t</ul>\n\t\t\t</div>\n\t\t</div>;\n\n\nconst RepoLabel: FunctionComponent<{ url: string; }> = ({ url }) => {\n\tconst match = /.+\\/(.+\\/.+)$/.exec(url);\n\treturn match ? <a href={`https://github.com/${match[1]}`} className=\"repo title\">{match[1]}</a> : null;\n};\n\n\nconst Label: FunctionComponent<{ label: SearchIssuesAndPullRequestsResponseItemsItemLabelsItem; }> = ({ label }) =>\n\t<span className=\"label\" key={label.id} style={{ backgroundColor: `#${label.color}` }}>\n\t\t<a style={{ color: getContrastColor(label.color) }}>{withEmoji(label.name)}</a>\n\t</span>;\n\n\nconst Avatar: FunctionComponent<{ user: SearchIssuesAndPullRequestsResponseItemsItemUser; }> = ({ user }) =>\n\t<a key={user.id} href={user.html_url}>\n\t\t<img src={user.avatar_url} width=\"20\" height=\"20\" alt={`@${user.login}`} />\n\t</a>;\n\n\nconst CollapseButton: FunctionComponent<{ n: number; collapsed: boolean; setCollapsed: (fn: boolean) => void; }> = ({ collapsed, setCollapsed, n }) =>\n\tcollapsed\n\t\t? <span className=\"more\" onClick={() => setCollapsed(false)}>▼ Show {n - defaultMaxCount} More</span>\n\t\t: <span className=\"less\" onClick={() => setCollapsed(true)}>▲ Show Less</span>;\n\n\nfunction getContrastColor(color: string): string {\n\t// Color algorithm from https://stackoverflow.com/questions/1855884/determine-font-color-based-on-background-color\n\tconst r = Number.parseInt(color.substr(0, 2), 16);\n\tconst g = Number.parseInt(color.substr(2, 2), 16);\n\tconst b = Number.parseInt(color.substr(4, 2), 16);\n\treturn ((0.299 * r + 0.587 * g + 0.114 * b) / 255) > 0.5 ? 'black' : 'white';\n}\n", ".item-row {\n\tpadding: 0.5em;\n\tcolor: var(--theme-foreground);\n}\n.item-row:hover {\n\tbackground-color: rgba(0, 0, 0, 0.1);\n}\n.item-main {\n\tdisplay: flex;\n\talign-items: center;\n}\n.title {\n\tcolor: var(--theme-foreground) !important;\n\tfont-size: 1.1em;\n\ttext-decoration: none;\n\tmargin-right: 3px;\n}\n.title:hover {\n\ttext-decoration: underline;\n}\n.title.repo {\n\topacity: 70%;\n\tpadding-right: 8px;\n}\n.label {\n\tfont-size: 0.85em;\n\tmargin: 0 2px;\n\tpadding: 1px 6px 2px 6px;\n\tborder-radius: 1em;\n}\n.label a {\n\t/* Prevents wrap breaking on hypen */\n\tdisplay: inline-block;\n}\n.status {\n\tdisplay: flex;\n\talign-items: center;\n\tfont-size: 0.8em;\n\topacity: 60%;\n\tpadding: 0.1em 0 0.1em 0.3em;\n}\n.assignee {\n\tflex: shrink;\n}\n.user {\n\tdisplay: flex;\n}\n.user img {\n\tpadding: 0 0.1em;\n\tborder-radius: 20px;\n\tborder: solid 1px transparent;\n}\n.item-state {\n\tpadding-right: 0.3em;\n\topacity: 60%;\n}\n.item-state .octicon {\n\tfill: var(--theme-foreground);\n\topacity: 0.7;\n\tdisplay: block;\n}\n.item-state .octicon.open {\n\tfill: green;\n\topacity: 1;\n}\n.stats {\n\ttext-align: center;\n\tfont-size: 0.7em;\n\topacity: 60%;\n\tpadding-top: 0.6em;\n}\n.collapse {\n\ttext-align: center;\n\tfont-size: 0.9em;\n\tcursor: pointer;\n\tpadding: 0.3em 0;\n}\n.collapse:hover > span,\n.collapse:focus > span {\n\tbackground: var(--theme-button-hover-background);\n}\n.collapse > span {\n\tcolor: var(--theme-button-foreground);\n\tbackground: var(--theme-button-background);\n\tpadding: 3px;\n}\n.item-row .start-working {\n\tdisplay: none;\n}\n.item-row .start-working a {\n\tcolor: var(--theme-foreground) !important;\n\tfont-size: 0.9em;\n\ttext-decoration: none;\n}\n.item-row .start-working a:hover {\n\ttext-decoration: underline;\n}\n.item-row:hover .start-working {\n\tdisplay: inline;\n}\n.item-row .actions {\n\tdisplay: none;\n\tmargin: 0;\n\tpadding: 0;\n}\n.item-row:hover .actions,\n.item-row:focus-within .actions {\n\tdisplay: flex;\n}\n.item-row .actions li {\n\tlist-style-type: none;\n\tpadding: 0;\n\tcursor: pointer;\n}\n\n.item-row .actions li a {\n\tcolor: inherit;\n}\n", "/*---------------------------------------------------------\n * Copyright (C) Microsoft Corporation. All rights reserved.\n *--------------------------------------------------------*/\n\nimport { h, render } from 'preact';\nimport type { ActivationFunction } from 'vscode-notebook-renderer';\nimport { AllItems } from './renderer';\nimport rendererCss from './renderer.css';\n\nexport const activate: ActivationFunction = () => {\n\tconst style = document.createElement('style');\n\tstyle.type = 'text/css';\n\tstyle.textContent = rendererCss;\n\n\treturn {\n\t\trenderOutputItem(info, element) {\n\t\t\tlet shadow = element.shadowRoot;\n\t\t\tif (!shadow) {\n\t\t\t\tshadow = element.attachShadow({ mode: 'open' });\n\n\t\t\t\tshadow.append(style.cloneNode(true));\n\n\t\t\t\tconst root = document.createElement('div');\n\t\t\t\troot.id = 'root';\n\t\t\t\tshadow.append(root);\n\t\t\t}\n\t\t\trender(<AllItems items={info.json()} />, shadow.querySelector('#root')!);\n\t\t},\n\t};\n};\n"], "mappings": ";AAAO,ICWDA;ADXC,IEoGMC;AFpGN,IGqKHC;AHrKG,IG6KDC;AH7KC,IG2LHC;AH3LG,IIKDC;AJLC,IKEIC;ALFJ,IAAMC,IAAY,CAAA;AAAlB,IACMC,IAAY,CAAA;AADlB,IAEMC,IAAqB;AMK3B,SAASC,EAAOC,IAAKC,IAAAA;AAAAA,WAClBN,MAAKM,GAAOD,CAAAA,GAAIL,EAAAA,IAAKM,GAAMN,EAAAA;AAAAA,SAAAA;AAAAA;AAU9B,SAASO,EAAWC,IAAAA;AAAAA,MACtBC,KAAaD,GAAKC;AAClBA,EAAAA,MAAYA,GAAWC,YAAYF,EAAAA;AAAAA;AJVxC,SAAgBG,EAAcC,IAAMN,IAAOO,IAAAA;AAA3C,MAEEb,IAAAA,KAAAA,WADGc,KAAkB,CAAA;AAAA,OAEjBd,MAAKM,GACC,WAANN,MAAqB,UAANA,OAAac,GAAgBd,EAAAA,IAAKM,GAAMN,EAAAA;AAAAA,MAGxDe,UAAUC,SAAS,EAAA,MACtBH,KAAW,CAACA,EAAAA,GAEPb,KAAI,GAAGA,KAAIe,UAAUC,QAAQhB,KACjCa,CAAAA,GAASI,KAAKF,GAAUf,EAAAA,CAAAA;AAAAA,MAGV,QAAZa,OACHC,GAAgBD,WAAWA,KAKT,cAAA,OAARD,MAA2C,QAArBA,GAAKM,aAAAA,MAChClB,MAAKY,GAAKM,aAAAA,YACVJ,GAAgBd,EAAAA,MACnBc,GAAgBd,EAAAA,IAAKY,GAAKM,aAAalB,EAAAA;AAAAA,SAKnCmB,EACNP,IACAE,IACAR,MAASA,GAAMc,KACfd,MAASA,GAAMe,KACf,IAAA;AAAA;AAgBK,SAASF,EAAYP,IAAMN,IAAOc,IAAKC,IAAKC,IAAAA;AAAAA,MAG5CC,KAAQ,EACbX,MAAAA,IACAN,OAAAA,IACAc,KAAAA,IACAC,KAAAA,IAAAA,KACW,MAAA,IACF,MAAA,KACD,GAAA,KACF,MAAA,KAAA,QAKIG,KACE,MACZC,aAAAA,QAAaD,KACFF,GAAAA;AAAAA,SAGI,QAAZA,OAAkBC,GAAAA,MAAkBA,KACpC7B,EAAQ6B,SAAO7B,EAAQ6B,MAAMA,EAAAA,GAE1BA;AAAAA;AAOD,SAASG,EAASC,IAAAA;AAAAA,SACjBA,GAAMC;AAAAA;AChFP,SAASC,EAAUF,IAAOG,IAAAA;AAAAA,OAC3BH,QAAQA,IAAAA,KACRG,UAAUA;AAAAA;AAqET,SAASC,EAAcC,IAAOC,IAAAA;AAAAA,MAClB,QAAdA,GAAAA,QAEID,GAAAA,KACJD,EAAcC,GAAAA,IAAeA,GAAAA,GAAAA,IAAwBE,QAAQF,EAAAA,IAAS,CAAA,IACtE;AAAA,WAGAG,IACGF,KAAaD,GAAAA,IAAgBI,QAAQH,KAAAA,KAG5B,SAFfE,KAAUH,GAAAA,IAAgBC,EAAAA,MAEa,QAAhBE,GAAAA,IAAAA,QAIfA,GAAAA;AAAAA,SASmB,cAAA,OAAdH,GAAMK,OAAqBN,EAAcC,EAAAA,IAAS;AAAA;AAsCjE,SAASM,EAAwBN,IAAAA;AAAjC,MAGWO,IACJC;AAAAA,MAHyB,SAA1BR,KAAQA,GAAAA,OAA8C,QAApBA,GAAAA,KAA0B;AAAA,SAChEA,GAAAA,MAAaA,GAAAA,IAAiBS,OAAO,MAC5BF,KAAI,GAAGA,KAAIP,GAAAA,IAAgBI,QAAQG,KAAAA,KAE9B,SADTC,KAAQR,GAAAA,IAAgBO,EAAAA,MACO,QAAdC,GAAAA,KAAoB;AACxCR,MAAAA,GAAAA,MAAaA,GAAAA,IAAiBS,OAAOD,GAAAA;AAAAA;IAAAA;AAAAA,WAKhCF,EAAwBN,EAAAA;EAAAA;AAAAA;AAoC1B,SAASU,EAAcC,IAAAA;AAAAA,GAAAA,CAE1BA,GAAAA,QACAA,GAAAA,MAAAA,SACDC,EAAcC,KAAKF,EAAAA,KAAAA,CAClBG,EAAAA,SACFC,MAAiBC,EAAQC,wBAEzBF,IAAeC,EAAQC,sBACNC,GAAOJ,CAAAA;AAAAA;AAK1B,SAASA,IAAAA;AAAAA,WACJK,IACIL,EAAAA,MAAyBF,EAAcR,SAC9Ce,CAAAA,KAAQP,EAAcQ,KAAK,SAACC,IAAGC,IAAAA;AAAAA,WAAMD,GAAAA,IAAAA,MAAkBC,GAAAA,IAAAA;EAAAA,CAAAA,GACvDV,IAAgB,CAAA,GAGhBO,GAAMI,KAAK,SAAAZ,IAAAA;AAnGb,QAAyBa,IAMnBC,IACEC,IAGFC,IATD3B,IACH4B,IACAC;AAiGKlB,IAAAA,GAAAA,QAlGLiB,MADG5B,MADoBwB,KAoGQb,IAAAA,KAAAA,MAjG/BkB,KAAYL,GAAAA,SAGRC,KAAc,CAAA,IACZC,KAAWI,EAAO,CAAA,GAAI9B,EAAAA,GAAAA,MACP0B,IAEjBC,KAASI,EACZF,IACA7B,IACA0B,IACAF,GAAAA,KAAAA,WACAK,GAAUG,iBACV,MACAP,IACU,QAAVG,KAAiB7B,EAAcC,EAAAA,IAAS4B,EAAAA,GAEzCK,EAAWR,IAAazB,EAAAA,GAEpB2B,MAAUC,MACbtB,EAAwBN,EAAAA;EAAAA,CAAAA;AAAAA;AIhH3B,SAAgBkC,EACfL,IACAM,IACAC,IACAC,IACAC,IACAC,IACAC,IACAf,IACAG,IACAa,IAAAA;AAVD,MAYKlC,IAAGmC,IAAGhB,IAAUiB,IAAYhB,IAAQiB,IAAeC,IAInDC,KAAeT,MAAkBA,GAAAA,OAA6BU,GAE9DC,KAAoBF,GAAY1C;AAAAA,OAMhCwB,MAAUqB,MAEZrB,KADwB,QAArBY,KACMA,GAAkB,CAAA,IACjBQ,KACDjD,EAAcsC,IAAgB,CAAA,IAE9B,OAIXD,GAAAA,MAA2B,CAAA,GACtB7B,KAAI,GAAGA,KAAI4B,GAAa/B,QAAQG,KAAAA,KAuClB,SAnCjBoC,KAAaP,GAAAA,IAAyB7B,EAAAA,IADrB,SAFlBoC,KAAaR,GAAa5B,EAAAA,MAEqB,aAAA,OAAdoC,KACW,OAKd,YAAA,OAAdA,MAA+C,YAAA,OAAdA,KACLO,EAC1C,MACAP,IACA,MACA,MACAA,EAAAA,IAESQ,MAAMC,QAAQT,EAAAA,IACmBO,EAC1CxD,GACA,EAAEE,UAAU+C,GAAAA,GACZ,MACA,MACA,IAAA,IAE4B,QAAnBA,GAAAA,OAAoD,QAAzBA,GAAAA,MACMO,EAC1CP,GAAWtC,MACXsC,GAAWhD,OACXgD,GAAWU,KACX,MACAV,GAAAA,GAAAA,IAG0CA,KAAAA;AAAAA,QAS5CA,GAAAA,KAAqBP,IACrBO,GAAAA,MAAoBP,GAAAA,MAAwB,GAS9B,UAHdV,KAAWoB,GAAYvC,EAAAA,MAIrBmB,MACAiB,GAAWU,OAAO3B,GAAS2B,OAC3BV,GAAWtC,SAASqB,GAASrB,KAE9ByC,CAAAA,GAAYvC,EAAAA,IAAAA;QAAK+C,MAIZZ,KAAI,GAAGA,KAAIM,IAAmBN,MAAK;AAAA,WACvChB,KAAWoB,GAAYJ,EAAAA,MAKtBC,GAAWU,OAAO3B,GAAS2B,OAC3BV,GAAWtC,SAASqB,GAASrB,MAC5B;AACDyC,QAAAA,GAAYJ,EAAAA,IAAAA;AAAKY;MAAAA;AAGlB5B,MAAAA,KAAW;IAAA;AAObC,IAAAA,KAASI,EACRF,IACAc,IALDjB,KAAWA,MAAYuB,GAOtBX,IACAC,IACAC,IACAf,IACAG,IACAa,EAAAA,IAGIC,KAAIC,GAAWY,QAAQ7B,GAAS6B,OAAOb,OACtCG,OAAMA,KAAO,CAAA,IACdnB,GAAS6B,OAAKV,GAAKhC,KAAKa,GAAS6B,KAAK,MAAMZ,EAAAA,GAChDE,GAAKhC,KAAK6B,IAAGC,GAAAA,OAAyBhB,IAAQgB,EAAAA,IAGjC,QAAVhB,MACkB,QAAjBiB,OACHA,KAAgBjB,KAGjBC,KAAS4B,EACR3B,IACAc,IACAjB,IACAoB,IACAN,IACAb,IACAC,EAAAA,GAa0B,YAAvBQ,GAAe/B,OAClBwB,GAAU4B,QAAQ,KACsB,cAAA,OAAvBrB,GAAe/B,SAQhC+B,GAAAA,MAA0BR,OAG3BA,MACAF,GAAAA,OAAiBE,MACjBA,GAAO8B,cAAc7B,OAIrBD,KAAS7B,EAAc2B,EAAAA;EAAAA;AAAAA,MAIzBU,GAAAA,MAAsBQ,IAGG,QAArBJ,MAA2D,cAAA,OAAvBJ,GAAe/B,KAAAA,MACjDE,KAAIiC,GAAkBpC,QAAQG,OACN,SAAxBiC,GAAkBjC,EAAAA,KAAYoD,EAAWnB,GAAkBjC,EAAAA,CAAAA;AAAAA,OAK5DA,KAAIyC,IAAmBzC,OACL,SAAlBuC,GAAYvC,EAAAA,KAAYqD,EAAQd,GAAYvC,EAAAA,GAAIuC,GAAYvC,EAAAA,CAAAA;AAAAA,MAI7DsC,GAAAA,MACEtC,KAAI,GAAGA,KAAIsC,GAAKzC,QAAQG,KAC5BsD,GAAShB,GAAKtC,EAAAA,GAAIsC,GAAAA,EAAOtC,EAAAA,GAAIsC,GAAAA,EAAOtC,EAAAA,CAAAA;AAAAA;AAqBhC,SAASuD,EACfC,IACAC,IACAC,IACAC,IACAC,IACAC,IACAC,IAAAA;AAPM,MASFC,IA2BGC,IAAiBC;AAAAA,MAAAA,WA1BpBR,GAAAA,IAIHM,CAAAA,KAAUN,GAAAA,KAMVA,GAAAA,MAAAA;WAEAG,MAAqBF,MACrBG,MAAUC,MACW,QAArBD,GAAOK,WAMPC,GAAO,KAAc,QAAVL,MAAkBA,GAAOI,eAAeV,GAClDA,CAAAA,GAAUY,YAAYP,EAAAA,GACtBE,KAAU;OACJ;AAAA,SAGDC,KAASF,IAAQG,KAAI,IACxBD,KAASA,GAAOK,gBAAgBJ,KAAIN,GAAYW,QACjDL,MAAK,EAAA,KAEDD,MAAUH,GAAAA,OACPM;AAGRX,IAAAA,GAAUe,aAAaV,IAAQC,EAAAA,GAC/BC,KAAUD;EAAAA;AAAAA,SAAAA,WAORC,KACMA,KAEAF,GAAOQ;AAAAA;ACjSX,SAASG,EAAUC,IAAKC,IAAUC,IAAUC,IAAOC,IAAAA;AAAAA,MACrDC;AAAAA,OAECA,MAAKH,GACC,gBAANG,MAA0B,UAANA,MAAiBA,MAAKJ,MAC7CK,EAAYN,IAAKK,IAAG,MAAMH,GAASG,EAAAA,GAAIF,EAAAA;AAAAA,OAIpCE,MAAKJ,GAENG,CAAAA,MAAiC,cAAA,OAAfH,GAASI,EAAAA,KACvB,eAANA,MACM,UAANA,MACM,YAANA,MACM,cAANA,MACAH,GAASG,EAAAA,MAAOJ,GAASI,EAAAA,KAEzBC,EAAYN,IAAKK,IAAGJ,GAASI,EAAAA,GAAIH,GAASG,EAAAA,GAAIF,EAAAA;AAAAA;AAKjD,SAASI,EAASC,IAAOC,IAAKC,IAAAA;AACd,UAAXD,GAAI,CAAA,IACPD,GAAMF,YAAYG,IAAKC,EAAAA,IAKvBF,GAAMC,EAAAA,IAHU,YAAA,OAATC,MAAAA,UACPC,EAAmBC,KAAKH,EAAAA,IAEXC,KAAQ,OACF,QAATA,KACG,KAEAA;AAAAA;AAYR,SAASJ,EAAYN,IAAKa,IAAMH,IAAOI,IAAUX,IAAAA;AAAjD,MACFY,IAAGC,IAAYC,IAsBPZ,IAQAA;AAAAA,MA5BRF,KACU,gBAATU,OACHA,KAAO,WAEW,YAATA,OACVA,KAAO,cAGK,YAATA,GAAAA,KACHE,KAAIf,GAAIQ,OAEY,YAAA,OAATE,GACVK,CAAAA,GAAEG,UAAUR;OACN;AAAA,QACiB,YAAA,OAAZI,OACVC,GAAEG,UAAU,IACZJ,KAAW,OAGRA,GAAAA,MACMT,MAAKS,GACPJ,CAAAA,MAASL,MAAKK,MACnBH,EAASQ,IAAGV,IAAG,EAAA;AAAA,QAKdK,GAAAA,MACML,MAAKK,GACRI,CAAAA,MAAYJ,GAAML,EAAAA,MAAOS,GAAST,EAAAA,KACtCE,EAASQ,IAAGV,IAAGK,GAAML,EAAAA,CAAAA;EAAAA;MAOL,SAAZQ,GAAK,CAAA,KAA0B,QAAZA,GAAK,CAAA,KAChCG,KAAaH,QAAUA,KAAOA,GAAKM,QAAQ,YAAY,EAAA,IACvDF,KAAYJ,GAAKO,YAAAA,GACjBP,MAAQI,MAAajB,KAAMiB,KAAYJ,IAAMQ,MAAM,CAAA,GAE/CX,MACEI,MAAUd,GAAIsB,iBAAiBT,IAAMU,GAAYP,EAAAA,IACrDhB,GAAIwB,MAAexB,GAAIwB,IAAa,CAAA,IAAKX,EAAAA,IAAQH,MAElDV,GAAIyB,oBAAoBZ,IAAMU,GAAYP,EAAAA,KAGlC,WAATH,MACS,cAATA,MAGS,WAATA,MACS,WAATA,MACS,WAATA,MAAAA,CACCV,MACDU,MAAQb,KAERA,GAAIa,EAAAA,IAAiB,QAATH,KAAgB,KAAKA,KACP,cAAA,OAATA,MAAgC,8BAATG,OACpCA,QAAUA,KAAOA,GAAKM,QAAQ,YAAY,EAAA,KAChC,QAATT,MAAAA,UAAiBA,KACpBV,GAAI0B,kBACH,gCACAb,GAAKO,YAAAA,CAAAA,IAGNpB,GAAI2B,eACH,gCACAd,GAAKO,YAAAA,GACLV,EAAAA,IAIO,QAATA,MAAAA,UACCA,MAAAA,CAOC,MAAME,KAAKC,EAAAA,IAEbb,GAAI4B,gBAAgBf,EAAAA,IAEpBb,GAAI6B,aAAahB,IAAMH,EAAAA;AAAAA;AAU1B,SAASa,EAAWO,IAAAA;AAAAA,OACdN,EAAWM,GAAEC,IAAAA,EAAMC,EAAQC,QAAQD,EAAQC,MAAMH,EAAAA,IAAKA,EAAAA;AAAAA;AAAAA,SCvJnDI,EAAgBC,IAAU9C,IAAQN,IAAAA;AAAAA,MACjCqD,IACFC;AAAAA,OADED,KAAM,GAAGA,KAAMD,GAAAA,IAAmBtC,QAAQuC,KAAAA,EAC5CC,KAAQF,GAAAA,IAAmBC,EAAAA,OAEhCC,GAAAA,KAAgBF,IAEZE,GAAAA,QACsB,cAAA,OAAdA,GAAMN,QAAsBM,GAAAA,IAAgBxC,SAAS,KAC/DqC,EAAgBG,IAAOhD,IAAQN,EAAAA,GAGhCM,KAASP,EACRC,IACAsD,IACAA,IACAF,GAAAA,KACA,MACAE,GAAAA,KACAhD,EAAAA,GAG2B,cAAA,OAAjB8C,GAASJ,SACnBI,GAAAA,MAAoB9C;AAAAA;AAuBzB,SAAgBiD,EACfvD,IACAoD,IACAlD,IACAsD,IACApC,IACAhB,IACAqD,IACAnD,IACAoD,IAAAA;AATD,MAWKL,IAWEM,IAAGC,IAAOzC,IAAU0C,IAAUC,IAAUC,IACxC7C,IAKA8C,IACAC,IAiIAC,IAlJLC,KAAUf,GAASJ;AAAAA,MAAAA,WAIhBI,GAASgB,YAA2B,QAAO;AAAA,GAE1Cf,KAAMJ,EAAAA,QAAgBI,GAAID,EAAAA;AAAAA,MAAAA;AAG9BzC,MAAO,KAAsB,cAAA,OAAXwD,IAAuB;AAAA,UAEpCjD,KAAWkC,GAASiB,OAKpBL,MADJX,KAAMc,GAAQG,gBACQd,GAAcH,GAAAA,GAAAA,GAChCY,KAAmBZ,KACpBW,KACCA,GAASK,MAAM1C,QACf0B,GAAAA,KACDG,IAGCtD,GAAAA,MAEH6D,MADAJ,KAAIP,GAAAA,MAAsBlD,GAAAA,KAAAA,KAC0ByD,GAAAA,OAGhD,eAAeQ,MAAWA,GAAQI,UAAUC,SAC/CpB,GAAAA,MAAsBO,KAAI,IAAIQ,GAAQjD,IAAU+C,EAAAA,KAEhDb,GAAAA,MAAsBO,KAAI,IAAIc,EAAUvD,IAAU+C,EAAAA,GAClDN,GAAES,cAAcD,IAChBR,GAAEa,SAASE,IAERV,MAAUA,GAASW,IAAIhB,EAAAA,GAE3BA,GAAEU,QAAQnD,IACLyC,GAAEiB,UAAOjB,GAAEiB,QAAQ,CAAA,IACxBjB,GAAEkB,UAAUZ,IACZN,GAAAA,MAAmBH,IACnBI,KAAQD,GAAAA,MAAAA,MACRA,GAAAA,MAAqB,CAAA,IAIF,QAAhBA,GAAAA,QACHA,GAAAA,MAAeA,GAAEiB,QAEsB,QAApCT,GAAQW,6BACPnB,GAAAA,OAAgBA,GAAEiB,UACrBjB,GAAAA,MAAeoB,EAAO,CAAA,GAAIpB,GAAAA,GAAAA,IAG3BoB,EACCpB,GAAAA,KACAQ,GAAQW,yBAAyB5D,IAAUyC,GAAAA,GAAAA,CAAAA,IAI7CxC,KAAWwC,GAAEU,OACbR,KAAWF,GAAEiB,OAGThB,GAEkC,SAApCO,GAAQW,4BACgB,QAAxBnB,GAAEqB,sBAEFrB,GAAEqB,mBAAAA,GAGwB,QAAvBrB,GAAEsB,qBACLtB,GAAAA,IAAmBuB,KAAKvB,GAAEsB,iBAAAA;WAErB;AAAA,YAE+B,QAApCd,GAAQW,4BACR5D,OAAaC,MACkB,QAA/BwC,GAAEwB,6BAEFxB,GAAEwB,0BAA0BjE,IAAU+C,EAAAA,GAAAA,CAIpCN,GAAAA,OAC0B,QAA3BA,GAAEyB,yBAAAA,UACFzB,GAAEyB,sBACDlE,IACAyC,GAAAA,KACAM,EAAAA,KAEFb,GAAAA,QAAuBlD,GAAAA,KACtB;AACDyD,UAAAA,GAAEU,QAAQnD,IACVyC,GAAEiB,QAAQjB,GAAAA,KAENP,GAAAA,QAAuBlD,GAAAA,QAAoByD,GAAAA,MAAAA,QAC/CA,GAAAA,MAAWP,IACXA,GAAAA,MAAgBlD,GAAAA,KAChBkD,GAAAA,MAAqBlD,GAAAA,KACjByD,GAAAA,IAAmB7C,UACtB2C,GAAYyB,KAAKvB,EAAAA,GAGlBR,EAAgBC,IAAU9C,IAAQN,EAAAA;AAAAA,gBAC5BW;QAAAA;AAGsB,gBAAzBgD,GAAE0B,uBACL1B,GAAE0B,oBAAoBnE,IAAUyC,GAAAA,KAAcM,EAAAA,GAGnB,QAAxBN,GAAE2B,sBACL3B,GAAAA,IAAmBuB,KAAK,WAAA;AACvBvB,UAAAA,GAAE2B,mBAAmBnE,IAAU0C,IAAUC,EAAAA;QAAAA,CAAAA;MAAAA;AAK5CH,MAAAA,GAAEkB,UAAUZ,IACZN,GAAEU,QAAQnD,IACVyC,GAAEiB,QAAQjB,GAAAA,MAELN,KAAMJ,EAAAA,QAAkBI,GAAID,EAAAA,GAEjCO,GAAAA,MAAAA,OACAA,GAAAA,MAAWP,IACXO,GAAAA,MAAe3D,IAEfqD,KAAMM,GAAEa,OAAOb,GAAEU,OAAOV,GAAEiB,OAAOjB,GAAEkB,OAAAA,GAGnClB,GAAEiB,QAAQjB,GAAAA,KAEe,QAArBA,GAAE4B,oBACL/B,KAAgBuB,EAAOA,EAAO,CAAA,GAAIvB,EAAAA,GAAgBG,GAAE4B,gBAAAA,CAAAA,IAGhD3B,MAAsC,QAA7BD,GAAE6B,4BACf1B,KAAWH,GAAE6B,wBAAwBrE,IAAU0C,EAAAA,IAK5CK,KADI,QAAPb,MAAeA,GAAIL,QAAQyC,KAAuB,QAAXpC,GAAI3B,MACJ2B,GAAIgB,MAAMqB,WAAWrC,IAE7DsC,EACC3F,IACA4F,MAAMC,QAAQ3B,EAAAA,IAAgBA,KAAe,CAACA,EAAAA,GAC9Cd,IACAlD,IACAsD,IACApC,IACAhB,IACAqD,IACAnD,IACAoD,EAAAA,GAGDC,GAAEmC,OAAO1C,GAAAA,KAELO,GAAAA,IAAmB7C,UACtB2C,GAAYyB,KAAKvB,EAAAA,GAGdI,OACHJ,GAAAA,MAAkBA,GAAAA,KAAyB,OAG5CA,GAAAA,MAAAA;IAAW,MAEU,SAArBvD,MACAgD,GAAAA,QAAuBlD,GAAAA,OAEvBkD,GAAAA,MAAqBlD,GAAAA,KACrBkD,GAAAA,MAAgBlD,GAAAA,OAEhBkD,GAAAA,MAAgB2C,EACf7F,GAAAA,KACAkD,IACAlD,IACAsD,IACApC,IACAhB,IACAqD,IACAC,EAAAA;AAAAA,KAIGL,KAAMJ,EAAQ+C,WAAS3C,GAAID,EAAAA;EAAAA,SACxBL,IAAAA;AACRK,IAAAA,GAAAA,MAAqB,MACrBH,EAAAA,IAAoBF,IAAGK,IAAUlD,EAAAA;EAAAA;AAAAA,SAG3BkD,GAAAA;AAAAA;AAQD,SAAS6C,EAAWxC,IAAayC,IAAAA;AACnCjD,IAAAA,OAAiBA,EAAAA,IAAgBiD,IAAMzC,EAAAA,GAE3CA,GAAY0C,KAAK,SAAAxC,IAAAA;AAAAA,QAAAA;AAEfF,MAAAA,KAAcE,GAAAA,KACdA,GAAAA,MAAqB,CAAA,GACrBF,GAAY0C,KAAK,SAAAC,IAAAA;AAChBA,QAAAA,GAAGC,KAAK1C,EAAAA;MAAAA,CAAAA;IAAAA,SAEDZ,IAAAA;AACRE,QAAAA,IAAoBF,IAAGY,GAAAA,GAAAA;IAAAA;EAAAA,CAAAA;AAAAA;AAmB1B,SAASoC,EACR9E,IACAmC,IACAlD,IACAsD,IACApC,IACAhB,IACAqD,IACAC,IAAAA;AARD,MAUKpC,IASIgF,IA+CHC,IACAC,IASOlF,IAjERH,KAAWjB,GAASmE,OACpBnD,KAAWkC,GAASiB;AAAAA,MAGxBjD,KAA0B,UAAlBgC,GAASJ,QAAkB5B,IAEV,QAArBhB;AAAAA,SACEkB,KAAI,GAAGA,KAAIlB,GAAkBU,QAAQQ,KAAAA,KAO/B,SANJgF,KAAQlG,GAAkBkB,EAAAA,QAOX,SAAlB8B,GAASJ,OACW,MAAnBsD,GAAMG,WACNH,GAAMI,cAActD,GAASJ,SAC/B/B,MAAOqF,KACP;AACDrF,MAAAA,KAAMqF,IACNlG,GAAkBkB,EAAAA,IAAK;AAAA;IAAA;;AAAA,MAMf,QAAPL,IAAa;AAAA,QACM,SAAlBmC,GAASJ,KAAAA,QACL2D,SAASC,eAAe1F,EAAAA;AAGhCD,IAAAA,KAAMG,KACHuF,SAASE,gBAAgB,8BAA8BzD,GAASJ,IAAAA,IAChE2D,SAASG,cACT1D,GAASJ,MACT9B,GAAS6F,MAAM,EAAEA,IAAI7F,GAAS6F,GAAAA,CAAAA,GAGjC3G,KAAoB,MAEpBsD,KAAAA;EAAc;AAAA,MAGO,SAAlBN,GAASJ,KACR7B,CAAAA,OAAaD,MAAYD,GAAI+F,QAAQ9F,OACxCD,GAAI+F,OAAO9F;OAEN;AAAA,QACmB,QAArBd,OACHA,KAAoB6G,EAAU3E,MAAM+D,KAAKpF,GAAIiG,UAAAA,IAK1CX,MAFJpF,KAAWjB,GAASmE,SAAS8C,GAENC,yBACnBZ,KAAUtF,GAASkG,yBAAAA,CAIlB1D,IAAa;AAAA,UAGQ,QAArBtD,GAAAA,MACHe,KAAW,CAAA,GACFG,KAAI,GAAGA,KAAIL,GAAIoG,WAAWvG,QAAQQ,KAC1CH,CAAAA,GAASF,GAAIoG,WAAW/F,EAAAA,EAAGQ,IAAAA,IAAQb,GAAIoG,WAAW/F,EAAAA,EAAGK;AAAAA,OAInD6E,MAAWD,QAETC,MAAYD,MAAWC,GAAAA,UAAkBD,GAAAA,WAC7CtF,GAAIqG,YAAad,MAAWA,GAAAA,UAAmB;IAAA;AAKlDxF,MAAUC,IAAKC,IAAUC,IAAUC,IAAOsC,EAAAA,GAGtC8C,KACHpD,GAAAA,MAAqB,CAAA,KAErB9B,KAAI8B,GAASiB,MAAMqB,UACnBC,EACC1E,IACA2E,MAAMC,QAAQvE,EAAAA,IAAKA,KAAI,CAACA,EAAAA,GACxB8B,IACAlD,IACAsD,IACkB,oBAAlBJ,GAASJ,QAAmC5B,IAC5ChB,IACAqD,IACA0D,GACAzD,EAAAA,IAKGA,OAEH,WAAWxC,MAAAA,YACVI,KAAIJ,GAASS,UACdL,OAAML,GAAIU,SAEVJ,EAAYN,IAAK,SAASK,IAAGH,GAASQ,OAAAA,KAAO,GAG7C,aAAaT,MAAAA,YACZI,KAAIJ,GAASqG,YACdjG,OAAML,GAAIsG,WAEVhG,EAAYN,IAAK,WAAWK,IAAGH,GAASoG,SAAAA,KAAS;EAAA;AAAA,SAK7CtG;AAAAA;AASR,SAAgBuG,EAASC,IAAK9F,IAAO2B,IAAAA;AAAAA,MAAAA;AAEjB,kBAAA,OAAPmE,KAAmBA,GAAI9F,EAAAA,IAC7B8F,GAAIC,UAAU/F;EAAAA,SACXoB,IAAAA;AACRE,MAAAA,IAAoBF,IAAGO,EAAAA;EAAAA;AAAAA;AAYzB,SAAgBqE,EAAQrE,IAAOsE,IAAaC,IAAAA;AAA5C,MACKC,IAOA7G,IAsBMK;AAAAA,MA5BN2B,EAAQ0E,WAAS1E,EAAQ0E,QAAQrE,EAAAA,IAEhCwE,KAAIxE,GAAMmE,SACTK,GAAEJ,WAAWI,GAAEJ,YAAYpE,GAAAA,OAAYkE,EAASM,IAAG,MAAMF,EAAAA,IAI1DC,MAAmC,cAAA,OAAdvE,GAAMN,SAC/B6E,KAAmC,SAArB5G,KAAMqC,GAAAA,OAKrBA,GAAAA,MAAaA,GAAAA,MAAAA,QAEiB,SAAzBwE,KAAIxE,GAAAA,MAA2B;AAAA,QAC/BwE,GAAEC,qBAAAA,KAAAA;AAEJD,MAAAA,GAAEC,qBAAAA;IAAAA,SACMhF,IAAAA;AACRE,QAAAA,IAAoBF,IAAG6E,EAAAA;IAAAA;AAIzBE,IAAAA,GAAEhC,OAAOgC,GAAAA,MAAe;EAAA;AAAA,MAGpBA,KAAIxE,GAAAA,IAAAA,MACChC,KAAI,GAAGA,KAAIwG,GAAEhH,QAAQQ,KACzBwG,CAAAA,GAAExG,EAAAA,KAAIqG,EAAQG,GAAExG,EAAAA,GAAIsG,IAAaC,EAAAA;AAI5B,UAAP5G,MAAa+G,EAAW/G,EAAAA;AAAAA;AAI7B,SAASyD,EAASL,IAAOO,IAAOC,IAAAA;AAAAA,SACxBoD,KAAK7D,YAAYC,IAAOQ,EAAAA;AAAAA;ALhehC,SAAgBL,EAAOlB,IAAOtD,IAAWkI,IAAAA;AAAzC,MAMKxE,IAOAxD,IAMAuD;AAlBAR,IAAAA,MAAeA,EAAAA,GAAcK,IAAOtD,EAAAA,GAYpCE,MAPAwD,KAAcwE,OAAgBC,KAQ/B,OACCD,MAAeA,GAAAA,OAA0BlI,GAAAA,KAC7CsD,KAAQwD,EAAcrB,GAAU,MAAM,CAACnC,EAAAA,CAAAA,GAGnCG,KAAc,CAAA,GAClBF,EACCvD,KAGE0D,KAAc1D,KAAYkI,MAAelI,IAAAA,MAAuBsD,IAClEpD,MAAYiH,GACZA,GAAAA,WACAnH,GAAUoI,iBACVF,MAAAA,CAAgBxE,KACb,CAACwE,EAAAA,IACDhI,KACA,OACAF,GAAUkH,WAAWpG,SACrBmG,EAAU3E,MAAM+D,KAAKrG,GAAUkH,UAAAA,IAC/B,MACHzD,IACAyE,MAAef,GACfzD,EAAAA,GAIDuC,EAAWxC,IAAaH,EAAAA;AAAAA;AH7CnB+E,IAAU,EAAA,KUFT,SAAqBC,IAAOC,IAAAA;AAAAA,WAE9BC,IAAWC,IAEPF,KAAQA,GAAAA,KAAAA,MACVC,KAAYD,GAAAA,QAAAA,CAAsBC,GAAAA,GAAAA,KAAAA;AAAAA,QAGpCA,GAAUE,eACwC,QAAlDF,GAAUE,YAAYC,6BAEtBF,KAAAA,MACAD,GAAUI,SACTJ,GAAUE,YAAYC,yBAAyBL,EAAAA,CAAAA,IAId,QAA/BE,GAAUK,sBACbJ,KAAAA,MACAD,GAAUK,kBAAkBP,EAAAA,IAGzBG,GACH,QAAOK,EAAeN,GAAAA,MAA0BA,EAAAA;EAAAA,SACzCO,IAAAA;AACRT,IAAAA,KAAQS;EAAAA;AAAAA,QAKLT;AAAAA,EAAAA,GT6DMU,IAAiB,SAAAT,IAAAA;AAAAA,SACpB,QAATA,MAAAA,WAAiBA,GAAMG;AAAAA,GC5ExBO,EAAUC,UAAUN,WAAW,SAASO,IAAQC,IAAAA;AAAAA,MAE3CC;AAEHA,EAAAA,KADGC,KAAAA,QAAoBA,KAAKC,QACxBD,KAAAA,MAEAA,KAAAA,MAAkBE,EAAO,CAAA,GAAIF,KAAKC,KAAAA,GAGlB,cAAA,OAAVJ,OACVA,KAASA,GAAOE,IAAGC,KAAKG,KAAAA,IAGrBN,MACHK,EAAOH,IAAGF,EAAAA,GAIG,QAAVA,MAEAG,KAAAA,QACCF,MAAUE,KAAAA,IAAsBI,KAAKN,EAAAA,GACzCN,EAAcQ,IAAAA;AAAAA,GAShBL,EAAUC,UAAUS,cAAc,SAASP,IAAAA;AACtCE,OAAAA,QAAAA,KAAAA,MAAAA,MAKCF,MAAUE,KAAAA,IAAsBI,KAAKN,EAAAA,GACzCN,EAAcQ,IAAAA;AAAAA,GAchBL,EAAUC,UAAUU,SAASC,GAwFzBC,IAAgB,CAAA,GAQdC,IACa,cAAA,OAAXC,UACJA,QAAQd,UAAUe,KAAKC,KAAKF,QAAQG,QAAAA,CAAAA,IACpCC,YA2CJC,EAAAA,MAAyB,GCtNnBC,IAAaC,GCHRC,IAAI;;;AOCf,IAAIC;AAAJ,IAGIC;AAHJ,IAiBIC;AAjBJ,IAMIC,KAAc;AANlB,IASIC,KAAoB,CAAA;AATxB,IAWIC,KAAkBC,EAAAA;AAXtB,IAYIC,KAAeD,EAAQE;AAZ3B,IAaIC,KAAYH,EAAAA;AAbhB,IAcII,KAAmBJ,EAAQK;AAkE/B,SAASC,GAAaC,IAAOC,IAAAA;AACxBR,IAAAA,OACHA,EAAAA,IAAcL,IAAkBY,IAAOV,MAAeW,EAAAA,GAEvDX,KAAc;AAAA,MAORY,KACLd,GAAAA,QACCA,GAAAA,MAA2B,EAAA,IACpB,CAAA,GAAA,KACU,CAAA,EAAA;AAAA,SAGfY,MAASE,GAAAA,GAAYC,UACxBD,GAAAA,GAAYE,KAAK,CAAA,CAAA,GAEXF,GAAAA,GAAYF,EAAAA;AAAAA;AAMb,SAASK,GAASC,IAAAA;AAAAA,SACxBhB,KAAc,GACPiB,GAAWC,IAAgBF,EAAAA;AAAAA;AASnC,SAAgBC,GAAWE,IAASH,IAAcI,IAAAA;AAAAA,MAE3CC,KAAYZ,GAAaZ,MAAgB,CAAA;AAAA,SAC/CwB,GAAUC,IAAWH,IAChBE,GAAAA,QACJA,GAAAA,MAAuBvB,IAEvBuB,GAAAA,KAAmB,CACjBD,KAAiDA,GAAKJ,EAAAA,IAA/CE,GAAAA,QAA0BF,EAAAA,GAElC,SAAAO,IAAAA;AAAAA,QACOC,KAAYH,GAAUC,EAASD,GAAAA,GAAiB,CAAA,GAAIE,EAAAA;AACtDF,IAAAA,GAAAA,GAAiB,CAAA,MAAOG,OAC3BH,GAAAA,KAAmB,CAACG,IAAWH,GAAAA,GAAiB,CAAA,CAAA,GAChDA,GAAAA,IAAqBI,SAAS,CAAA,CAAA;EAAA,CAAA,IAM3BJ,GAAAA;AAAAA;AAOD,SAASK,EAAUC,IAAUC,IAAAA;AAAAA,MAE7BC,KAAQpB,GAAaZ,MAAgB,CAAA;AAAA,GACtCM,EAAAA,OAAwB2B,GAAYD,GAAAA,KAAaD,EAAAA,MACrDC,GAAAA,KAAeF,IACfE,GAAAA,MAAcD,IAEd9B,GAAAA,IAAAA,IAAyCgB,KAAKe,EAAAA;AAAAA;AA4CzC,SAASE,GAAQC,IAASC,IAAAA;AAAAA,MAE1BC,KAAQC,GAAaC,MAAgB,CAAA;AAAA,SACvCC,GAAYH,GAAAA,KAAaD,EAAAA,KAC5BC,GAAAA,MAAcD,IACdC,GAAAA,MAAiBF,IACTE,GAAAA,KAAeF,GAAAA,KAGjBE,GAAAA;AAAAA;AAiER,SAASI,IAAAA;AACRC,EAAAA,GAAkBC,KAAK,SAAAC,IAAAA;AAAAA,QAClBA,GAAAA,IAAAA,KAAAA;AAEFA,MAAAA,GAAAA,IAAAA,IAAkCC,QAAQC,CAAAA,GAC1CF,GAAAA,IAAAA,IAAkCC,QAAQE,EAAAA,GAC1CH,GAAAA,IAAAA,MAAoC,CAAA;IAAA,SAC5BI,IAAAA;AAAAA,aACRJ,GAAAA,IAAAA,MAAoC,CAAA,GACpCK,EAAAA,IAAoBD,IAAGJ,GAAAA,GAAAA,GAAAA;IAChB;EAAA,CAAA,GAIVF,KAAoB,CAAA;AAAA;AAzQrBO,EAAAA,MAAkB,SAAAC,IAAAA;AACbC,EAAAA,MAAiBA,GAAgBD,EAAAA,GAGrCE,KAAe;AAAA,MAETC,MAHNC,KAAmBJ,GAAAA,KAAAA;AAIfG,EAAAA,OACHA,GAAAA,IAAsBR,QAAQC,CAAAA,GAC9BO,GAAAA,IAAsBR,QAAQE,EAAAA,GAC9BM,GAAAA,MAAwB,CAAA;AAAA,GAI1BJ,EAAQM,SAAS,SAAAL,IAAAA;AACZM,EAAAA,MAAcA,GAAaN,EAAAA;AAAAA,MAEzBO,KAAIP,GAAAA;AACNO,EAAAA,MAAKA,GAAAA,OAAaA,GAAAA,IAAAA,IAA0BC,WA4RzB,MA3RXhB,GAAkBiB,KAAKF,EAAAA,KA2RPG,OAAYX,EAAQY,2BAC/CD,KAAUX,EAAQY,0BAvBpB,SAAwBC,IAAAA;AAAAA,QAQnBC,IAPEC,KAAO,WAAA;AACZC,mBAAaC,EAAAA,GACTC,MAASC,qBAAqBL,EAAAA,GAClCM,WAAWP,EAAAA;IAAAA,GAENI,KAAUG,WAAWL,IAjSR,GAAA;AAoSfG,IAAAA,OACHJ,KAAMF,sBAAsBG,EAAAA;EAAAA,GAcAvB,CAAAA;AAAAA,GAzR9BQ,EAAAA,MAAkB,SAACC,IAAOoB,IAAAA;AACzBA,EAAAA,GAAY3B,KAAK,SAAAC,IAAAA;AAAAA,QAAAA;AAEfA,MAAAA,GAAAA,IAA2BC,QAAQC,CAAAA,GACnCF,GAAAA,MAA6BA,GAAAA,IAA2B2B,OAAO,SAAAC,IAAAA;AAAAA,eAAAA,CAC9DA,GAAAA,MAAYzB,GAAayB,EAAAA;MAAAA,CAAAA;IAAAA,SAElBxB,IAAAA;AACRsB,MAAAA,GAAY3B,KAAK,SAAAc,IAAAA;AACZA,QAAAA,GAAAA,QAAoBA,GAAAA,MAAqB,CAAA;MAAA,CAAA,GAE9Ca,KAAc,CAAA,GACdrB,EAAAA,IAAoBD,IAAGJ,GAAAA,GAAAA;IAAAA;EAAAA,CAAAA,GAIrB6B,MAAWA,GAAUvB,IAAOoB,EAAAA;AAAAA,GAGjCrB,EAAQyB,UAAU,SAAAxB,IAAAA;AACbyB,EAAAA,MAAkBA,GAAiBzB,EAAAA;AAAAA,MAEjCO,KAAIP,GAAAA;AAAAA,MACNO,MAAKA,GAAAA,IAAAA,KAAAA;AAEPA,IAAAA,GAAAA,IAAAA,GAAgBZ,QAAQC,CAAAA;EAAAA,SAChBE,IAAAA;AACRC,MAAAA,IAAoBD,IAAGS,GAAAA,GAAAA;EAAAA;AAAAA;AA0N1B,IAAIU,KAA0C,cAAA,OAAzBN;AA2CrB,SAASf,EAAc8B,IAAAA;AACM,gBAAA,OAAjBA,GAAKC,KAAwBD,GAAKC,EAAAA;AAAAA;AAO9C,SAAS9B,GAAa6B,IAAAA;AACrBA,EAAAA,GAAKC,IAAWD,GAAAA,GAAAA;AAAAA;AAOjB,SAASE,GAAYC,IAASC,IAAAA;AAAAA,SAAAA,CACrBD,MAAWC,GAAQrC,KAAK,SAACsC,IAAKC,IAAAA;AAAAA,WAAUD,OAAQF,GAAQG,EAAAA;EAAAA,CAAAA;AAAAA;AAGjE,SAASC,GAAeF,IAAKG,IAAAA;AAAAA,SACT,cAAA,OAALA,KAAkBA,GAAEH,EAAAA,IAAOG;AAAAA;;;AC9V1C,IAAM,YAAoC,KAAK,MAAM;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA+yDnD;AAGK,SAAS,UAAU,KAAqB;AAC7C,SAAO,IAAI,QAAQ,oBAAoB,CAACC,IAAG,OAAO;AAChD,WAAO,UAAU,EAAE,KAAKA;AAAA,EAC1B,CAAC;AACH;;;ACpzDO,IAAM,gBAAgB,MAC5B;AAAA,EAAC;AAAA;AAAA,IACA,WAAU;AAAA,IACV,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IACP,eAAY;AAAA;AAAA,EAEZ;AAAA,IAAC;AAAA;AAAA,MACA,aAAU;AAAA,MACV,GAAE;AAAA;AAAA,EACF;AACF;AAIM,IAAM,kBAAkB,MAC9B;AAAA,EAAC;AAAA;AAAA,IACA,WAAU;AAAA,IACV,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IACP,eAAY;AAAA;AAAA,EAEZ;AAAA,IAAC;AAAA;AAAA,MACA,aAAU;AAAA,MACV,GAAE;AAAA;AAAA,EACF;AACF;AAIM,IAAM,SAAS,MACrB;AAAA,EAAC;AAAA;AAAA,IACA,WAAU;AAAA,IACV,SAAQ;AAAA,IACR,SAAQ;AAAA,IACR,OAAM;AAAA,IACN,QAAO;AAAA,IACP,eAAY;AAAA;AAAA,EAEZ;AAAA,IAAC;AAAA;AAAA,MACA,aAAU;AAAA,MACV,GAAE;AAAA;AAAA,EACF;AACF;;;AC3CD,IAAM,kBAAkB;AAEjB,IAAM,WAAuG,CAAC,EAAE,OAAO,SAAS,MAAM;AAC5I,QAAM,CAAC,QAAQ,SAAS,IAAIC,GAAmB,CAAC,CAAC;AACjD,QAAM,QAAQC,GAAQ,MAAM;AAC3B,UAAM,OAAO,oBAAI,IAAY;AAC7B,WAAO,SAAS,OAAO,UAAQ;AAC9B,UAAI,OAAO,SAAS,KAAK,EAAE,GAAG;AAC7B,eAAO;AAAA,MACR;AAEA,UAAI,KAAK,IAAI,KAAK,GAAG,GAAG;AACvB,eAAO;AAAA,MACR;AAEA,WAAK,IAAI,KAAK,GAAG;AACjB,aAAO;AAAA,IACR,CAAC;AAAA,EACF,GAAG,CAAC,UAAU,MAAM,CAAC;AAGrB,IAAU,MAAM,UAAU,CAAC,CAAC,GAAG,CAAC,QAAQ,CAAC;AAEzC,QAAM,eAAe,MAAM,KAAK,UAAQ,KAAK,mBAAmB,MAAM,CAAC,EAAE,cAAc;AACvF,QAAM,aAAa,CAAC,SACnB,kBAAC,QAAK,KAAK,KAAK,IAAI,MAAY,MAAM,MAAM,UAAU,CAAC,GAAG,QAAQ,KAAK,EAAE,CAAC,GAAG,UAAU,cAAc;AAEtG,MAAI,MAAM,UAAU,iBAAiB;AACpC,WAAO,kBAAC,aAAK,MAAM,IAAI,UAAU,CAAE;AAAA,EACpC;AAEA,QAAM,CAAC,WAAW,YAAY,IAAID,GAAS,IAAI;AAC/C,QAAM,KAAK,YAAY,MAAM,MAAM,GAAG,eAAe,IAAI;AAEzD,SAAO,kBAAC,SAAI,WAAU,WACpB,GAAG,IAAI,UAAU,GAClB,kBAAC,SAAI,WAAU,cACd,kBAAC,kBAAe,GAAG,MAAM,QAAQ,cAA4B,WAAsB,CACpF,CACD;AACD;AAGA,IAAM,OAID,CAAC,EAAE,MAAM,UAAU,KAAK,MAC3B,kBAAC,SAAI,WAAU,cACd,kBAAC,SAAI,WAAU,eACd,kBAAC,SAAI,WAAU,gBAAc,KAAK,eAAe,kBAAC,YAAO,IAAK,KAAK,YAAY,kBAAC,qBAAgB,IAAK,kBAAC,mBAAc,CAAG,GACvH,kBAAC,SAAI,OAAO,EAAE,MAAM,QAAQ,WAAW,EAAE,KACvC,YAAY,kBAAC,aAAU,KAAK,KAAK,gBAAgB,GAClD,kBAAC,OAAE,MAAM,KAAK,UAAU,WAAU,WAAS,KAAK,KAAM,GACrD,KAAK,OAAO,IAAI,WAAS,kBAAC,SAAM,OAAc,KAAK,MAAM,IAAI,CAAE,CACjE,GACA,kBAAC,SAAI,WAAU,UACb,KAAK,WAAW,IAAI,UAAQ,kBAAC,UAAO,MAAY,KAAK,KAAK,IAAI,CAAE,CAClE,CAED,GAEA,kBAAC,SAAI,WAAU,YACd,kBAAC,cAAK,KAAE,KAAK,QAAO,YAAS,IAAI,KAAK,KAAK,UAAU,EAAE,mBAAmB,GAAE,QAAK,KAAK,KAAK,KAAM,GACjG,kBAAC,UAAK,OAAO,EAAE,MAAM,EAAE,GAAG,GAC1B,kBAAC,QAAG,WAAU,aACb,kBAAC,YAAG,kBAAC,OAAE,MAAK,UAAS,SAAS,QAAM,MAAI,CAAI,CAC7C,CACD,CACD;AAGF,IAAM,YAAiD,CAAC,EAAE,IAAI,MAAM;AACnE,QAAM,QAAQ,gBAAgB,KAAK,GAAG;AACtC,SAAO,QAAQ,kBAAC,OAAE,MAAM,sBAAsB,MAAM,CAAC,CAAC,IAAI,WAAU,gBAAc,MAAM,CAAC,CAAE,IAAO;AACnG;AAGA,IAAM,QAA+F,CAAC,EAAE,MAAM,MAC7G,kBAAC,UAAK,WAAU,SAAQ,KAAK,MAAM,IAAI,OAAO,EAAE,iBAAiB,IAAI,MAAM,KAAK,GAAG,KAClF,kBAAC,OAAE,OAAO,EAAE,OAAO,iBAAiB,MAAM,KAAK,EAAE,KAAI,UAAU,MAAM,IAAI,CAAE,CAC5E;AAGD,IAAM,SAAyF,CAAC,EAAE,KAAK,MACtG,kBAAC,OAAE,KAAK,KAAK,IAAI,MAAM,KAAK,YAC3B,kBAAC,SAAI,KAAK,KAAK,YAAY,OAAM,MAAK,QAAO,MAAK,KAAK,IAAI,KAAK,KAAK,IAAI,CAC1E;AAGD,IAAM,iBAA6G,CAAC,EAAE,WAAW,cAAc,GAAAE,GAAE,MAChJ,YACG,kBAAC,UAAK,WAAU,QAAO,SAAS,MAAM,aAAa,KAAK,KAAG,gBAAQA,KAAI,iBAAgB,OAAK,IAC5F,kBAAC,UAAK,WAAU,QAAO,SAAS,MAAM,aAAa,IAAI,KAAG,kBAAW;AAGzE,SAAS,iBAAiB,OAAuB;AAEhD,QAAMC,KAAI,OAAO,SAAS,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE;AAChD,QAAMC,KAAI,OAAO,SAAS,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE;AAChD,QAAMC,KAAI,OAAO,SAAS,MAAM,OAAO,GAAG,CAAC,GAAG,EAAE;AAChD,UAAS,QAAQF,KAAI,QAAQC,KAAI,QAAQC,MAAK,MAAO,MAAM,UAAU;AACtE;;;AChHA;;;ACSO,IAAM,WAA+B,MAAM;AACjD,QAAM,QAAQ,SAAS,cAAc,OAAO;AAC5C,QAAM,OAAO;AACb,QAAM,cAAc;AAEpB,SAAO;AAAA,IACN,iBAAiB,MAAM,SAAS;AAC/B,UAAI,SAAS,QAAQ;AACrB,UAAI,CAAC,QAAQ;AACZ,iBAAS,QAAQ,aAAa,EAAE,MAAM,OAAO,CAAC;AAE9C,eAAO,OAAO,MAAM,UAAU,IAAI,CAAC;AAEnC,cAAM,OAAO,SAAS,cAAc,KAAK;AACzC,aAAK,KAAK;AACV,eAAO,OAAO,IAAI;AAAA,MACnB;AACA,QAAO,kBAAC,YAAS,OAAO,KAAK,KAAK,GAAG,GAAI,OAAO,cAAc,OAAO,CAAE;AAAA,IACxE;AAAA,EACD;AACD;", "names": ["options", "isValidElement", "rerenderQueue", "defer", "prevDebounce", "IS_HYDRATE", "i", "EMPTY_OBJ", "EMPTY_ARR", "IS_NON_DIMENSIONAL", "assign", "obj", "props", "removeNode", "node", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "createElement", "type", "children", "normalizedProps", "arguments", "length", "push", "defaultProps", "createVNode", "key", "ref", "original", "vnode", "undefined", "constructor", "Fragment", "props", "children", "Component", "context", "getDomSibling", "vnode", "childIndex", "indexOf", "sibling", "length", "type", "updateParentDomPointers", "i", "child", "base", "enqueueRender", "c", "rerenderQueue", "push", "process", "prevDebounce", "options", "debounceRendering", "defer", "queue", "sort", "a", "b", "some", "component", "commitQueue", "oldVNode", "newDom", "oldDom", "parentDom", "assign", "diff", "ownerSVGElement", "commitRoot", "diff<PERSON><PERSON><PERSON><PERSON>", "renderResult", "newParentVNode", "oldParentVNode", "globalContext", "isSvg", "excessDomChildren", "isHydrating", "j", "childVNode", "firstChildDom", "refs", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "EMPTY_ARR", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "EMPTY_OBJ", "createVNode", "Array", "isArray", "key", "undefined", "ref", "<PERSON><PERSON><PERSON><PERSON>", "value", "parentNode", "removeNode", "unmount", "applyRef", "<PERSON><PERSON><PERSON><PERSON>", "parentDom", "childVNode", "oldVNode", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "excessDomChildren", "newDom", "oldDom", "nextDom", "sibDom", "j", "parentNode", "outer", "append<PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "length", "insertBefore", "diffProps", "dom", "newProps", "oldProps", "isSvg", "hydrate", "i", "setProperty", "setStyle", "style", "key", "value", "IS_NON_DIMENSIONAL", "test", "name", "oldValue", "s", "useCapture", "nameLower", "cssText", "replace", "toLowerCase", "slice", "addEventListener", "eventProxy", "_listeners", "removeEventListener", "removeAttributeNS", "setAttributeNS", "removeAttribute", "setAttribute", "e", "type", "options", "event", "reorderC<PERSON>dren", "newVNode", "tmp", "vnode", "diff", "globalContext", "commitQueue", "isHydrating", "c", "isNew", "oldState", "snapshot", "clearProcessingException", "provider", "componentContext", "renderResult", "newType", "constructor", "props", "contextType", "prototype", "render", "Component", "doR<PERSON>", "sub", "state", "context", "getDerivedStateFromProps", "assign", "componentWillMount", "componentDidMount", "push", "componentWillReceiveProps", "shouldComponentUpdate", "componentWillUpdate", "componentDidUpdate", "getChildContext", "getSnapshotBeforeUpdate", "Fragment", "children", "diff<PERSON><PERSON><PERSON><PERSON>", "Array", "isArray", "base", "diffElementNodes", "diffed", "commitRoot", "root", "some", "cb", "call", "child", "oldHtml", "newHtml", "nodeType", "localName", "document", "createTextNode", "createElementNS", "createElement", "is", "data", "EMPTY_ARR", "childNodes", "EMPTY_OBJ", "dangerouslySetInnerHTML", "attributes", "innerHTML", "checked", "applyRef", "ref", "current", "unmount", "parentVNode", "<PERSON><PERSON><PERSON><PERSON>", "r", "componentWillUnmount", "removeNode", "this", "replaceNode", "IS_HYDRATE", "ownerSVGElement", "options", "error", "vnode", "component", "<PERSON><PERSON><PERSON><PERSON>", "constructor", "getDerivedStateFromError", "setState", "componentDidCatch", "enqueueRender", "e", "isValidElement", "Component", "prototype", "update", "callback", "s", "this", "state", "assign", "props", "push", "forceUpdate", "render", "Fragment", "rerenderQueue", "defer", "Promise", "then", "bind", "resolve", "setTimeout", "process", "IS_HYDRATE", "EMPTY_OBJ", "i", "currentIndex", "currentComponent", "prevRaf", "currentHook", "afterPaintEffects", "oldBeforeRender", "options", "oldAfterDiff", "diffed", "old<PERSON><PERSON><PERSON>", "oldBeforeUnmount", "unmount", "getHookState", "index", "type", "hooks", "length", "push", "useState", "initialState", "useReducer", "invokeOrReturn", "reducer", "init", "hookState", "_reducer", "action", "nextValue", "setState", "useEffect", "callback", "args", "state", "args<PERSON><PERSON><PERSON>", "useMemo", "factory", "args", "state", "getHookState", "currentIndex", "args<PERSON><PERSON><PERSON>", "flushAfterPaintEffects", "afterPaintEffects", "some", "component", "for<PERSON>ach", "invokeCleanup", "invokeEffect", "e", "options", "vnode", "oldBeforeRender", "currentIndex", "hooks", "currentComponent", "diffed", "oldAfterDiff", "c", "length", "push", "prevRaf", "requestAnimationFrame", "callback", "raf", "done", "clearTimeout", "timeout", "HAS_RAF", "cancelAnimationFrame", "setTimeout", "commitQueue", "filter", "cb", "old<PERSON><PERSON><PERSON>", "unmount", "oldBeforeUnmount", "hook", "_cleanup", "args<PERSON><PERSON><PERSON>", "oldArgs", "newArgs", "arg", "index", "invokeOrReturn", "f", "m", "m", "_", "n", "r", "g", "b"]}