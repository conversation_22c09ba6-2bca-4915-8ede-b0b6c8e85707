{% extends "base.html" %}

{% block title %}首页 - Python Web应用{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <!-- 欢迎区域 -->
            <div class="text-center mb-5">
                <h1 class="display-4 mb-4">
                    <i class="fas fa-rocket text-primary"></i>
                    欢迎使用Python Web应用
                </h1>
                <p class="lead">
                    这是一个功能丰富的Python Web应用示例，展示了现代Web开发的各种特性
                </p>
            </div>

            <!-- 功能特性 -->
            <div class="row mb-5">
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-users fa-3x text-primary mb-3"></i>
                            <h5 class="card-title">用户管理</h5>
                            <p class="card-text">完整的用户注册、登录、会话管理系统</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-upload fa-3x text-success mb-3"></i>
                            <h5 class="card-title">文件上传</h5>
                            <p class="card-text">安全的文件上传和管理功能</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-comments fa-3x text-info mb-3"></i>
                            <h5 class="card-title">实时聊天</h5>
                            <p class="card-text">基于WebSocket的实时聊天功能</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-6 mb-4">
                    <div class="card h-100">
                        <div class="card-body text-center">
                            <i class="fas fa-chart-bar fa-3x text-warning mb-3"></i>
                            <h5 class="card-title">数据统计</h5>
                            <p class="card-text">实时数据统计和可视化图表</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 技术栈 -->
            <div class="card mb-5">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-code"></i> 技术栈
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>后端技术</h6>
                            <ul class="list-unstyled">
                                <li><i class="fab fa-python text-primary"></i> Python 3.x</li>
                                <li><i class="fas fa-flask text-success"></i> Flask Web框架</li>
                                <li><i class="fas fa-plug text-info"></i> Flask-SocketIO</li>
                                <li><i class="fas fa-database text-warning"></i> SQLite数据库</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6>前端技术</h6>
                            <ul class="list-unstyled">
                                <li><i class="fab fa-html5 text-danger"></i> HTML5</li>
                                <li><i class="fab fa-css3-alt text-primary"></i> CSS3 + Bootstrap 5</li>
                                <li><i class="fab fa-js-square text-warning"></i> JavaScript</li>
                                <li><i class="fas fa-chart-line text-success"></i> Chart.js</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 快速开始 -->
            <div class="text-center">
                <h3 class="mb-4">快速开始</h3>
                <div class="d-grid gap-2 d-md-block">
                    <a href="{{ url_for('register') }}" class="btn btn-primary btn-lg">
                        <i class="fas fa-user-plus"></i> 注册账户
                    </a>
                    <a href="{{ url_for('login') }}" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-sign-in-alt"></i> 立即登录
                    </a>
                </div>
                <p class="mt-3 text-muted">
                    注册后即可体验所有功能
                </p>
            </div>

            <!-- 实时状态 -->
            <div class="card mt-5">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-heartbeat text-danger"></i> 系统状态
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="mb-2">
                                <i class="fas fa-server fa-2x text-success"></i>
                            </div>
                            <h6>服务器</h6>
                            <span class="badge bg-success">运行中</span>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <i class="fas fa-database fa-2x text-primary"></i>
                            </div>
                            <h6>数据库</h6>
                            <span class="badge bg-success">正常</span>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <i class="fas fa-wifi fa-2x text-info"></i>
                            </div>
                            <h6>WebSocket</h6>
                            <span class="badge bg-success">连接</span>
                        </div>
                        <div class="col-md-3">
                            <div class="mb-2">
                                <i class="fas fa-clock fa-2x text-warning"></i>
                            </div>
                            <h6>运行时间</h6>
                            <span class="timestamp badge bg-info">--:--:--</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 背景动画 -->
<style>
    body {
        background: linear-gradient(-45deg, #ee7752, #e73c7e, #23a6d5, #23d5ab);
        background-size: 400% 400%;
        animation: gradientBG 15s ease infinite;
        min-height: 100vh;
    }

    @keyframes gradientBG {
        0% { background-position: 0% 50%; }
        50% { background-position: 100% 50%; }
        100% { background-position: 0% 50%; }
    }

    .card {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        border: none;
        box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
    }

    .navbar {
        background: rgba(0, 123, 255, 0.9) !important;
        backdrop-filter: blur(10px);
    }
</style>
{% endblock %}
