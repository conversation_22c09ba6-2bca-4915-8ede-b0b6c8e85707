[{"eventPrefix": "typescript-language-features/", "sourceDirs": ["../../s/extensions/typescript-language-features"], "excludedDirs": [], "applyEndpoints": true}, {"eventPrefix": "git/", "sourceDirs": ["../../s/extensions/git"], "excludedDirs": [], "applyEndpoints": true}, {"eventPrefix": "extension-telemetry/", "sourceDirs": ["vscode-extension-telemetry"], "excludedDirs": [], "applyEndpoints": true}, {"eventPrefix": "vscode-markdown/", "sourceDirs": ["../../s/extensions/markdown-language-features"], "excludedDirs": [], "applyEndpoints": true}, {"eventPrefix": "html-language-features/", "sourceDirs": ["../../s/extensions/html-language-features", "vscode-html-languageservice"], "excludedDirs": [], "applyEndpoints": true}, {"eventPrefix": "json-language-features/", "sourceDirs": ["../../s/extensions/json-language-features", "vscode-json-languageservice"], "excludedDirs": [], "applyEndpoints": true}, {"eventPrefix": "ms-vscode.node/", "sourceDirs": ["vscode-chrome-debug-core", "vscode-node-debug"], "excludedDirs": [], "applyEndpoints": true, "patchDebugEvents": true}]