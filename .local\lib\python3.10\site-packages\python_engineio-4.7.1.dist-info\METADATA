Metadata-Version: 2.1
Name: python-engineio
Version: 4.7.1
Summary: Engine.IO server and client for Python
Home-page: https://github.com/miguelgrinberg/python-engineio
Author: <PERSON>
Author-email: <EMAIL>
Project-URL: <PERSON><PERSON> Tracker, https://github.com/miguelgrinberg/python-engineio/issues
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python :: 3
Classifier: License :: OSI Approved :: MIT License
Classifier: Operating System :: OS Independent
Requires-Python: >=3.6
Description-Content-Type: text/markdown
License-File: LICENSE
Requires-Dist: simple-websocket >=0.10.0
Provides-Extra: asyncio_client
Requires-Dist: aiohttp >=3.4 ; extra == 'asyncio_client'
Provides-Extra: client
Requires-Dist: requests >=2.21.0 ; extra == 'client'
Requires-Dist: websocket-client >=0.54.0 ; extra == 'client'
Provides-Extra: docs
Requires-Dist: sphinx ; extra == 'docs'

python-engineio
===============

[![Build status](https://github.com/miguelgrinberg/python-engineio/workflows/build/badge.svg)](https://github.com/miguelgrinberg/python-engineio/actions) [![codecov](https://codecov.io/gh/miguelgrinberg/python-engineio/branch/main/graph/badge.svg)](https://codecov.io/gh/miguelgrinberg/python-engineio)

Python implementation of the `Engine.IO`_ realtime client and server.

Sponsors
--------

The following organizations are funding this project:

![Socket.IO](https://images.opencollective.com/socketio/050e5eb/logo/64.png)<br>[Socket.IO](https://socket.io)  | [Add your company here!](https://github.com/sponsors/miguelgrinberg)|
-|-

Many individual sponsors also support this project through small ongoing contributions. Why not [join them](https://github.com/sponsors/miguelgrinberg)?

Resources
---------

-  [Documentation](https://python-engineio.readthedocs.io/en/latest/)
-  [PyPI](https://pypi.python.org/pypi/python-engineio)
-  [Change Log](https://github.com/miguelgrinberg/python-engineio/blob/main/CHANGES.md)
-  Questions? See the [questions](https://stackoverflow.com/questions/tagged/python-socketio) others have asked on Stack Overflow, or [ask](https://stackoverflow.com/questions/ask?tags=python+python-socketio) your own question.
