#!/usr/bin/env python3
"""
VS Code Server Python测试文件
"""

import sys
import os
from datetime import datetime

def main():
    print("=" * 50)
    print("Python测试程序")
    print("=" * 50)
    print(f"执行时间: {datetime.now()}")
    print(f"Python版本: {sys.version}")
    print(f"当前目录: {os.getcwd()}")
    print(f"脚本路径: {__file__}")

    # 测试基本功能
    numbers = [1, 2, 3, 4, 5]
    result = sum(numbers)
    print(f"数字求和: {numbers} = {result}")

    # 测试文件操作
    test_file = "test_output.txt"
    with open(test_file, 'w', encoding='utf-8') as f:
        f.write(f"测试输出 - {datetime.now()}\n")
        f.write("Python在VS Code Server中运行正常！\n")

    print(f"创建测试文件: {test_file}")

    # 读取并显示文件内容
    with open(test_file, 'r', encoding='utf-8') as f:
        content = f.read()
        print(f"文件内容:\n{content}")

    print("=" * 50)
    print("测试完成！")
    print("=" * 50)

if __name__ == "__main__":
    main()
