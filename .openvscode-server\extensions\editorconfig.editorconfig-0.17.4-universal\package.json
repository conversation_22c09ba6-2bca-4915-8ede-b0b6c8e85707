{"name": "EditorConfig", "displayName": "EditorConfig for VS Code", "description": "EditorConfig Support for Visual Studio Code", "publisher": "EditorConfig", "version": "0.17.4", "icon": "EditorConfig_icon.png", "engines": {"vscode": "^1.98.0"}, "author": "EditorConfig Team", "license": "MIT", "categories": ["Other"], "keywords": ["editor", "config", "multi-root ready"], "homepage": "https://github.com/editorconfig/editorconfig-vscode/blob/main/README.md", "repository": {"type": "git", "url": "https://github.com/editorconfig/editorconfig-vscode.git"}, "bugs": {"url": "https://github.com/editorconfig/editorconfig-vscode/issues"}, "galleryBanner": {"color": "#37699A", "theme": "dark"}, "activationEvents": ["onStartupFinished"], "main": "./out/editorConfigMain.js", "types": "./out/editorConfigMain.d.ts", "contributes": {"commands": [{"command": "EditorConfig.generate", "title": "Generate .editorconfig"}], "configuration": {"title": "EditorConfig", "properties": {"editorconfig.generateAuto": {"type": "boolean", "default": true, "description": "Automatically generates an .editorconfig file according to your current editor settings."}, "editorconfig.template": {"type": "string", "default": "default", "description": "If generateAuto is false, this template path will be used for each newly-generated .editorconfig file."}, "editorconfig.showMenuEntry": {"type": "boolean", "default": true, "description": "Show the 'Generate .editorconfig' entry in the context menu of the Explorer view."}}}, "menus": {"commandPalette": [{"command": "EditorConfig.generate", "when": "explorerResourceIsFolder && config.editorconfig.showMenuEntry"}], "explorer/context": [{"command": "EditorConfig.generate", "when": "explorerResourceIsFolder && config.editorconfig.showMenuEntry", "group": "EditorConfig@1"}]}, "languages": [{"id": "editorconfig", "extensions": [".editorconfig"], "aliases": ["EditorConfig", "editorconfig"], "filenames": [], "configuration": "./editorconfig.language-configuration.json"}], "grammars": [{"language": "editorconfig", "scopeName": "source.editorconfig", "path": "./syntaxes/editorconfig.tmLanguage.json"}]}, "capabilities": {"virtualWorkspaces": {"supported": false, "description": "The `editorconfig` dependency requires fs access for parsing the config to use."}, "untrustedWorkspaces": {"supported": true}}, "dependencies": {"editorconfig": "^2.0.1"}, "devDependencies": {"@types/mocha": "^10.0.10", "@types/node": "^20.17.19", "@types/vscode": "^1.90.0", "@typescript-eslint/eslint-plugin": "^8.25.0", "@typescript-eslint/parser": "^8.25.0", "@vscode/test-electron": "^2.4.1", "eslint": "^8.0.0", "eslint-config-prettier": "^10.0.2", "eslint-config-xo": "^0.45.0", "eslint-plugin-prettier": "^5.2.3", "glob": "^11.0.1", "mocha": "^11.1.0", "prettier": "^3.5.2 ", "rimraf": "^6.0.1", "typescript": "~5.7.3", "@vscode/vsce": "^3.2.2", "vscode-test-utils": "^1.0.0"}, "scripts": {"clean": "<PERSON><PERSON><PERSON> out", "prebuild": "npm run clean", "build": "tsc", "postbuild": "cp -r src/test/suite/fixtures out/test/suite && cp -r src/test/untitled-suite/fixtures out/test/untitled-suite && cp src/DefaultTemplate.editorconfig out", "lint": "eslint src/**/*.ts", "pretest": "npm run lint && npm run build", "watch": "tsc -watch", "check-types": "tsc --noEmit", "test": "node out/test/runTest.js", "vscode:prepublish": "npm run build"}, "eslintConfig": {"extends": ["xo", "prettier"], "env": {"jest": true, "node": true}, "rules": {"prettier/prettier": "error"}, "plugins": ["prettier"]}, "prettier": {"arrowParens": "avoid", "proseWrap": "always", "semi": false, "singleQuote": true, "trailingComma": "all"}, "__metadata": {"installedTimestamp": 1753968420739, "targetPlatform": "universal", "size": 659190}}