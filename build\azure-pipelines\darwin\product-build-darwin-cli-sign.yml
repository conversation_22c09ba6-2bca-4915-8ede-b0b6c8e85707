parameters:
  - name: VSCODE_BUILD_MACOS
    type: boolean
  - name: VSCODE_BUILD_MACOS_ARM64
    type: boolean
  - name: VSCODE_QUALITY
    type: string

steps:
  - task: NodeTool@0
    inputs:
      versionSource: fromFile
      versionFilePath: .nvmrc
      nodejsMirror: https://github.com/joaomoreno/node-mirror/releases/download

  - ${{ if ne(parameters.VSCODE_QUALITY, 'oss') }}:
    - task: AzureKeyVault@2
      displayName: "Azure Key Vault: Get Secrets"
      inputs:
        azureSubscription: vscode
        KeyVaultName: vscode-build-secrets
        SecretsFilter: "github-distro-mixin-password"

  - script: node build/setup-npm-registry.js $NPM_REGISTRY build
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Registry

  - script: |
      set -e
      # Set the private NPM registry to the global npmrc file
      # so that authentication works for subfolders like build/, remote/, extensions/ etc
      # which does not have their own .npmrc file
      npm config set registry "$NPM_REGISTRY"
      echo "##vso[task.setvariable variable=NPMRC_PATH]$(npm config get userconfig)"
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM

  - task: npmAuthenticate@0
    inputs:
      workingFile: $(NPMRC_PATH)
    condition: and(succeeded(), ne(variables['NPM_REGISTRY'], 'none'))
    displayName: Setup NPM Authentication

  - script: |
      set -e

      for i in {1..5}; do # try 5 times
        npm ci && break
        if [ $i -eq 5 ]; then
          echo "Npm install failed too many times" >&2
          exit 1
        fi
        echo "Npm install failed $i, trying again..."
      done
    workingDirectory: build
    env:
      GITHUB_TOKEN: "$(github-distro-mixin-password)"
    displayName: Install build dependencies

  - template: ../cli/cli-darwin-sign.yml@self
    parameters:
      VSCODE_CLI_ARTIFACTS:
        - ${{ if eq(parameters.VSCODE_BUILD_MACOS, true) }}:
          - unsigned_vscode_cli_darwin_x64_cli
        - ${{ if eq(parameters.VSCODE_BUILD_MACOS_ARM64, true) }}:
          - unsigned_vscode_cli_darwin_arm64_cli
