{
	"extends": "./tsconfig.browser.json",
	"include": [
		"../../src/**/common/**/*.ts",
		"../../src/**/browser/**/*.ts",
		"../../src/**/electron-browser/**/*.ts",
		"../../src/typings/*.d.ts",
		"../../src/vs/monaco.d.ts",
		"../../src/vscode-dts/vscode.proposed.*.d.ts",
		"../../src/vscode-dts/vscode.d.ts",
		"../../node_modules/@webgpu/types/dist/index.d.ts",
		"../../node_modules/@types/trusted-types/index.d.ts",
		"../../node_modules/@types/wicg-file-system-access/index.d.ts"
	],
	"exclude": [
		"../../src/**/test/**",
		"../../src/**/fixtures/**",
		"../../src/vs/base/parts/sandbox/electron-browser/preload.ts", 		// Preload scripts for Electron sandbox
		"../../src/vs/base/parts/sandbox/electron-browser/preload-aux.ts"	// have limited access to node.js APIs
	]
}
