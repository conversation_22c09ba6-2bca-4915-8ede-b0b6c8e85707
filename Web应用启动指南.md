# Python Web应用启动指南

## 🚀 快速启动

### 方法1: 自动安装并启动（推荐）
```bash
python install_and_run.py
```

### 方法2: 手动安装依赖后启动
```bash
# 安装依赖
pip install -r requirements.txt

# 启动应用
python web_app.py
```

### 方法3: 启动简化版（如果依赖安装失败）
```bash
python simple_web_app.py
```

## 📍 访问地址

启动成功后，在浏览器中访问：
- **主地址**: http://localhost:1111
- **或者**: http://127.0.0.1:1111

## 🎯 应用功能

### 完整版功能 (web_app.py)
1. **用户系统**
   - 用户注册/登录
   - 会话管理
   - 用户权限控制

2. **文件管理**
   - 文件上传（支持拖拽）
   - 文件列表查看
   - 文件大小限制：16MB

3. **实时聊天**
   - WebSocket实时通信
   - 多用户聊天
   - 表情支持
   - 声音提醒

4. **数据统计**
   - 用户活动统计
   - 文件上传统计
   - 图表可视化

5. **系统监控**
   - 系统信息查看
   - 性能监控
   - 活动日志

6. **API接口**
   - RESTful API
   - JSON数据返回
   - 实时数据更新

### 简化版功能 (simple_web_app.py)
- 基本Web页面
- 系统信息显示
- API测试接口
- 响应式设计

## 🛠️ 技术栈

### 后端
- **Python 3.x**: 主要编程语言
- **Flask**: Web框架
- **Flask-SocketIO**: WebSocket支持
- **SQLite**: 数据库
- **Werkzeug**: WSGI工具库

### 前端
- **HTML5/CSS3**: 页面结构和样式
- **Bootstrap 5**: UI框架
- **JavaScript**: 交互逻辑
- **Chart.js**: 图表库
- **Socket.IO**: 实时通信
- **Font Awesome**: 图标库

## 📁 文件结构

```
├── web_app.py              # 主应用文件
├── install_and_run.py      # 自动安装启动脚本
├── simple_web_app.py       # 简化版应用（自动生成）
├── requirements.txt        # 依赖列表
├── templates/              # HTML模板
│   ├── base.html          # 基础模板
│   ├── index.html         # 首页
│   ├── login.html         # 登录页
│   ├── register.html      # 注册页
│   ├── dashboard.html     # 仪表板
│   ├── upload.html        # 文件上传
│   └── chat.html          # 聊天室
├── uploads/               # 上传文件目录（自动创建）
├── static/                # 静态文件目录（自动创建）
└── webapp.db              # SQLite数据库（自动创建）
```

## 🔧 环境要求

### 最低要求
- Python 3.6+
- 网络连接（用于下载依赖）

### 推荐配置
- Python 3.8+
- 2GB+ RAM
- 现代浏览器（Chrome, Firefox, Safari, Edge）

## 🚨 故障排除

### 问题1: `python: command not found`
**解决方案**:
```bash
# 尝试使用python3
python3 install_and_run.py

# 或者创建别名
alias python=python3
```

### 问题2: 依赖安装失败
**解决方案**:
```bash
# 升级pip
python -m pip install --upgrade pip

# 使用国内镜像
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

### 问题3: 端口1111被占用
**解决方案**:
- 修改 `web_app.py` 最后一行的端口号
- 或者停止占用端口的程序

### 问题4: 无法访问网页
**检查项目**:
1. 确认应用已启动且无错误
2. 检查防火墙设置
3. 尝试使用 127.0.0.1:1111 而不是 localhost:1111

## 📊 使用说明

### 1. 首次使用
1. 启动应用
2. 访问 http://localhost:1111
3. 点击"注册账户"创建用户
4. 登录后即可使用所有功能

### 2. 文件上传
1. 登录后点击"文件上传"
2. 拖拽文件到上传区域或点击选择
3. 支持所有文件类型，最大16MB

### 3. 实时聊天
1. 点击"聊天室"进入
2. 输入消息后按回车或点击发送
3. 支持表情和声音提醒

### 4. 数据统计
1. 在仪表板查看个人统计
2. 点击"API统计"查看系统数据
3. 图表会自动更新

## 🔒 安全特性

- 密码哈希存储
- 文件名安全处理
- SQL注入防护
- XSS攻击防护
- 文件大小限制
- 用户会话管理

## 🌐 部署建议

### 开发环境
- 使用内置开发服务器
- 启用调试模式
- 本地数据库

### 生产环境
- 使用Gunicorn或uWSGI
- 配置反向代理（Nginx）
- 使用PostgreSQL或MySQL
- 启用HTTPS
- 配置日志记录

## 📞 技术支持

如果遇到问题：
1. 查看控制台错误信息
2. 检查浏览器开发者工具
3. 确认Python和依赖版本
4. 检查网络连接

## 🎉 开始使用

现在您可以运行以下命令启动应用：

```bash
python install_and_run.py
```

然后在浏览器中访问 http://localhost:1111 开始体验！
