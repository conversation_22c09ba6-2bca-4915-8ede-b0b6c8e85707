{"name": "vscode-selfhost-test-provider", "displayName": "VS Code Selfhost Test Provider", "description": "Test provider for the VS Code project", "enabledApiProposals": ["testObserver", "testRelatedCode"], "engines": {"vscode": "^1.88.0"}, "contributes": {"commands": [{"command": "selfhost-test-provider.updateSnapshot", "title": "Update Snapshot", "category": "Testing", "icon": "$(merge)"}, {"command": "selfhost-test-provider.openFailureLog", "title": "Open Selfhost Failure Logs", "category": "Testing", "icon": "$(merge)"}], "menus": {"commandPalette": [{"command": "selfhost-test-provider.updateSnapshot", "when": "false"}], "testing/message/context": [{"command": "selfhost-test-provider.updateSnapshot", "group": "inline@1", "when": "testMessage == isSelfhostSnapshotMessage && !testResultOutdated"}], "testing/message/content": [{"command": "selfhost-test-provider.updateSnapshot", "when": "testMessage == isSelfhostSnapshotMessage && !testResultOutdated"}]}}, "icon": "icon.png", "version": "0.4.0", "publisher": "ms-vscode", "categories": ["Other"], "activationEvents": ["workspaceContains:src/vs/loader.js"], "workspaceTrust": {"request": "onDemand", "description": "Trust is required to execute tests in the workspace."}, "main": "./out/extension.js", "prettier": {"printWidth": 100, "singleQuote": true, "tabWidth": 2, "arrowParens": "avoid"}, "repository": {"type": "git", "url": "https://github.com/microsoft/vscode.git"}, "license": "MIT", "scripts": {"compile": "gulp compile-extension:vscode-selfhost-test-provider", "watch": "gulp watch-extension:vscode-selfhost-test-provider", "test": "npx mocha --ui tdd 'out/*.test.js'"}, "devDependencies": {"@types/mocha": "^10.0.6", "@types/node": "22.x"}, "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "ansi-styles": "^5.2.0", "cockatiel": "^3.1.3", "istanbul-to-vscode": "^2.0.1"}}