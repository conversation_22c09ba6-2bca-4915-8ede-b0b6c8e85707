{
	// --- Chat ---
	// "inlineChat.enableV2": true,

	// --- Editor ---
	"editor.insertSpaces": false,
	"editor.experimental.asyncTokenization": true,
	"editor.experimental.asyncTokenizationVerification": true,
	"editor.occurrencesHighlightDelay": 0,
	// "editor.experimental.preferTreeSitter.typescript": true,
	// "editor.experimental.preferTreeSitter.regex": true,
	// "editor.experimental.preferTreeSitter.css": true,

	// --- Language Specific ---
	"[plaintext]": {
		"files.insertFinalNewline": false
	},
	"[typescript]": {
		"editor.defaultFormatter": "vscode.typescript-language-features",
		"editor.formatOnSave": true
	},
	"[javascript]": {
		"editor.defaultFormatter": "vscode.typescript-language-features",
		"editor.formatOnSave": true
	},
	"[rust]": {
		"editor.defaultFormatter": "rust-lang.rust-analyzer",
		"editor.formatOnSave": true
	},
	"[github-issues]": {
		"editor.wordWrap": "on"
	},

	// --- Files ---
	"files.trimTrailingWhitespace": true,
	"files.insertFinalNewline": true,
	"files.exclude": {
		".git": true,
		".build": true,
		".profile-oss": true,
		"**/.DS_Store": true,
		".vscode-test": true,
		"cli/target": true,
		"build/**/*.js.map": true,
		"build/**/*.js": {
			"when": "$(basename).ts"
		}
	},
	"files.associations": {
		"cglicenses.json": "jsonc",
		"*.tst": "typescript"
	},
	"files.readonlyInclude": {
		"**/node_modules/**/*.*": true,
		"**/yarn.lock": true,
		"**/package-lock.json": true,
		"**/Cargo.lock": true,
		"build/**/*.js": true,
		"out/**": true,
		"out-build/**": true,
		"out-vscode/**": true,
		"out-vscode-reh/**": true,
		"extensions/**/dist/**": true,
		"extensions/**/out/**": true,
		"extensions/terminal-suggest/src/completions/upstream/**": true,
		"test/smoke/out/**": true,
		"test/automation/out/**": true,
		"test/integration/browser/out/**": true
	},
	"files.readonlyExclude": {
		"build/builtin/*.js": true,
		"build/monaco/*.js": true,
		"build/npm/*.js": true,
		"build/*.js": true
	},

	// --- Search ---
	"search.exclude": {
		"**/node_modules": true,
		"cli/target/**": true,
		".build/**": true,
		"out/**": true,
		"out-build/**": true,
		"out-vscode/**": true,
		"i18n/**": true,
		"extensions/**/dist/**": true,
		"extensions/**/out/**": true,
		"test/smoke/out/**": true,
		"test/automation/out/**": true,
		"test/integration/browser/out/**": true,
		"src/vs/base/test/common/filters.perf.data.js": true,
		"src/vs/base/test/node/uri.perf.data.txt": true,
		"src/vs/workbench/api/test/browser/extHostDocumentData.test.perf-data.ts": true,
		"src/vs/base/test/node/uri.test.data.txt": true,
		"src/vs/editor/test/node/diffing/fixtures/**": true,
		"build/loader.min": true
	},

	// --- TypeScript ---
	"typescript.tsdk": "node_modules/typescript/lib",
	"typescript.preferences.importModuleSpecifier": "relative",
	"typescript.preferences.quoteStyle": "single",
	"typescript.tsc.autoDetect": "off",
	"typescript.preferences.autoImportFileExcludePatterns": [
		"@xterm/xterm",
		"@xterm/headless",
		"node-pty",
		"vscode-notebook-renderer",
		"src/vs/workbench/workbench.web.main.internal.ts"
	],

	// --- Languages ---
	"json.schemas": [
		{
			"fileMatch": [
				"cgmanifest.json"
			],
			"url": "https://www.schemastore.org/component-detection-manifest.json"
		},
		{
			"fileMatch": [
				"cglicenses.json"
			],
			"url": "./.vscode/cglicenses.schema.json"
		}
	],
	"css.format.spaceAroundSelectorSeparator": true,

	// --- Git ---
	"git.ignoreLimitWarning": true,
	"git.branchProtection": [
		"main",
		"distro",
		"release/*"
	],
	"git.branchProtectionPrompt": "alwaysCommitToNewBranch",
	"git.branchRandomName.enable": true,
	"git.pullBeforeCheckout": true,
	"git.mergeEditor": true,
	"git.diagnosticsCommitHook.enabled": true,
	"git.diagnosticsCommitHook.sources": {
		"*": "error",
		"ts": "warning",
		"eslint": "warning"
	},

	// --- GitHub ---
	"githubPullRequests.experimental.createView": true,
	"githubPullRequests.assignCreated": "${user}",
	"githubPullRequests.defaultMergeMethod": "squash",
	"githubPullRequests.ignoredPullRequestBranches": [
		"main"
	],
	"githubPullRequests.codingAgent.enabled": true,
	"githubPullRequests.codingAgent.uiIntegration": true,

	// --- Testing & Debugging ---
	"testing.autoRun.mode": "rerun",
	"debug.javascript.terminalOptions": {
		"outFiles": [
			"${workspaceFolder}/out/**/*.js",
			"${workspaceFolder}/build/**/*.js"
		]
	},
	"extension-test-runner.debugOptions": {
		"outFiles": [
			"${workspaceFolder}/extensions/*/out/**/*.js"
		]
	},

	// --- Coverage ---
	"lcov.path": [
		"./.build/coverage/lcov.info",
		"./.build/coverage-single/lcov.info"
	],
	"lcov.watch": [
		{
			"pattern": "**/*.test.js",
			"command": "${workspaceFolder}/scripts/test.sh --coverage --run ${file}",
			"windows": {
				"command": "${workspaceFolder}\\scripts\\test.bat --coverage --run ${file}"
			}
		}
	],

	// --- Tools ---
	"npm.exclude": "**/extensions/**",
	"eslint.useFlatConfig": true,
	"emmet.excludeLanguages": [],
	"gulp.autoDetect": "off",
	"rust-analyzer.linkedProjects": [
		"cli/Cargo.toml"
	],
	"conventionalCommits.scopes": [
		"tree",
		"scm",
		"grid",
		"splitview",
		"table",
		"list",
		"git",
		"sash"
	],

	// --- Workbench ---
	"remote.extensionKind": {
		"msjsdiag.debugger-for-chrome": "workspace"
	},
	"terminal.integrated.suggest.enabled": true,
	"application.experimental.rendererProfiling": true,

	"editor.aiStats.enabled": true, // Team selfhosting on ai stats
	"chat.checkpoints.showFileChanges": true,

	// --- Python ---
	"python.defaultInterpreterPath": "C:\\Python313\\python.exe",
	"python.terminal.activateEnvironment": true,
	"python.linting.enabled": true,
	"python.linting.pylintEnabled": false,
	"python.linting.flake8Enabled": false,
	"python.analysis.autoImportCompletions": true,
	"python.analysis.typeCheckingMode": "basic"
}
