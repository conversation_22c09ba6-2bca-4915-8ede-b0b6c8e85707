<?xml version="1.0" encoding="utf-8"?>
	<PackageManifest Version="2.0.0" xmlns="http://schemas.microsoft.com/developer/vsx-schema/2011" xmlns:d="http://schemas.microsoft.com/developer/vsx-schema-design/2011">
		<Metadata>
			<Identity Language="en-US" Id="vscode-pull-request-github" Version="0.114.3" Publisher="GitHub" />
			<DisplayName>GitHub Pull Requests</DisplayName>
			<Description xml:space="preserve">Pull Request and Issue Provider for GitHub</Description>
			<Tags>keybindings,chat-participant,tools,language-model-tools,__web_extension</Tags>
			<Categories>Other,AI,Chat</Categories>
			<GalleryFlags>Public</GalleryFlags>
			
			<Properties>
				<Property Id="Microsoft.VisualStudio.Code.Engine" Value="^1.102.0" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionDependencies" Value="vscode.github-authentication" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionPack" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.ExtensionKind" Value="workspace,web" />
				<Property Id="Microsoft.VisualStudio.Code.LocalizedLanguages" Value="" />
				<Property Id="Microsoft.VisualStudio.Code.EnabledApiProposals" Value="activeComment,chatParticipantAdditions,chatParticipantPrivate,codiconDecoration,codeActionRanges,commentingRangeHint,commentReactor,commentReveal,commentThreadApplicability,contribAccessibilityHelpContent,contribCommentEditorActionsMenu,contribCommentPeekContext,contribCommentThreadAdditionalMenu,contribCommentsViewThreadMenus,contribEditorContentMenu,contribShareMenu,diffCommand,quickDiffProvider,remoteCodingAgents,shareProvider,tokenInformation,treeViewMarkdownMessage" />
				
				<Property Id="Microsoft.VisualStudio.Code.ExecutesCode" Value="true" />
				
				<Property Id="Microsoft.VisualStudio.Services.Links.Source" Value="https://github.com/Microsoft/vscode-pull-request-github.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Getstarted" Value="https://github.com/Microsoft/vscode-pull-request-github.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.GitHub" Value="https://github.com/Microsoft/vscode-pull-request-github.git" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Support" Value="https://github.com/Microsoft/vscode-pull-request-github/issues" />
				<Property Id="Microsoft.VisualStudio.Services.Links.Learn" Value="https://github.com/Microsoft/vscode-pull-request-github#readme" />
				
				
				<Property Id="Microsoft.VisualStudio.Services.GitHubFlavoredMarkdown" Value="true" />
				<Property Id="Microsoft.VisualStudio.Services.Content.Pricing" Value="Free"/>

				
				
			</Properties>
			<License>extension/LICENSE.txt</License>
			<Icon>extension/resources/icons/github_logo.png</Icon>
		</Metadata>
		<Installation>
			<InstallationTarget Id="Microsoft.VisualStudio.Code"/>
		</Installation>
		<Dependencies/>
		<Assets>
			<Asset Type="Microsoft.VisualStudio.Code.Manifest" Path="extension/package.json" Addressable="true" />
			<Asset Type="Microsoft.VisualStudio.Services.Content.Details" Path="extension/readme.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.Changelog" Path="extension/changelog.md" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Content.License" Path="extension/LICENSE.txt" Addressable="true" />
<Asset Type="Microsoft.VisualStudio.Services.Icons.Default" Path="extension/resources/icons/github_logo.png" Addressable="true" />
		</Assets>
	</PackageManifest>